# Liberty Pay POS Transaction Types Legend

**Last Updated:** July 2025  
**Version:** 1.0  
**Maintainer:** Development Team

## Overview

This document serves as a comprehensive reference for all transaction types used in the Liberty Pay POS system. It provides clear descriptions, categorizations, and examples to help developers, support staff, and stakeholders understand the various transaction types and their purposes.

## Table of Contents

1. [Transaction Categories](#transaction-categories)
2. [Transaction Status Codes](#transaction-status-codes)
3. [Transaction Modes](#transaction-modes)
4. [Wallet Types](#wallet-types)
5. [Debit Transactions](#debit-transactions)
6. [Credit Transactions](#credit-transactions)
7. [Bill Payment Types](#bill-payment-types)
8. [Service Providers](#service-providers)
9. [User Types](#user-types)
10. [Maintenance Guidelines](#maintenance-guidelines)

---

## Transaction Categories

The system categorizes transactions into four main types:

| Category | Description | Purpose |
|----------|-------------|---------|
| `DEBIT` | Standard debit transactions | Regular outgoing transactions from user accounts |
| `CREDIT` | Standard credit transactions | Regular incoming transactions to user accounts |
| `OTHER_DEBIT` | Special debit transactions | System-specific or administrative debit transactions |
| `OTHER_CREDIT` | Special credit transactions | System-specific or administrative credit transactions |

---

## Transaction Status Codes

| Status | Code | Description |
|--------|------|-------------|
| Successful | `SUCCESSFUL` | Transaction completed successfully |
| Pending | `PENDING` | Transaction is awaiting processing |
| In Progress | `IN_PROGRESS` | Transaction is currently being processed |
| Failed | `FAILED` | Transaction failed to complete |
| Reversed | `REVERSED` | Transaction was reversed/cancelled |
| Ignore History | `IGNORE_HISTORY` | Transaction should be ignored in history |

---

## Transaction Modes

| Mode | Code | Description |
|------|------|-------------|
| USSD | `USSD` | Transaction via USSD channel |
| Send Money Online | `SEND_MONEY_ONLINE` | Online money transfer |
| Send Money Offline | `SEND_MONEY_OFFLINE` | Offline money transfer |
| Card Withdraw Online | `CARD_WITHDRAW_ONLINE` | Online card withdrawal |
| Card Withdraw Offline | `CARD_WITHDRAW_OFFLINE` | Offline card withdrawal |
| Fund Paystack Online | `FUND_PAYSTACK_ONLINE` | Online funding via Paystack |

---

## Wallet Types

| Type | Code | Description |
|------|------|-------------|
| Spend Wallet | `SPEND` | Primary spending wallet |
| Collection Wallet | `COLLECTION` | Collection/receiving wallet |
| Savings Wallet | `SAVINGS` | Savings account wallet |
| Commissions Wallet | `COMMISSIONS` | Commission earnings wallet |

---

## Debit Transactions

### Money Transfer & Sending

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Send to Buddy | `SEND_BUDDY` | Transfer money to another Liberty Pay user |
| Send to Lotto Wallet | `SEND_LOTTO_WALLET` | Transfer to lotto agent wallet |
| Send to Ajo Wallet | `SEND_AJO_WALLET` | Transfer to Ajo savings wallet |
| Send to Ajo Loans | `SEND_AJO_LOANS` | Transfer for Ajo loan purposes |
| Send to Collection Account | `SEND_COLLECTION_ACCOUNT` | Transfer to collection account |
| Send Bank Transfer | `SEND_BANK_TRANSFER` | Transfer to external bank account |
| Send Liberty Commission | `SEND_LIBERTY_COMMISSION` | Commission payment to Liberty |

### Bill Payments & Services

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Bills and Payment | `BILLS_AND_PAYMENT` | General bill payment transactions |
| Airtime PIN | `AIRTIME_PIN` | Airtime/data purchase transactions |
| Lotto Play | `LOTTO_PLAY` | Lottery game participation |
| Liberty Life Payment | `LIBERTY_LIFE_PAYMENT` | Insurance premium payments |

### Card & Terminal Transactions

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Terminal Purchase | `TERMINAL_PURCHASE` | Purchase via POS terminal |
| Card Purchase | `CARD_PURCHASE` | Purchase using card |
| USSD Withdraw | `USSD_WITHDRAW` | Cash withdrawal via USSD |

### Loans & Credits

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Credi Loan | `CREDI_LOAN` | Loan disbursement/repayment |
| Ajo Loan Commissions | `AJO_LOAN_COMMISSIONS` | Commission for Ajo loans |
| Assured Loan Commissions | `ASSURED_LOAN_COMMISSIONS` | Commission for assured loans |

### System & Administrative

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Electronic Transfer Levy | `ELECTRONIC_TRANSFER_LEVY` | Government levy on transfers |
| Auto Refund | `AUTO_REFUND` | Automated refund processing |
| Funds Retrieval | `FUNDS_RETRIEVAL` | System fund retrieval |
| Savings | `SAVINGS` | Savings account transactions |
| Retail Auto Debit | `RETAIL_AUTO_DEBIT` | Automated retail deductions |

---

## Credit Transactions

### Funding & Receiving

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Fund Buddy | `FUND_BUDDY` | Receive money from another user |
| Fund Lotto Wallet | `FUND_LOTTO_WALLET` | Receive funds in lotto wallet |
| Fund Ajo Wallet | `FUND_AJO_WALLET` | Receive funds in Ajo wallet |
| Fund Ajo Loans | `FUND_AJO_LOANS` | Receive loan funds |
| Fund Collection Account | `FUND_COLLECTION_ACCOUNT` | Receive funds in collection account |
| Fund Bank Transfer | `FUND_BANK_TRANSFER` | Receive bank transfer |
| Fund Paystack | `FUND_PAYSTACK` | Receive funds via Paystack |
| Fund by USSD | `FUND_BY_USSD` | Receive funds via USSD |
| Fund QR Code | `FUND_QRCODE` | Receive funds via QR code |

### Card Transactions

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Card Transaction Fund | `CARD_TRANSACTION_FUND` | Receive funds from card transaction |
| Card Transaction Fund Transfer | `CARD_TRANSACTION_FUND_TRANSFER` | Card-based fund transfer |

### Reversals & Refunds

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Reversal Buddy | `REVERSAL_BUDDY` | Reverse buddy transfer |
| Reversal Bank Transfer | `REVERSAL_BANK_TRANSFER` | Reverse bank transfer |
| Reversal Bank Transfer In | `REVERSAL_BANK_TRANSFER_IN` | Incoming bank transfer reversal |
| Bills and Payment Reversal | `BILLS_AND_PAYMENT_REVERSAL` | Reverse bill payment |
| Airtime PIN Reversal | `AIRTIME_PIN_REVERSAL` | Reverse airtime purchase |
| Refund Card Purchase | `REFUND_CARD_PURCHASE` | Refund card purchase |

### Commission & Rewards

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Fund Transfer from Commission | `FUND_TRANSFER_FROM_COMMISSION` | Transfer from commission wallet |
| Fund Transfer from Other Commission | `FUND_TRANSFER_FROM_OTHER_COMMISSION` | Transfer from other commission sources |
| Lotto Winning Credit | `LOTTO_WINNING_CREDIT` | Lottery winnings credit |

---

## Bill Payment Types

| Type | Code | Description |
|------|------|-------------|
| Data Bundle | `DATA_BUNDLE` | Mobile data purchases |
| Electricity | `ELECTRICITY` | Electricity bill payments |
| Subscription | `SUBSCRIPTION` | Various subscription services |
| Betting | `BETTING` | Sports betting and gaming |
| Cable TV | `CABLE_TV` | Cable television subscriptions |
| VTU | `VTU` | Virtual Top-Up services |
| Airtime Print | `AIRTIME_PRINT` | Physical airtime vouchers |
| Other | `OTHER` | Miscellaneous bill payments |

---

## Service Providers

### Bill Payment Providers

| Provider | Code | Description |
|----------|------|-------------|
| Coral Pay | `CORALPAY` | Primary bill payment provider |
| Red Biller | `REDBILLER` | Secondary bill payment provider |

### Account Providers

| Provider | Code | Description |
|----------|------|-------------|
| VFD Bank | `VFD` | VFD Microfinance Bank |
| Woven Finance | `WOVEN` | Woven Finance provider |
| Wema Bank | `WEMA` | Wema Bank provider |
| Fidelity Bank | `FIDELITY` | Fidelity Bank provider |

### Network Providers (Airtime/Data)

| Provider | Code | Description |
|----------|------|-------------|
| MTN Nigeria | `MTN_NIGERIA` | MTN network services |
| Airtel Nigeria | `AIRTEL_NIGERIA` | Airtel network services |
| Glo Nigeria | `GLO_NIGERIA` | Globacom network services |
| 9Mobile | `9_MOBILE` | 9Mobile network services |

---

## User Types

| Type | Code | Description |
|------|------|-------------|
| Merchant | `MERCHANT` | Business merchant users |
| Agent | `AGENT` | Liberty Pay agents |
| Personal | `PERSONAL` | Individual personal users |
| Lotto Agent | `LOTTO_AGENT` | Lottery service agents |
| Liberty Retail | `LIBERTY_RETAIL` | Liberty retail outlets |
| Ajo Agent | `AJO_AGENT` | Ajo savings agents |
| Merchant Agent | `MERCHANT_AGENT` | Combined merchant-agent |
| Staff Agent | `STAFF_AGENT` | Staff member agents |
| Prosper Agent | `PROSPER_AGENT` | Prosper loan agents |
| DMO Agent | `DMO_AGENT` | Deposit Mobilization Officers |
| Micro Saver | `MICRO_SAVER` | Micro savings users |
| Pharmacist | `PHARMACIST` | Pharmacy service providers |

---

## Maintenance Guidelines

### Updating This Document

1. **When to Update:**
   - New transaction types are added to the system
   - Existing transaction types are modified or deprecated
   - New service providers are integrated
   - Status codes or categories change

2. **How to Update:**
   - Update the relevant sections with new information
   - Maintain consistent formatting and structure
   - Update the "Last Updated" date at the top
   - Increment the version number for major changes

3. **Validation:**
   - Cross-reference with `accounts/models.py` Transaction model
   - Check `main/model_choices.py` for any new choices
   - Verify against `liberty_pay/settings.py` transaction choices
   - Test with actual transaction data when possible

### Code References

Key files to monitor for changes:
- `accounts/models.py` - Main Transaction model and choices
- `main/model_choices.py` - Model choice definitions
- `liberty_pay/settings.py` - System-wide transaction settings
- `accounts/model_choices.py` - Account-specific choices

### Contact Information

For questions or updates to this document, contact:
- Development Team
- Chief Technology Officer (CTO)
- Product Management

---

## Special Transaction Categories

### Other Debit Transactions

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Send Back to Float Transfer | `SEND_BACK_TO_FLOAT_TRANSFER` | Return funds to float account |
| Lotto Winning Debit | `LOTTO_WINNING_DEBIT` | Debit for lottery winnings |
| Send Refund Card Purchase | `SEND_REFUND_CARD_PURCHASE` | Send refund for card purchase |

### Other Credit Transactions

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Float Inflow | `FLOAT_INFLOW` | Inflow to system float account |
| Release Hold Balance | `RELEASE_HOLD_BALANCE` | Release held funds |
| Lotto Play Credit | `LOTTO_PLAY_CR` | Credit version of lotto play |
| Liberty Life Payment Credit | `LIBERTY_LIFE_PAYMENT_CR` | Credit version of insurance payment |
| Credi Loan Credit | `CREDI_LOAN_CR` | Credit version of loan transaction |
| Auto Refund Credit | `AUTO_REFUND_CR` | Credit version of auto refund |
| Funds Retrieval Credit | `FUNDS_RETRIEVAL_CR` | Credit version of funds retrieval |
| Savings Credit | `SAVINGS_CR` | Credit version of savings transaction |

### Bank Out-of-Band Transactions

| Transaction Type | Code | Description |
|------------------|------|-------------|
| Bank OOB In | `BANK_OOB_IN` | Bank out-of-band incoming transaction |
| Bank OOB Out | `BANK_OOB_OUT` | Bank out-of-band outgoing transaction |
| Bank OOB Commission | `BANK_OOB_COMM` | Bank out-of-band commission |
| Re-Failed Transfer In | `RE_FAILED_TRANSFER_IN` | Re-process failed incoming transfer |

---

## Transaction Groupings for Analytics

### Cash-Out Transactions
```
CARD_TRANSACTION_FUND_TRANSFER
CARD_TRANSACTION_FUND
```

### Money Transfer Transactions
```
SEND_BANK_TRANSFER
SEND_BUDDY
FUND_BANK_TRANSFER
```

### Bill Payment Transactions
```
BILLS_AND_PAYMENT
BILLS_AND_PAYMENT_REVERSAL
AIRTIME_PIN
AIRTIME_PIN_REVERSAL
```

### Buddy Transfer Transactions
```
SEND_BUDDY
SEND_LOTTO_WALLET
SEND_AJO_WALLET
SEND_AJO_LOANS
SEND_COLLECTION_ACCOUNT
LOTTO_PLAY
LIBERTY_LIFE_PAYMENT
```

### Funding Transactions
```
FUND_BUDDY
FUND_LOTTO_WALLET
FUND_AJO_WALLET
FUND_AJO_LOANS
FUND_COLLECTION_ACCOUNT
```

### Card Transactions
```
CARD_TRANSACTION_FUND
CARD_TRANSACTION_FUND_TRANSFER
CARD_PURCHASE
REFUND_CARD_PURCHASE
```

### Leaderboard Eligible Transactions
```
SEND_BANK_TRANSFER
CARD_TRANSACTION_FUND
CARD_TRANSACTION_FUND_TRANSFER
FUND_BANK_TRANSFER
```

---

## Commission Types

| Type | Code | Description |
|------|------|-------------|
| Bank Commission | `BANK` | Commission paid via bank |
| Cash Commission | `CASH` | Commission paid in cash |

### Commission Sub-Types

| Sub-Type | Code | Description |
|----------|------|-------------|
| Lotto Commission | `LOTTO_COMMISSION` | Commission from lottery transactions |
| Lottery Winning Commission | `LOTTERY_WINNING_COMMISSION` | Commission from lottery winnings |
| Lottery Play Commission | `LOTTERY_PLAY_COMMISSION` | Commission from lottery plays |
| Super Agent Commission Reward | `SUPER_AGENT_COMMISSION_REWARD` | Reward for super agents |
| Ajo Staff Loan | `AJO_STAFF_LOAN` | Staff loan from Ajo |
| Ajo Prosper Loan | `AJO_PROSPER_LOAN` | Prosper loan from Ajo |

---

## Device Types

| Type | Code | Description |
|------|------|-------------|
| Terminal | `TERMINAL` | POS terminal device |
| Mobile | `MOBILE` | Mobile application |
| Web | `WEB` | Web application |

---

## Charge Band Transaction Types

| Type | Code | Description |
|------|------|-------------|
| Send Money | `SEND_MONEY` | Money transfer charges |
| Card Withdraw Agent | `CARD_WITHDRAW_AGENT` | Agent card withdrawal charges |
| Card Withdraw Merchant | `CARD_WITHDRAW_MERCHANT` | Merchant card withdrawal charges |

---

## Examples and Use Cases

### Example 1: Agent Cash-Out Transaction
```
Transaction Type: CARD_TRANSACTION_FUND_TRANSFER
Status: SUCCESSFUL
Mode: CARD_WITHDRAW_OFFLINE
User Type: AGENT
Description: Agent processes cash withdrawal for customer using card
```

### Example 2: Bill Payment Transaction
```
Transaction Type: BILLS_AND_PAYMENT
Status: SUCCESSFUL
Mode: SEND_MONEY_ONLINE
Bill Type: ELECTRICITY
Provider: CORALPAY
Description: Customer pays electricity bill through agent
```

### Example 3: Money Transfer Transaction
```
Transaction Type: SEND_BANK_TRANSFER
Status: PENDING
Mode: SEND_MONEY_ONLINE
User Type: PERSONAL
Description: Personal user sends money to external bank account
```

---

## Troubleshooting Guide

### Common Transaction Status Issues

| Issue | Possible Causes | Resolution |
|-------|----------------|------------|
| Transaction stuck in PENDING | Network issues, provider downtime | Check provider status, retry if needed |
| Transaction marked as FAILED | Insufficient funds, invalid details | Verify account balance and transaction details |
| Transaction shows REVERSED | Manual reversal, system error | Check reversal reason in transaction logs |

### Transaction Type Validation

When implementing new features:
1. Ensure transaction type exists in `TRANSACTION_TYPE_CHOICES`
2. Verify correct categorization (DEBIT/CREDIT)
3. Check if special handling is required
4. Update this documentation

---

*This document is automatically generated from the Liberty Pay POS codebase and should be kept in sync with system changes.*
