from email import header
from wsgiref import headers
import requests
import json
from flask import Flask, request, abort
from main.helper.logging_utils import log_info


# webhook = "https://webhook.site/e0852ce8-d8f6-4e6a-934d-7a5ae1792edd"

# url = "https://libertyussd.com/api/loandisk_loan"

# data = {
#     "name":"segun oloyede",
#     'chanel_url':'Testing webhook'
# }

# r = requests.post(webhook, data=json.dumps(data), headers= {'Content-Type':'application/json'})

app = Flask(__name__)

@app.route('/webhook', methods=['POST'])
def webhook():
    if request.method == 'POST':
        log_info(str(request.json))
        return 'success', 200
    else:
        abort(400)

if __name__ == '__main__':
    app.run()

