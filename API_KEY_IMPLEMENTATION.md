# API Key Authentication System Implementation

This document describes the complete API key authentication system implemented for the Django REST Framework project.

## Overview

The API key authentication system provides secure, scope-based access control with rate limiting and usage tracking. It integrates seamlessly with existing JWT and token authentication mechanisms.

## Components Implemented

### 1. Database Model (`main/models.py`)

**APIKey Model** with the following features:
- **key_id**: Public identifier (format: `ak_xxxxx`)
- **key_hash**: SHA256 hash of the secret key for secure storage
- **name**: Human-readable name for the API key
- **user**: Foreign key to User model
- **is_active**: Boolean flag for enabling/disabling keys
- **scopes**: JSON field for permission scopes
- **last_used**: Timestamp of last usage
- **usage_count**: Number of times the key has been used
- **expires_at**: Optional expiration date
- **rate_limit_requests**: Maximum requests per window
- **rate_limit_window**: Rate limit window in seconds

**Key Methods**:
- `generate_key()`: Creates new API key with format `ak_xxxxx:secret`
- `verify_key()`: Validates provided secret against stored hash
- `is_expired()`: Checks if key has expired
- `has_scope()`: Validates required permissions
- `check_rate_limit()`: Enforces rate limiting
- `update_usage()`: Updates usage statistics

### 2. Authentication Class (`accounts/authentication.py`)

**APIKeyAuthentication** class extending `BaseAuthentication`:
- Header format: `Authorization: ApiKey ak_xxxxx:secret_key`
- Validates key format and existence
- Checks user account status
- Enforces rate limiting using Django cache
- Updates usage statistics on successful authentication
- Returns `(user, api_key_object)` tuple

### 3. Permission Classes (`main/permissions.py`)

**RequireAPIKeyScope** permission class:
- Validates API key has required scope
- Allows regular user authentication to bypass scope checks
- Configurable via view attribute: `required_scope = 'transactions:read'`

**Updated CheckDynamicAuthentication**:
- Added support for API key authentication
- Maintains compatibility with existing JWT, Basic, and Token auth
- Stores API key object in request for scope checking

### 4. Management Command (`main/management/commands/create_apikey.py`)

**create_apikey** command with options:
- `--user`: Email or username of the user
- `--name`: Human-readable name for the API key
- `--scopes`: Comma-separated list of scopes
- `--expires-in-days`: Number of days until expiration
- `--rate-limit`: Maximum requests per window (default: 1000)
- `--rate-window`: Rate limit window in seconds (default: 3600)
- `--output-format`: Output format (text/json)

### 5. Settings Integration (`liberty_pay/settings.py`)

Updated `DEFAULT_AUTHENTICATION_CLASSES` to include:
```python
"DEFAULT_AUTHENTICATION_CLASSES": (
    "rest_framework_simplejwt.authentication.JWTAuthentication",
    "accounts.authentication.APIKeyAuthentication",
    "accounts.authentication.CustomTokenAuthentication",
),
```

## Usage Examples

### Creating API Keys

```bash
# Basic API key creation
python manage.py create_apikey --user <EMAIL> --name "Mobile App API Key"

# API key with scopes and expiration
python manage.py create_apikey \
    --user <EMAIL> \
    --name "Analytics Service" \
    --scopes "transactions:read,users:read" \
    --expires-in-days 90 \
    --rate-limit 5000 \
    --rate-window 3600

# JSON output for automation
python manage.py create_apikey \
    --user <EMAIL> \
    --name "Integration Key" \
    --output-format json
```

### Using API Keys

```bash
# HTTP request with API key
curl -H "Authorization: ApiKey ak_1234567890abcdef:secret_key_here" \
     https://your-api.com/api/endpoint
```

### View Implementation with Scope Requirements

```python
from rest_framework.views import APIView
from main.permissions import RequireAPIKeyScope

class TransactionListView(APIView):
    permission_classes = [RequireAPIKeyScope]
    required_scope = 'transactions:read'
    
    def get(self, request):
        # Your view logic here
        pass
```

### Dynamic Authentication Usage

```python
from main.permissions import CheckDynamicAuthentication

class FlexibleView(APIView):
    permission_classes = [CheckDynamicAuthentication]
    
    def get(self, request):
        # Works with JWT, API keys, Basic auth, or Token auth
        user = request.user
        api_key = getattr(request, 'api_key', None)
        
        if api_key:
            # Request was authenticated with API key
            if not api_key.has_scope('data:read'):
                return Response({'error': 'Insufficient scope'}, status=403)
        
        # Your view logic here
        pass
```

## Security Features

1. **Secure Storage**: API keys are hashed using SHA256 before storage
2. **Rate Limiting**: Configurable per-key rate limits with Redis/cache backend
3. **Scope-based Permissions**: Fine-grained access control
4. **Expiration Support**: Optional key expiration dates
5. **Usage Tracking**: Monitor key usage patterns
6. **Active/Inactive States**: Easy key management

## Database Migration

After implementing the model, run:
```bash
python manage.py makemigrations main --name add_apikey_model
python manage.py migrate
```

## Integration Notes

- **Backward Compatibility**: Existing authentication methods continue to work
- **Performance**: Uses database indexes for efficient key lookups
- **Caching**: Rate limiting uses Django cache framework
- **Logging**: Comprehensive logging for security monitoring
- **Error Handling**: Proper exception handling with meaningful error messages

## Common Scopes

Suggested scope naming convention:
- `transactions:read` - Read transaction data
- `transactions:write` - Create/modify transactions
- `users:read` - Read user information
- `users:write` - Modify user data
- `accounts:read` - Read account information
- `accounts:write` - Modify account data
- `admin:*` - Administrative access

## Monitoring and Maintenance

1. **Usage Monitoring**: Track `usage_count` and `last_used` fields
2. **Rate Limit Monitoring**: Monitor rate limit violations
3. **Key Rotation**: Regularly rotate API keys for security
4. **Cleanup**: Remove expired or unused keys
5. **Audit Logging**: Log all API key authentication attempts

## Error Responses

The system provides detailed error responses:

```json
{
    "error": True,
    "detail": "insufficient_scope",
    "message": "API key does not have the required scope: transactions:read",
    "required_scope": "transactions:read"
}
```

Rate limit exceeded:
```json
{
    "error": "Rate limit exceeded. Limit: 1000 requests per 3600 seconds. Reset at: 2024-01-01 15:30:00 UTC"
}
```

This implementation provides a robust, secure, and scalable API key authentication system that integrates seamlessly with your existing Django REST Framework application.
