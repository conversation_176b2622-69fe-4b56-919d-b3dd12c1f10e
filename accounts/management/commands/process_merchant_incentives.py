from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
from main.models import User
from accounts.models import MerchantIncentive, Transaction
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


class Command(BaseCommand):
    help = """
    Daily processing of merchant incentives.
    
    This command should be run daily (preferably early morning) to:
    1. Calculate each merchant's total transaction amount from the previous day
    2. Compare against their current target_amount in the incentive table
    3. If previous day's transactions meet or exceed the target:
       - Grant 2 free transactions
       - Increase target_amount by 20% for the next period
    4. Log all results for monitoring
    
    Usage: python manage.py process_merchant_incentives
    """

    def add_arguments(self, parser):
        parser.add_argument(
            '--date',
            type=str,
            help='Process incentives for specific date (YYYY-MM-DD). Defaults to yesterday.',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making any changes to the database.',
        )
        parser.add_argument(
            '--merchant-email',
            type=str,
            help='Process incentives for a specific merchant only.',
        )

    def handle(self, *args, **options):
        """Main command handler"""
        try:
            # Determine the date to process
            if options['date']:
                process_date = datetime.strptime(options['date'], '%Y-%m-%d').date()
            else:
                # Default to yesterday
                process_date = (timezone.now() - timedelta(days=1)).date()

            dry_run = options['dry_run']
            merchant_email = options['merchant_email']

            self.stdout.write(
                self.style.SUCCESS(f"Processing merchant incentives for {process_date}")
            )
            
            if dry_run:
                self.stdout.write(
                    self.style.WARNING("DRY RUN MODE - No changes will be made to the database")
                )

            log_info(f"Starting merchant incentive processing for {process_date}")

            # Get active merchant incentives
            incentives_query = MerchantIncentive.objects.filter(is_active=True).select_related('merchant')
            
            if merchant_email:
                incentives_query = incentives_query.filter(merchant__email=merchant_email)

            active_incentives = incentives_query.all()

            if not active_incentives:
                self.stdout.write(
                    self.style.WARNING("No active merchant incentives found")
                )
                log_warning("No active merchant incentives found")
                return

            self.stdout.write(f"Found {len(active_incentives)} active merchant incentive(s)")

            processed_count = 0
            qualified_count = 0
            error_count = 0

            # Process each merchant incentive
            for incentive in active_incentives:
                try:
                    result = self.process_merchant_incentive(incentive, process_date, dry_run)
                    processed_count += 1
                    
                    if result['qualified']:
                        qualified_count += 1
                        
                    self.stdout.write(
                        f"✓ {incentive.merchant.email}: "
                        f"₦{result['total_amount']:,.2f} "
                        f"(Target: ₦{result['target_amount']:,.2f}) "
                        f"{'QUALIFIED' if result['qualified'] else 'NOT QUALIFIED'}"
                    )
                    
                except Exception as e:
                    error_count += 1
                    error_msg = f"Error processing incentive for {incentive.merchant.email}: {str(e)}"
                    self.stdout.write(self.style.ERROR(error_msg))
                    log_error(error_msg)

            # Summary
            self.stdout.write(
                self.style.SUCCESS(
                    f"\nProcessing complete:\n"
                    f"- Processed: {processed_count}\n"
                    f"- Qualified: {qualified_count}\n"
                    f"- Errors: {error_count}"
                )
            )

            log_info(
                f"Merchant incentive processing completed for {process_date}. "
                f"Processed: {processed_count}, Qualified: {qualified_count}, Errors: {error_count}"
            )

        except Exception as e:
            error_msg = f"Fatal error in merchant incentive processing: {str(e)}"
            self.stdout.write(self.style.ERROR(error_msg))
            log_critical(error_msg)
            raise

    def process_merchant_incentive(self, incentive, process_date, dry_run=False):
        """
        Process a single merchant incentive for the given date.
        
        Args:
            incentive: MerchantIncentive instance
            process_date: Date to process (date object)
            dry_run: If True, don't make database changes
            
        Returns:
            dict: Processing results
        """
        merchant = incentive.merchant
        
        # Calculate total transaction amount for the previous day
        # Only count successful transactions that are not reversed
        start_datetime = timezone.make_aware(
            datetime.combine(process_date, datetime.min.time())
        )
        end_datetime = timezone.make_aware(
            datetime.combine(process_date, datetime.max.time())
        )

        # Get transactions for the merchant on the specified date
        # Include relevant transaction types that should count towards incentives
        relevant_transaction_types = [
            'FUND_BANK_TRANSFER',
            'CARD_TRANSACTION_FUND',
            'CARD_TRANSACTION_FUND_TRANSFER'
        ]

        total_amount = Transaction.objects.filter(
            user=merchant,
            date_created__range=(start_datetime, end_datetime),
            status='SUCCESSFUL',
            is_reversed=False,
            transaction_type__in=relevant_transaction_types
        ).aggregate(
            total=Sum('amount')
        )['total'] or 0

        target_amount = incentive.target_amount
        qualified = total_amount >= target_amount

        result = {
            'merchant_email': merchant.email,
            'total_amount': total_amount,
            'target_amount': target_amount,
            'qualified': qualified,
            'free_transactions_granted': 0,
            'new_target_amount': target_amount
        }

        if qualified and not dry_run:
            # Grant free transactions and increase target
            with transaction.atomic():
                # Grant 2 free transactions
                incentive.grant_free_transactions(2)
                result['free_transactions_granted'] = 2
                
                # Increase target amount by 20%
                incentive.increase_target_amount(20)
                result['new_target_amount'] = incentive.target_amount

                log_info(
                    f"Merchant {merchant.email} qualified for incentive. "
                    f"Amount: ₦{total_amount:,.2f}, Target: ₦{target_amount:,.2f}. "
                    f"Granted 2 free transactions, new target: ₦{incentive.target_amount:,.2f}"
                )

        elif qualified and dry_run:
            # Calculate what would happen in dry run
            result['free_transactions_granted'] = 2
            result['new_target_amount'] = target_amount * 1.2

        return result

