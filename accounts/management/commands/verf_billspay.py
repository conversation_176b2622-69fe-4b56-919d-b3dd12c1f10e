from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q, Count
from main.models import User, ConstantTable
from accounts.models import Transaction, CommissionsRecord, BillsPaymentDumpData, InAppTransactionNotification, BillsPaymentConstant, WalletSystem, DebitCreditRecordOnAccount, AirtimeToPinParent
from accounts.helpers.coral_pay_manager import ServicesVASApp
from datetime import datetime, timedelta
from django.utils import timezone
from liberty_pay.settings import cloud_messaging

import random
import uuid as py_uuid
import json
from main.helper.logging_utils import log_info, log_debug


class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--libref', dest='arg1', type=str, help='Liberty References.')
        parser.add_argument('--type', dest='arg2', type=str, help='Transaction Type.')
        parser.add_argument('--month_num', dest='arg3', type=int, help='Num of Months back.')

    def handle(self, *args, **options):



        """
        This Cron job runs every 12am to update pending transactions of the past 2 months
        """



        vas_trans = ["BILLS_AND_PAYMENT", "AIRTIME_PIN"]



        arg_liberty_references = options.get('arg1')
        arg_transaction_type = options.get('arg2')
        # month_num = options.get('arg3')

        # print(month_num)
        # if month_num and isinstance(month_num, int):
        #     print("got innn")
        #     today = datetime.today()
        #     one_month_ago = today - timedelta(days=30) 

            # all_trxs = Transaction.objects \
            #     .filter(date_created__gte=one_month_ago, transaction_type__in=["BILLS_AND_PAYMENT", "BILLS_AND_PAYMENT_REVERSAL"], status="SUCCESSFUL") \
            #     .annotate(liberty_reference_count=Count('liberty_reference')) \
            #         .filter(liberty_reference_count=1)
            # all_trxs = Transaction.objects \
            #     .filter(date_created__gte=one_month_ago, transaction_type="BILLS_AND_PAYMENT", status="SUCCESSFUL")
            
            # print("&&&&&&&&&", all_trxs, all_trxs.count())
            
            # for main_trx in all_trxs:
            #     print(main_trx.transaction_type)
            #     print(main_trx.escrow_id)

            

            # all_trans_in_list = all_trxs.values_list('transaction_id', flat=True)
            # all_trans_in_list_str = [str(uuid) for uuid in all_trans_in_list]


            # commission_transactions = CommissionsRecord.objects \
            #     .filter(transaction_id__in=all_trans_in_list_str, entry=CommissionsRecord.CREDIT, user__email="<EMAIL>") \
            #         .values_list('transaction_id', flat=True)


            # comm_trans_uuid = [py_uuid.UUID(trx_str) for trx_str in commission_transactions]

            # unique_transactions = all_trxs.exclude(transaction_id__in=comm_trans_uuid)

            # print(type(unique_transactions), unique_transactions, unique_transactions.count())


            # for trx in unique_transactions:
            #     # print(trx.transaction_type)
            #     if trx.transaction_type == "BILLS_AND_PAYMENT":
            #         vas_dump_data = BillsPaymentDumpData.objects.filter(transaction_instance=trx).last()
            #         biller = vas_dump_data.biller
            #         bill_provider  = ConstantTable.default_bills_payment_provider()
            #         amount = trx.amount

            #         get_profits = BillsPaymentConstant.share_commissions_profit(biller=biller, amount=amount, bill_provider=bill_provider)

            #         total_profit = get_profits["total_profit"]
            #         liberty_profit = get_profits["liberty_profit"]
            #         agent_cash_profit = get_profits["agent_cash_profit"]
            #         provider_fee = get_profits["provider_fee"]
    
            #         print("going in")
            #         CommissionsRecord.create_and_top_up_bill_and_pay_commissions(
            #             user = trx.user,
            #             amount = amount,
            #             biller = biller,
            #             transaction_id = trx.transaction_id,
            #             total_profit = total_profit,
            #             liberty_profit = liberty_profit,
            #             agent_cash_profit = agent_cash_profit,
            #             provider_fee = provider_fee,
            #             escrow_id=trx.escrow_id
            #         )                        


        log_info(str(arg_liberty_references))
        if arg_liberty_references and arg_transaction_type:
            arg_list = arg_liberty_references.split(',')
            
            get_pending_vas = Transaction.objects.exclude(status__in=["SUCCESSFUL", "FAILED"]) \
                .filter(transaction_type=arg_transaction_type, liberty_reference__in=arg_list)
    
            log_info(str(get_pending_vas))

            vas_single_type = arg_transaction_type

            if not get_pending_vas:
                return

        else:

            vas_single_type = random.choice(vas_trans)


            five_minutes_ago = timezone.now() - timezone.timedelta(minutes=20)
            sixty_days_ago = timezone.now() - timezone.timedelta(days=120)

            get_pending_vas_qs = Transaction.objects.exclude(status__in=["SUCCESSFUL", "FAILED"]) \
                .filter(transaction_type=vas_single_type, date_created__lte=five_minutes_ago, date_created__gte=sixty_days_ago)
            
            log_info("trx_count: ", get_pending_vas_qs.count())
            
            get_pending_vas = get_pending_vas_qs[:30]


        confirmed_trans = []
        no_charge_trans = []
        paid_back_already_trans = []
        reversed_now_trans = []
        manaul_update_successful = []
        unknown_status = []

        if vas_single_type == "BILLS_AND_PAYMENT":
            for trans in get_pending_vas:

                vas_dump_data = BillsPaymentDumpData.objects.filter(transaction_instance=trans).last()

                # if not vas_dump_data:
                #     vas_dump_data = BillsPaymentDumpData.objects.filter(dump=trans.payload).last()
                #     print(vas_dump_data)
                #     if vas_dump_data:
                #         vas_dump_data.transaction_instance = trans
                #         vas_dump_data.save()
                    

                # if not vas_dump_data:
                #     vas_dump_data = BillsPaymentDumpData.objects.create(
                #         user=trans.user,
                #         trans
                #         transaction_type = "BILLS_AND_PAYMENT",
                #         dump=trans.payload
                #     )
                
                if vas_dump_data:
                    
                    log_info("sent", vas_dump_data.sent)
                    # if vas_dump_data.sent == False and not arg_liberty_references:
                    if vas_dump_data.sent == False:
                        vas_dump_data.sent = True
                        vas_dump_data.save()
                    #     print("Not yet sent::", trans.liberty_reference)
                    #     continue

                    verify_bills = ServicesVASApp.verify_trans_from_coralpay(liberty_reference=trans.liberty_reference)

                    log_debug("-------------------------------------------------------")
                    log_debug("-------------------------------------------------------")
                    log_info(str(verify_bills))
                    log_debug("-------------------------------------------------------")
                    log_debug("-------------------------------------------------------")
                    
                    if isinstance(verify_bills, dict):

                        # if verify_bills.get("error") == False:
                        unique_reference = verify_bills.get("responseData").get("transactionId")
                        provider_status = verify_bills.get("responseData").get("vendStatus")

                        formatted_customer_message = verify_bills.get("responseData").get("customerMessage")
                        
                        user_instance = trans.user
                        amount = trans.amount
                        liberty_commission = trans.liberty_commission

                        vas_dump_data.reverify_data = verify_bills
                        biller = vas_dump_data.biller
                        # bills_type = vas_dump_data.bills_type
                        bill_provider  = ConstantTable.default_bills_payment_provider()


                        wallet_instance = user_instance.wallets.filter(wallet_type="COLLECTION").last()
                        if wallet_instance:
                            wallet_instance = wallet_instance
                        else:
                            wallet_instance = user_instance.wallets.filter(wallet_type="COLLECTION").last()

                        if vas_dump_data.manaul_update_successful == True:
                            trans.balance_after = wallet_instance.available_balance
                            trans.provider_status = provider_status
                            trans.status = "SUCCESSFUL"
                            trans.save()

                            manaul_update_successful.append(trans.id)
                            pass

                        elif provider_status not in ["CONFIRMED", "FAILED"]:
                            vas_dump_data.save()
                            unknown_status.append(trans.id)
                            pass

                        elif provider_status == "CONFIRMED":
                            with transaction.atomic():
                                trans.status = "SUCCESSFUL"

                                trans.unique_reference = unique_reference
                                trans.provider_status = provider_status
                                trans.vas_customer_message = formatted_customer_message
                                trans.save()

                                vas_dump_data.status = "SUCCESSFUL"
                                vas_dump_data.save()

                                confirmed_trans.append(trans.id)

            ##########################################################################################################################################

                                
                                # SEND OUT APP NOTIFICATION
                                not_token=trans.user.firebase_key
                                not_title="Transaction Successful"
                                not_body=f"You have successfully performed a BILLS and PAYMENT transaction of N{amount}. COMM - {liberty_commission}"
                                not_data={"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}


                                send_out_notification = cloud_messaging.send_broadcast(
                                    token=not_token,
                                    title=not_title,
                                    body=not_body,
                                    data=not_data
                                )

                                InAppTransactionNotification.create_in_app_transaction_notification(
                                    user=user_instance,
                                    title=not_title,
                                    message_body=not_body
                                )

            #####################################################################################################

                                # HANDLE COMMISSIONS AND PROFITS

                                # check_if_profit has been paid

                                get_comm_rec = CommissionsRecord.objects.select_related("user").filter(transaction_id=trans.transaction_id).last()
                                if get_comm_rec:
                                    pass
                                else:

                                    get_profits = BillsPaymentConstant.share_commissions_profit(biller=biller, amount=amount, bill_provider=bill_provider)

                                    total_profit = get_profits["total_profit"]
                                    liberty_profit = get_profits["liberty_profit"]
                                    agent_cash_profit = get_profits["agent_cash_profit"]
                                    provider_fee = get_profits["provider_fee"]

                                    # Fund User commission wallet
                                    CommissionsRecord.create_and_top_up_bill_and_pay_commissions(
                                        user = user_instance,
                                        amount = amount,
                                        biller = biller,
                                        transaction_id = trans.transaction_id,
                                        total_profit = total_profit,
                                        liberty_profit = liberty_profit,
                                        agent_cash_profit = agent_cash_profit,
                                        provider_fee = provider_fee,
                                        escrow_id=trans.escrow_id
                                    )

                        elif provider_status == "FAILED":

                            # Refund Money
                            reversal_transaction = Transaction.reverse_bills_airpin_transactions(transaction=trans, provider_status=provider_status)

                            vas_dump_data.status = "FAILED"
                            vas_dump_data.save()

                            reversed_now_trans.append(trans.id)


                            get_comms_record = CommissionsRecord.objects.filter(user=user_instance, transaction_id=trans.transaction_id)
                            if get_comms_record and not get_comms_record.filter(entry="DEBIT").exists():
                                return_vas_comm = CommissionsRecord.debit_bill_and_pay_commissions(user=trans.user, transaction_inst=trans, former_bills_inst=get_comms_record.last())

        elif vas_single_type == "AIRTIME_PIN":
            batch_id_list = []
            for first_trans in get_pending_vas:
                try:
                    airtime_pin_parent = AirtimeToPinParent.objects.get(liberty_reference=first_trans.liberty_reference)

                    # batch_id_list.append(str(airtime_pin_parent.batch_id))
                    batch_id_list.append({
                        "id": str(airtime_pin_parent.batch_id),
                        "trans": first_trans
                    })

                except:
                    pass
            
            
            if batch_id_list:
                new_list = [item["id"] for item in batch_id_list]
                verify_bills = ServicesVASApp.verify_trans_from_redbiller(batch_id_list=new_list)

                # verify_bills = {
                #     "data": [
                #         {
                #             "68e0e877-dc92-4210-bdc9-e2a88ade7ee1": {
                #                 "status": "FAILED",
                #                 "message": "Verified successfully.",
                #                 "data": {
                #                     "product": "MTN",
                #                     "quantity": 1,
                #                     "pins": [],
                #                     "reference": "ea82a10b01644a9c236e137273c382cdsd6",
                #                     "batch_id": "68e0e877-dc92-4210-bdc9-e2a88ade7ee1"
                #                 }
                #             }
                #         },
                #         {
                #             "b946c583-81df-4cfa-bcb4-31bb86e94516": {
                #                 "status": "FAILED",
                #                 "message": "Verified successfully.",
                #                 "data": {
                #                     "product": "MTN",
                #                     "quantity": 1,
                #                     "pins": [],
                #                     "reference": "ea82a10b01644a9c236e137273cdeas382c6",
                #                     "batch_id": "b946c583-81df-4cfa-bcb4-31bb86e94516"
                #                 }
                #             }
                #         }
                #     ],
                #     "count": 2
                # }

                log_debug("-------------------------------------------------------")
                log_debug("-------------------------------------------------------")
                log_info(str(verify_bills))
                log_debug("-------------------------------------------------------")
                log_debug("-------------------------------------------------------")

                new_data = verify_bills.get("data", [])
                if isinstance(new_data, list):
                    if not len(new_data) == 0:

                        for batch_data in batch_id_list:
                            # # Now, you can loop through the inner dictionary
                            # for key, value in inner_dict.items():
                            #     print(f"{key}: {value}")

                            trans: Transaction = batch_data["trans"]
                            batch = batch_data["id"]

                            log_info(trans.liberty_reference, ":::::::::")
                            for returned_data in new_data:
                                for key, value in returned_data.items():
                                    if key == batch:
                                        # value = json.loads(value.replace("'", "\""))
                                        new_response = BillsPaymentDumpData.handle_airtime_pin_resolve(batch, value, airtime_pin_parent, trans)

                                        if new_response.get("status") == "success":
                                            confirmed_trans.append(trans.id)
                                        elif new_response.get("status") == "reversed":
                                            reversed_now_trans.append(trans.id)
                                        elif new_response.get("status") == "pending":
                                            unknown_status.append(trans.id)
                                        elif new_response.get("status") == "exists":
                                            paid_back_already_trans.append(trans.id)
                                        else:
                                            pass

                
              
        log_info("confirmed_trans", confirmed_trans)
        log_info("no_charge_trans", no_charge_trans)
        log_info("paid_back_already_trans", paid_back_already_trans)
        log_info("reversed_now_trans", reversed_now_trans)
        log_info("manaul_update_successful", manaul_update_successful)
        log_info("unknown_status", unknown_status)
