import requests
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta

from accounts.models import TransactionDispute
from django.db import models

N8N_WEBHOOK_URL = "https://libertyassured.app.n8n.cloud/webhook/transaction-dispute"


class Command(BaseCommand):
    help = "Batch unresolved disputes and push them to n8n (max once per 10 minutes per dispute)"

    def handle(self, *args, **options):
        # Get unresolved disputes that haven't been reported in the last 10 minutes
        ten_minutes_ago = timezone.now() - timedelta(minutes=10)
        
        disputes = TransactionDispute.objects.filter(
            resolved=False
        ).filter(
            # Either never reported OR last reported more than 10 minutes ago
            models.Q(last_reported__isnull=True) | 
            models.Q(last_reported__lt=ten_minutes_ago)
        ).order_by("-date_created")[:20]

        if not disputes.exists():
            self.stdout.write(self.style.WARNING("No new disputes to send"))
            return

        payload = []
        dispute_ids = []
        
        for dispute in disputes:
            payload.append({
                "id": dispute.id,
                "reason": dispute.reason,
                "customer_name": dispute.customer_name,
                "customer_email": dispute.user_email,
                "customer_account_no": dispute.customer_account_no,
                "customer_phone_no": dispute.customer_phone_no,
                "customer_bank": dispute.customer_bank.name if dispute.customer_bank else None,
                "transaction_id": dispute.transaction_id,
                "rrn": dispute.rrn,
                "resolved": dispute.resolved,
                "date_created": dispute.date_created.isoformat(),
            })
            dispute_ids.append(dispute.id)

        try:
            res = requests.post(N8N_WEBHOOK_URL, json={"disputes": payload}, timeout=10)
            res.raise_for_status()
            
            # Update last_reported timestamp for all sent disputes
            TransactionDispute.objects.filter(
                id__in=dispute_ids
            ).update(last_reported=timezone.now())
            
            self.stdout.write(
                self.style.SUCCESS(f"Sent {len(payload)} disputes to n8n and marked as reported")
            )
            
        except requests.RequestException as e:
            self.stderr.write(self.style.ERROR(f"Failed to send disputes batch: {e}"))
            return