from django.core.management.base import BaseCommand
from django.db import transaction

from accounts.helpers.helper_func import settle_terminal_commission
from accounts.models import TerminalPurchaseCommission


class Command(BaseCommand):
    help = "Calculates and pays commissions to agents and their supervisors for POS requests. This function should be scheduled to run every hour."

    @transaction.atomic
    def handle(self, *args, **options):

        commissions = TerminalPurchaseCommission.objects.select_for_update().filter(settled=False, user__isnull=False).values_list('id', flat=True)
        if commissions:
            settle_terminal_commission(commissions)
