from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q, Count
from accounts.tasks import send_money_pay_buddy_task
from main.models import User, ConstantTable
from accounts.models import Escrow, Transaction, CommissionsRecord, BillsPaymentDumpData, InAppTransactionNotification, BillsPaymentConstant, WalletSystem, DebitCreditRecordOnAccount, AirtimeToPinParent
from accounts.helpers.coral_pay_manager import ServicesVASApp
from datetime import datetime, timedelta
from django.utils import timezone
from liberty_pay.settings import cloud_messaging

import random
import uuid as py_uuid
import json
from main.helper.logging_utils import log_info


class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--escrow', dest='arg1', type=str, help='Escrow IDs.')


    def handle(self, *args, **options):




        arg_escrow_ids = options.get('arg1')
        if arg_escrow_ids:
            log_info(str(arg_escrow_ids))
    
            arg_list = arg_escrow_ids.split(',')

            def safe_convert(value):
                try:
                    return int(value)
                except ValueError as e:
                    raise ValueError(e)

            arg_list = [safe_convert(s) for s in arg_list]
            
            get_escrows = Escrow.objects.filter(id__in=arg_list)
        else:
            all_trans = Transaction.objects.filter(transaction_type=Transaction.SEND_BUDDY, status="PENDING") \
                        .filter(date_created__date=datetime.now().date()).order_by("-id")[:20].values_list("escrow_id", flat=True)

            get_escrows = Escrow.objects.filter(escrow_id__in=all_trans)
        
        for escrow in get_escrows:
            resend_buddy = send_money_pay_buddy_task(
                escrow.from_wallet_id,
                escrow.to_wallet_id,
                escrow.amount,
                escrow.escrow_id,
                escrow.customer_reference,
                escrow.metadata,
            )

            log_info(str(resend_buddy))

