from datetime import <PERSON><PERSON><PERSON>
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.contrib.auth.hashers import make_password
from main.models import User
from accounts.models import AccountSystem, WalletSystem, AccountTypes
import random
import string
from main.helper.logging_utils import log_info



class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        for user in User.objects.filter(last_login__isnull=False):

            log_info(str(user, user.email, user.first_name))
            wema_account = AccountSystem.objects.filter(user=user, bank_name="Wema Bank PLC")
            log_info(str(wema_account))

            if not wema_account.exists() :

                wallet = WalletSystem.objects.filter(wallet_type="COLLECTION", user=user)

                try:
                    if wallet.exists():

                        account = AccountSystem.create_wema_account(user, wallet.last())
                        log_info(str(account))
                except Exception as e:
                    log_info(str(str(e)))
            else:
                log_info("USER ALREADY HAS ACCOUNT")


