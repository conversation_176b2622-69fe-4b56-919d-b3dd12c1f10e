from django.core.management.base import BaseCommand
from main.models import User, CallbackSystem
from accounts.models import Transaction, CommissionsRecord
from datetime import datetime, timedelta

from django.utils import timezone
from main.helper.logging_utils import log_info


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
        
        ten_minutes_before = timezone.now() - timezone.timedelta(minutes=10)

        five_days_ago = timezone.now() - timezone.timedelta(days=2)

        
        get_trans = Transaction.objects.filter(is_other_account=True, callback_sent=False, status="SUCCESSFUL", transaction_type="FUND_BANK_TRANSFER", date_created__lte=ten_minutes_before, date_created__gte=five_days_ago)
        
            
        for instance in get_trans:

            payload = {
                "user_id": instance.user.id,
                "reference": instance.liberty_reference,
                "amount": instance.amount,
                "agent_phone": instance.user.phone_number,
                "account_number": instance.is_other_account_number,
                "response": instance.payload
            }

            
            send_callback = CallbackSystem.send_callback(
                user = instance.is_other_account_owner,
                transaction_type = instance.transaction_type,
                payload = payload,
                transaction_instance = instance
            )

            log_info(str(send_callback))
