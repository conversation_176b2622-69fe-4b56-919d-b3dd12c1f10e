from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from accounts.models import ReconciledWalletData
from main.models import User
from main.helper.send_emails import send_email_with_parameters
from datetime import datetime, timedelta
import pandas as pd
from main.helper.logging_utils import log_info


class Command(BaseCommand):
    help = ""

    def add_arguments(self, parser):
        parser.add_argument('--day', dest='arg1', type=str, help="By Default, this runs for the previous day. But passing day as DD-MM-YYYY will ensure it runs for the set date.")

    def handle(self, *args, **options):
        custom_day = options.get('arg1')

        # Your custom logic goes here
        custom_date = datetime.now().date() - timedelta(days=1)
        formatted_custom_date = custom_date.strftime("%d-%m-%Y")


        if custom_day:
            try:
                custom_date = datetime.strptime(custom_day, "%d-%m-%Y").date()
            except ValueError:
                raise CommandError('Invalid Date Format. Use DD-MM-YYYY')
            

        table_needed = []
  
        # for user in User.objects.all():
        all_users = User.objects.exclude(email=settings.FLOAT_USER_EMAIL).all()
        log_info(str(all_users.count()))

        count = 1
        
        for user in all_users:

            log_info(str(count))
            count += 1

            instance = ReconciledWalletData.reconcile_Wallets(user=user, custom_date=custom_date)

            if instance:
                data = {
                    "user_email": instance.user.email,
                    "account_name": instance.user.bvn_full_name,
                    "opening_balance": instance.opening_balance,
                    "total_credit": instance.total_credit,
                    "total_debit": instance.total_debit,
                    "closing_balance_statement": instance.closing_balance_statement,
                    "closing_balance_wallet": instance.closing_balance_wallet,
                    "balance_difference": instance.balance_difference,
                    "is_ok": instance.is_ok,
                    "st_wt_bal_match": instance.st_wt_bal_match,
                    "line_to_line_recon": instance.line_to_line_recon,
                    "date": formatted_custom_date,
                }

                table_needed.append(data)
            
            else:
                pass


        current_datetime = datetime.now()
        formatted_datetime = current_datetime.strftime("%d-%m-%Y_%H-%M")

        df = pd.DataFrame(table_needed)

        if not settings.ENVIRONMENT == "development":
        # Apply the styling function to the DataFrame
        # styled_df = df.style.applymap(highlight_pd_df_cells, subset=['is_ok', 'st_wt_bal_match'])
        # styled_df = df.style.applymap(highlight_pd_df_cells, subset=['balance_difference'])
        # styled_df_as_df = styled_df.data

        # print(styled_df.render())



            df.to_excel(f"wallet_recon_{formatted_datetime}.xlsx", index=False)

     
        if table_needed:

            if not settings.ENVIRONMENT == "development":
                email_list = [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ]


            else:
                email_list = [
                    "<EMAIL>"
                ]


            for email in email_list:  
                message_body = f"Trust you're a having a great day! Attached is a report of all wallets reconciled for the day {formatted_custom_date} ran at datetime {formatted_datetime}"    

                send_email_with_parameters(
                    email=email,
                    subject=f"Wallet Reconciliation Report For {formatted_custom_date}",
                    message_body=message_body,
                    from_email="<EMAIL>",
                    attachment=df,
                    attachment_name=f"wallet_recon_{formatted_custom_date}"
                )

        pass
