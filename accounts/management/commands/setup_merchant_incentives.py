from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
from main.models import User
from accounts.models import MerchantIncentive, Transaction
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


class Command(BaseCommand):
    help = """
    Setup merchant incentives for existing merchants.
    
    This command finds merchants without existing incentive records and creates
    initial MerchantIncentive records based on their previous day's transaction volume.
    
    For each merchant without an incentive:
    1. Calculates their previous day's transaction total
    2. Creates a MerchantIncentive record with that amount as the target
    3. Sets minimum target to ₦1,000 if no transactions found
    
    Usage: python manage.py setup_merchant_incentives
    """

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without making database changes.',
        )
        parser.add_argument(
            '--min-target',
            type=float,
            default=1000.0,
            help='Minimum target amount in Naira (default: 1000.0)',
        )
        parser.add_argument(
            '--merchant-email',
            type=str,
            help='Process only a specific merchant by email address.',
        )

    def handle(self, *args, **options):
        """Main command handler"""
        try:
            dry_run = options['dry_run']
            min_target = options['min_target']
            merchant_email = options['merchant_email']

            self.stdout.write(
                self.style.SUCCESS("Setting up merchant incentives...")
            )
            
            if dry_run:
                self.stdout.write(
                    self.style.WARNING("DRY RUN MODE - No changes will be made to the database")
                )

            log_info("Starting merchant incentive setup process")

            # Find merchants without existing incentives
            merchants_without_incentives = self.get_merchants_without_incentives(merchant_email)

            if not merchants_without_incentives:
                self.stdout.write(
                    self.style.WARNING("No merchants found without existing incentives")
                )
                log_info("No merchants found without existing incentives")
                return

            self.stdout.write(f"Found {len(merchants_without_incentives)} merchant(s) without incentives")

            processed_count = 0
            created_count = 0
            error_count = 0

            # Process each merchant
            for merchant in merchants_without_incentives:
                try:
                    result = self.process_merchant(merchant, min_target, dry_run)
                    processed_count += 1
                    
                    if result['created']:
                        created_count += 1
                        
                    self.stdout.write(
                        f"✓ {merchant.email}: "
                        f"Previous day volume: ₦{result['previous_day_volume']:,.2f}, "
                        f"Target set: ₦{result['target_amount']:,.2f} "
                        f"{'(DRY RUN)' if dry_run else '(CREATED)'}"
                    )
                    
                except Exception as e:
                    error_count += 1
                    error_msg = f"Error processing merchant {merchant.email}: {str(e)}"
                    self.stdout.write(self.style.ERROR(error_msg))
                    log_error(error_msg)

            # Summary
            self.stdout.write(
                self.style.SUCCESS(
                    f"\nSetup complete:\n"
                    f"- Merchants processed: {processed_count}\n"
                    f"- Incentives created: {created_count}\n"
                    f"- Errors: {error_count}"
                )
            )

            log_info(
                f"Merchant incentive setup completed. "
                f"Processed: {processed_count}, Created: {created_count}, Errors: {error_count}"
            )

        except Exception as e:
            error_msg = f"Fatal error in merchant incentive setup: {str(e)}"
            self.stdout.write(self.style.ERROR(error_msg))
            log_critical(error_msg)
            raise

    def get_merchants_without_incentives(self, merchant_email=None):
        """
        Find merchants who don't have existing incentive records.
        
        Args:
            merchant_email: Optional email to filter to specific merchant
            
        Returns:
            QuerySet: Merchants without existing incentives
        """
        # Start with all merchants
        merchants_query = User.objects.filter(type_of_user='MERCHANT')
        
        # Filter to specific merchant if provided
        if merchant_email:
            merchants_query = merchants_query.filter(email=merchant_email)
        
        # Find merchants without existing incentives using the related name
        merchants_without_incentives = merchants_query.filter(
            merchant_incentives__isnull=True
        ).distinct()
        
        return merchants_without_incentives

    def calculate_previous_day_volume(self, merchant):
        """
        Calculate merchant's transaction volume for the previous day.
        
        Args:
            merchant: User instance
            
        Returns:
            float: Total transaction volume for previous day
        """
        # Calculate previous day date range
        yesterday = (timezone.now() - timedelta(days=1)).date()
        start_datetime = timezone.make_aware(
            datetime.combine(yesterday, datetime.min.time())
        )
        end_datetime = timezone.make_aware(
            datetime.combine(yesterday, datetime.max.time())
        )

        # Transaction types to include in volume calculation
        relevant_transaction_types = [
            'SEND_BANK_TRANSFER',
            'FUND_BANK_TRANSFER',
            'CARD_TRANSACTION_FUND',
            'CARD_TRANSACTION_FUND_TRANSFER'
        ]

        # Calculate total volume
        total_volume = Transaction.objects.filter(
            user=merchant,
            date_created__range=(start_datetime, end_datetime),
            status='SUCCESSFUL',
            is_reversed=False,
            transaction_type__in=relevant_transaction_types
        ).aggregate(
            total=Sum('amount')
        )['total'] or 0

        return float(total_volume)

    def process_merchant(self, merchant, min_target, dry_run=False):
        """
        Process a single merchant to create their incentive record.
        
        Args:
            merchant: User instance
            min_target: Minimum target amount
            dry_run: If True, don't make database changes
            
        Returns:
            dict: Processing results
        """
        # Calculate previous day volume
        previous_day_volume = self.calculate_previous_day_volume(merchant)
        
        # Set target amount (minimum of min_target)
        target_amount = max(previous_day_volume, min_target)
        
        result = {
            'merchant_email': merchant.email,
            'previous_day_volume': previous_day_volume,
            'target_amount': target_amount,
            'created': False
        }

        if not dry_run:
            # Create the incentive record using the model's method
            try:
                with transaction.atomic():
                    incentive = MerchantIncentive.get_or_create_for_merchant(
                        merchant=merchant,
                        initial_target_amount=target_amount
                    )
                    
                    # The get_or_create_for_merchant method handles the case where
                    # an incentive already exists, so we need to check if it was created
                    if incentive.target_amount == target_amount:
                        result['created'] = True
                        
                        log_info(
                            f"Created incentive for {merchant.email}. "
                            f"Previous day volume: ₦{previous_day_volume:,.2f}, "
                            f"Target set: ₦{target_amount:,.2f}"
                        )
                    else:
                        # Incentive already existed
                        log_info(
                            f"Incentive already exists for {merchant.email}. "
                            f"Existing target: ₦{incentive.target_amount:,.2f}"
                        )
                        
            except Exception as e:
                log_error(f"Error creating incentive for {merchant.email}: {str(e)}")
                raise
        else:
            # In dry run mode, we would create it
            result['created'] = True

        return result


