from django.conf import settings
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.core.cache import cache
from accounts.helpers.helper_func import notify_admin_whatsapp_on_blocked_funders, notify_admin_group, notify_admin_on_customer_trans_count_limit, get_names_match, one_way_decrypt_trans_notify
from accounts.models import CallbackSending, Transaction, WalletSystem, TransactionPerformance, UserOwnAccount, DebitCreditRecordOnAccount, \
    AccountSystem, ParallexDumpData, OtherCommissionsRecord, AllBankList, debit_list, credit_list, accepted_status_list, AccountOutflowsTrail, \
    SendCommissionScheduler
from accounts.tasks import (
    handle_debit_credit_send_email
)
from main.models import ConstantTable, User, UserFlag, list_of_allowed_terminal_users
from retail.models import LedgerTableModel
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


@receiver(post_save, sender=Transaction)
def update_balance_before_and_after(sender, instance: Transaction, created, **kwargs):
    pass
    # if instance.status == "SUCCESSFUL":
    #     sending_list = ["SEND_BUDDY", "SEND_BANK_TRANSFER", "SEND_LIBERTY_COMMISSION", "BILLS_AND_PAYMENT"]
    #     receiving_list = [
    #         "REVERSAL_BUDDY", "REVERSAL_BANK_TRANSFER", "FUND_BUDDY",
    #         "FUND_BANK_TRANSFER", "FUND_PAYSTACK", "CARD_TRANSACTION_FUND"
    #     ]

    #     return_last_object = Transaction.objects.filter(Q(user=instance.user) & Q(transaction_type__in=receiving_list) | Q(transaction_type__in=sending_list))[:-1]

    #     wallet_instance = WalletSystem.objects.get(wallet_id=instance.wallet_id)
    #     if return_last_object:

    #         instance.balance_before = return_last_object.balance_after
    #     else:
    #         instance.balance_before = 0.00

    #     instance.balance_after = wallet_instance.available_balance

        # if instance.transaction_type in sending_list:
        # elif instance.transaction_type in receiving_list:
        #     instance.balance_before = wallet_instance.available_balance - full_amount + instance.sms_charge
        #     instance.balance_after = wallet_instance.available_balance - instance.sms_charge

        # wallet_instance = WalletSystem.objects.get(wallet_id=instance.wallet_id)
        # full_amount = instance.amount + instance.liberty_commission + instance.sms_charge

        # if instance.transaction_type in sending_list:
        #     instance.balance_before = wallet_instance.available_balance + full_amount
        #     instance.balance_after = wallet_instance.available_balance - instance.sms_charge

        # elif instance.transaction_type in receiving_list:
        #     instance.balance_before = wallet_instance.available_balance - full_amount + instance.sms_charge
        #     instance.balance_after = wallet_instance.available_balance - instance.sms_charge

        # instance.save()

# if instance.transaction_type in transaction_type_list and instance.transaction_leg == "EXTERNAL" or  instance.transaction_leg == "COMMISSION":
    # send_out_transaction_payload_task.delay(
    #     status = instance.status,
    #     amount = instance.amount,
    #     commission = instance.liberty_commission,
    #     terminal_id = instance.terminal_id,
    #     timestamp = instance.last_updated,
    #     customer_id = instance.user.customer_id,
    #     customer_email = instance.user.email,
    #     transaction_type = instance.transaction_type,
    #     transaction_id = instance.transaction_id,
    #     unique_reference = instance.unique_reference,
    #     provider_status = instance.provider_status,
    # )

@receiver(post_save, sender=WalletSystem)
def check_for_false_credit_signal(sender, instance: WalletSystem, created, **kwargs):
    if not created:
        instance.refresh_from_db()
        previous_bal = instance.tracker.previous('available_balance')
        current_bal = instance.available_balance

        if instance.user.block_on_funding:
            if current_bal > previous_bal:
                amount_funded = current_bal - previous_bal
                if amount_funded >= ConstantTable.get_constant_table_instance().block_on_funding_amount:
                    if instance.user.send_money_status:
                        instance.user.block_on_funding = True
                        instance.user.send_money_status = False
                        instance.user.save()

                        # if settings.ENVIRONMENT == "development":
                        #     for num in ["*************"]:
                        #         notify_admin_whatsapp_on_blocked_funders(num, instance.user.email, amount_funded, previous_bal, current_bal, True)
                        # else:
                        for num in ["*************", "*************"]:
                            notify_admin_whatsapp_on_blocked_funders(num, instance.user.email, amount_funded, previous_bal, current_bal, True)
                else:
                    for num in ["*************", "*************"]:
                        notify_admin_whatsapp_on_blocked_funders(num, instance.user.email, amount_funded, previous_bal, current_bal, False)


        if previous_bal is not None:

            if previous_bal >= current_bal:
                pass
            else:
                # sleep(5)

                if instance.user.email != settings.FLOAT_USER_EMAIL:
                    discrepancy = current_bal - previous_bal

                    # credit_record = DebitCreditRecordOnAccount.objects.filter(user=instance.user, entry="CREDIT", amount=discrepancy, date_created__gte=one_minute_ago).last()

                    # check_for_false_credit_task.delay(
                    #     instance_id = instance.id,
                    #     previous_bal = previous_bal,
                    #     current_bal = current_bal,
                    #     discrepancy = discrepancy
                    # )

                else:
                    pass

        else:
            pass
    else:
        pass


###################################################################
# Send Transaction to HorizonPay

@receiver(post_save, sender=Transaction)
def send_email_out_signal(sender, instance: Transaction, created, **kwargs):

    if instance.email_sent == False and instance.user.email_subscription == True:
        if instance.user and instance.status == "SUCCESSFUL" and instance.transaction_type not in ["SEND_LIBERTY_COMMISSION", "SEND_BACK_TO_FLOAT_TRANSFER"]:
            if instance.transaction_type == "SEND_BANK_TRANSFER" and instance.transaction_leg == "INTERNAL":
                pass
            else:
                instance.email_sent = True
                instance.save()

                send_email_task = handle_debit_credit_send_email.apply_async(
                    queue="processbulksheet",
                    kwargs={
                        "instance_id": instance.id
                    }
                )


@receiver(post_save, sender=Transaction)
def count_transactions(sender, instance: Transaction, created, **kwargs):
    # if instance.status == "SUCCESSFUL" and instance.transaction_type not in ["SEND_LIBERTY_COMMISSION", "SEND_BACK_TO_FLOAT_TRANSFER"] and instance.transaction_leg not in ["INTERNAL", "TEMP_EXTERNAL"]:

    if instance.status == "SUCCESSFUL" and instance.is_reversed == False and instance.transaction_type not in ["SEND_LIBERTY_COMMISSION", "SEND_BACK_TO_FLOAT_TRANSFER"] and instance.transaction_leg not in ["INTERNAL", "TEMP_EXTERNAL"]:
        if instance.trans_counted == False:

            trans_perform = TransactionPerformance.create_trans_perform(
                user=instance.user,
                trans_type=instance.transaction_type,
            )

            if instance.transaction_type == "SEND_BANK_TRANSFER" and instance.transaction_leg == "EXTERNAL":
                beneficiary_name = instance.beneficiary_account_name
                user_full_name = instance.user_full_name

                if get_names_match(user_full_name, beneficiary_name) == True:
                    UserOwnAccount.add_own_account_to_table(
                        user=instance.user,
                        account_number=instance.beneficiary_nuban,
                        bank_code=instance.beneficiary_bank_code,
                    )
                else:
                    pass

            instance.trans_counted = True
            instance.save()

        else:
            pass
    else:
        pass


@receiver(post_save, sender=WalletSystem)
def notification_on_balance(sender, instance: WalletSystem, created, **kwargs):
    if instance.user.email in ConstantTable.get_constant_table_instance().watch_balance_users:
        details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with email: {instance.user.email} is being watched. Their balance has been affected. Please check."

        User.suspend_user(
            user=instance.user,
            reason=details
        )

        if settings.ENVIRONMENT == "development":
            admin_list = ["*************"]
            for num in admin_list:
                notify_admin_on_customer_trans_count_limit(user=instance.user, phone_number=num)
        else:
            notify_admin_group(user=instance.user, details = details)


@receiver(post_save, sender=Transaction)
def handle_ledger_input(sender, instance: Transaction, created, **kwargs):

    if instance.user.type_of_user in list_of_allowed_terminal_users:

        # if created:
        if instance.status in accepted_status_list and instance.transaction_type in debit_list and not LedgerTableModel.objects.filter(transaction=instance).exists():

            if instance.transaction_type == "SEND_BANK_TRANSFER" and instance.transaction_leg == "EXTERNAL":
                pass
            else:

                from accounts.tasks import handle_ledger_input_task

                handle_ledger_input_task.apply_async(
                    queue="ledgertask" if instance.user.type_of_user == "LIBERTY_RETAIL" else "ledgertaskother",
                    kwargs={
                        "instance_id": instance.id,
                        "charge_type": "BANK",
                    }
                )

            # pre_save.disconnect(process_successful_transaction, sender=sender)


@receiver(post_save, sender=DebitCreditRecordOnAccount)
def handle_sms_for_ledger_input(sender, instance: DebitCreditRecordOnAccount, created, **kwargs):

    if instance.user.type_of_user in list_of_allowed_terminal_users:

        if created:
            if instance.type_of_trans == "WHISPER_CHARGE" and instance.entry == "DEBIT":

                from accounts.tasks import handle_ledger_input_task

                handle_ledger_input_task.apply_async(
                    queue="ledgertask" if instance.user.type_of_user == "LIBERTY_RETAIL" else "ledgertaskother",
                    kwargs={
                        "instance_id": instance.id,
                        "charge_type": "SMS_CHARGE",
                        "type_of_task": "SMS_CHARGE",
                    }
                )


@receiver(post_save, sender=DebitCreditRecordOnAccount)
def handle_sms_for_ledger_input(sender, instance: DebitCreditRecordOnAccount, created, **kwargs):

    try:

        if instance.date_created:
            time = instance.date_created.strftime("%Y-%m-%d %H:%M")
            last_char = int(time[-1:])
            if last_char < 5:
                last_char = 5
            else:
                last_char = 0
            AccountOutflowsTrail.objects.create(user_email=instance.user.email, amount=instance.amount, key=f'{instance.user.email}-{instance.entry}-{instance.date_created.strftime("%Y-%m-%d %H:")}{last_char}-{instance.amount}')

    except Exception as e:
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)
        log_info("::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::", e)


# Signal receiver to clear the cache when the instance or model is updated
@receiver(post_save, sender=WalletSystem)
def clear_user_wallets_list_cache(sender, instance: WalletSystem, created, **kwargs):
    if created:
        user_instance = instance.user
        cache_key = f"{user_instance.id}_wallets"
        cache.delete(cache_key)


@receiver(post_save, sender=AccountSystem)
def clear_user_wallets_list_cache(sender, instance: AccountSystem, created, **kwargs):
    if created:
        user_instance = instance.user
        cache_key = f"{user_instance.id}_accounts"
        cache.delete(cache_key)

##############################################################################################################

# Signal receiver to clear the cache when the instance or model is deleted
@receiver(post_delete, sender=WalletSystem)
def clear_user_wallets_list_cache_delete(sender, instance: WalletSystem, **kwargs):
    user_instance = instance.user
    cache_key = f"{user_instance.id}_wallets"
    cache.delete(cache_key)


@receiver(post_delete, sender=AccountSystem)
def clear_user_wallets_list_cache_delete(sender, instance: AccountSystem, **kwargs):
    user_instance = instance.user
    cache_key = f"{user_instance.id}_accounts"
    cache.delete(cache_key)


@receiver(post_save, sender=Transaction)
def invalidate_transaction_cache(sender, instance: Transaction, **kwargs):
    """
    Invalidate the cache when a new transaction is created.
    """
    cache_key = f"transaction_history_{instance.user.id}"
    cache.delete(cache_key)


@receiver(post_save, sender=OtherCommissionsRecord)
def invalidate_other_comm_cache(sender, instance: OtherCommissionsRecord, **kwargs):
    """
    Invalidate the cache when a new record is created.
    """
    cache_key = f"other_comm_history_{instance.agent.id}"
    cache.delete(cache_key)
    if instance.sales_rep:
        cache_key2 = f"sale_rep_other_comm_history_{instance.sales_rep.id}"
        cache.delete(cache_key2)


@receiver(post_save, sender=AllBankList)
def invalidate_all_bank_list_cache(sender, instance: AllBankList, **kwargs):
    """
    Invalidate the cache when a new record is created.
    """
    cache_key = "all_bank_list"
    cache.delete(cache_key)


@receiver(post_save, sender=Transaction)
def create_callback_record(sender, instance, created, **kwargs):
    if created:
        # Get the wallet using wallet_id
        try:
            wallet = WalletSystem.objects.get(wallet_id=instance.wallet_id)
            if wallet.callback:
                CallbackSending.objects.create(transaction=instance)
        except WalletSystem.DoesNotExist:
            pass


@receiver(post_save, sender=SendCommissionScheduler)
def settle_liberty_commission(sender, instance, **kwargs):
    if not instance.settled:
        try:
            WalletSystem.pay_commission_to_liberty(
                user_id=instance.user.id,
                wallet_id=instance.wallet_id,
                wallet_type=instance.wallet_type,
                liberty_commission=instance.amount,
                from_provider_type=instance.provider,
                transaction_commission_id=instance.transaction_commission_id,
                transfer_leg=instance.transfer_leg,
                get_escrow_id=instance.escrow_id,
                transaction_sub_type=instance.transaction_sub_type
            )
            instance.settled = True
            instance.response_payload = {"detail": "Commission settled"}
        except Exception as err:
            instance.response_payload = {"detail": str(err)}

        instance.save()




