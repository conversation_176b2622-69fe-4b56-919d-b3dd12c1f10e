from django.conf import settings
import hashlib
import json
import requests
import uuid
from base64 import b64encode
from Crypto.Cipher import DES3
from Crypto.Util.Padding import pad
from main.helper.logging_utils import log_info


# Heritage OnePipe Integration APIs
class HeritageBank:
    """
    Heritage OnePipe manager; every single call should have the headers:
    {
        'Content-Type': 'application/json'
        'Authorization': 'Bearer {{api_key}}'
        'Signature': '{{MD5Hash(request_ref;client_secret)}}'
    }
    NOTE: Signature is unique per call (same request_ref should be parsed in payload).
    For Encryption of Secure element use Secret Key as shared key.
    
    NOTE: for notifications of Credit or Debit transations, a webhook url to receive a
    POST request is required. Further, webhook url should return '200 OK' on receipt.
    """

    # api_key = settings.HERITAGE_API_KEY
    # secret_key = settings.HERITAGE_SECRET_KEY
    api_key = "dsfs541"
    secret_key = "sds452"
    
    base_url = "https://api.theplug.ng"
    transact_url = "/v2/transact"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
    }

    
    @classmethod
    def encrypt_secure_element(cls, element):
        """Encrypts element using secret key"""
        buffered_key = cls.secret_key.encode("utf-16le")
        hashed_key = hashlib.md5(buffered_key).digest()
        new_key = hashed_key + hashed_key[:8]
        IV =  b"\x00" * 8
        
        cipher = DES3.new(new_key, DES3.MODE_CBC, IV)
        cipher_bytes = cipher.encrypt(pad(element.encode("ascii"), 8))
        cipher_text = b64encode(cipher_bytes).decode("utf-8")
        
        return cipher_text

        
    @classmethod
    def create_heritage_wallet(cls, user):
        """Accounts Provisioning - Open Account (Virtual)"""
        request_ref = f"{uuid.uuid4()}--Liberty-Pay"
        signature = hashlib.md5(f"{request_ref};{cls.secret_key}".encode("utf-8")).hexdigest()
        transaction_ref = f"LBHB-{user}{uuid.uuid4()}"

        cls.headers["Signature"] = signature
        absolute_url = f"{cls.base_url}{cls.transact_url}"

        customer_ref = f"**********{user}"[-10:]

        log_info(str(len(customer_ref)))
        log_info(str(customer_ref))

        
        payload = json.dumps({
            "request_ref": request_ref,
            "request_type": "open_account",
            "auth": {
                "type": None,
                "secure": None,
                "auth_provider": "HeritageVirtual",
                "route_mode": None
            },
            "transaction": {
                "mock_mode": "Inspect",
                "transaction_ref": transaction_ref,
                "transaction_desc": "Open virtual account",
                "transaction_ref_parent": None,
                "amount": 0,
                "customer": {
                    "customer_ref": customer_ref,         # *************
                    "firstname": user.first_name,
                    "surname": user.last_name,
                    "email": user.email,
                    "mobile_no": user.phone_number             # *************
                },
                "meta": {
                    "a_key": "a_meta_value_1",
                    "b_key": "a_meta_value_2"
                },
                "details": {
                    "name_on_account": f"{user.check_kyc.bvn_rel.bvn_first_name} {user.check_kyc.bvn_rel.bvn_last_name}",       # Augustine C. Jibunoh
                    "middlename": user.check_kyc.bvn_rel.bvn_middle_name,         # Chukwudi
                    "dob": user.check_kyc.bvn_rel.bvn_birthdate,              # 1990-12-31
                    "gender": user.check_kyc.bvn_rel.bvn_gender,                  # M/F
                    # "title": user.title,                    # Mr/Mrs/Miss
                    # "address_line_1": user.address,         # 27, Alara Street, Sabo
                    # "address_line_2": user.address_2,       # Sabo
                    # "city": user.city,                      # Yaba
                    "state": user.state,                    # Lagos State
                    "country": "Nigeria"                 # Nigeria
                }
            }
        })

        gender = user.check_kyc.bvn_rel.bvn_gender
        if gender is not None:
            payload["gender"] = gender
        else:
            pass

        response = requests.request("POST", absolute_url, headers=cls.headers, data=payload)
        return response.json()

    
    @classmethod
    def get_balance(cls, user, bank_account, bank_code):
        """Get user's Account balance"""
        request_ref = f"{uuid.uuid4()}--Liberty-Pay"
        signature = hashlib.md5(f"{request_ref};{cls.secret_key}".encode("utf-8")).hexdigest()
        transaction_ref = f"{uuid.uuid4()}--Liberty-Pay"
        account_number = cls.encrypt_secure_element(element=f"{bank_account};{bank_code}")
        
        cls.headers["Signature"] = signature
        absolute_url = f"{cls.base_url}{cls.transact_url}"
        
        payload = json.dumps({
            "request_ref": request_ref,
            "request_type": "get_balance",
            "auth": {
                "type": "bank.account",
                "secure": account_number,
                "auth_provider": "HeritageBank",
                "route_mode": None
            },
            "transaction": {
                "mock_mode": "Live",
                "transaction_ref": transaction_ref,
                "transaction_desc": "Get account balance",
                "transaction_ref_parent": None,
                "amount": 0,
                "customer": {
                    "customer_ref": user.mobile_no,         # *************
                    "firstname": user.first_name,
                    "surname": user.last_name,
                    "email": user.email,
                    "mobile_no": user.mobile_no             # *************
                },
                "meta": {
                    "a_key": "a_meta_value_1",
                    "b_key": "a_meta_value_2"
                },
                "details": None
            }
        })
        response = requests.request("POST", absolute_url, headers=cls.headers, data=payload)
        return response.json()
    

    @classmethod
    def get_statement(cls, user, bank_account, bank_code, start_date, end_date):
        """Get user's Account statement """
        request_ref = f"{uuid.uuid4()}--Liberty-Pay"
        signature = hashlib.md5(f"{request_ref};{cls.secret_key}".encode("utf-8")).hexdigest()
        transaction_ref = f"{uuid.uuid4()}--Liberty-Pay"
        account_number = cls.encrypt_secure_element(element=f"{bank_account};{bank_code}")
        
        cls.headers["Signature"] = signature
        absolute_url = f"{cls.base_url}{cls.transact_url}"
        
        payload = json.dumps({
            "request_ref": request_ref,
            "request_type": "get_statement",
            "auth": {
                "type": "bank.account",
                "secure": account_number,
                "auth_provider": "HeritageBank",
                "route_mode": None
            },
            "transaction": {
                "mock_mode": "Live",
                "transaction_ref": transaction_ref,
                "transaction_desc": "Get account statement",
                "transaction_ref_parent": None,
                "amount": 0,
                "customer": {
                    "customer_ref": user.mobile_no,         # *************
                    "firstname": user.first_name,
                    "surname": user.last_name,
                    "email": user.email,
                    "mobile_no": user.mobile_no             # *************
                },
                "meta": {
                    "a_key": "a_meta_value_1",
                    "b_key": "a_meta_value_2"
                },
                "details": {
                    "start_date": start_date,               # 1990-12-01
                    "end_date": end_date                    # 1990-12-30
                }
            }
        })
        response = requests.request("POST", absolute_url, headers=cls.headers, data=payload)
        return response.json()


    @classmethod
    def disburse(cls, user, account_no, bank_code):
        """
        NOTE: Amount should be in Kobo with decimal places.
        """
        request_ref = f"{uuid.uuid4()}--Liberty-Pay"
        signature = hashlib.md5(f"{request_ref};{cls.secret_key}".encode("utf-8")).hexdigest()
        transaction_ref = f"{uuid.uuid4()}--Liberty-Pay"
        
        cls.headers["Signature"] = signature
        absolute_url = f"{cls.base_url}{cls.transact_url}"

        payload = json.dumps({
            "request_ref": request_ref,
            "request_type": "disburse",
            "auth": {
                "type": None,
                "secure": None,
                "auth_provider": "HeritageBank",
                "route_mode": None
            },
            "transaction": {
                "mock_mode": "Live",
                "transaction_ref": transaction_ref,
                "transaction_desc": "A debit transaction",
                "transaction_ref_parent": None,
                "amount": 1000.00,
                "customer": {
                    "customer_ref": user.mobile_no,         # *************
                    "firstname": user.first_name,
                    "surname": user.last_name,
                    "email": user.email,
                    "mobile_no": user.mobile_no             # *************
                },
                "meta": {
                    "a_key": "a_meta_value_1",
                    "b_key": "a_meta_value_2"
                },
                "details": {
                    "destination_account": account_no,
                    "destination_bank_code": bank_code
                }
            }
        })
        response = requests.request("POST", absolute_url, headers=cls.headers, data=payload)
        return response.json()


    @classmethod
    def collect(cls, user, bank_account, bank_code):
        request_ref = f"{uuid.uuid4()}--Liberty-Pay"
        signature = hashlib.md5(f"{request_ref};{cls.secret_key}".encode("utf-8")).hexdigest()
        transaction_ref = f"{uuid.uuid4()}--Liberty-Pay"
        account_number = cls.encrypt_secure_element(element=f"{bank_account};{bank_code}")
        
        cls.headers["Signature"] = signature
        absolute_url = f"{cls.base_url}{cls.transact_url}"
        
        payload = json.dumps({
            "request_ref": request_ref,
            "request_type": "collect",
            "auth": {
                "type": "bank.account",
                "secure": account_number,
                "auth_provider": "HeritageBank",
                "route_mode": None
            },
            "transaction": {
                "mock_mode": "Live",
                "transaction_ref": transaction_ref,
                "transaction_desc": "A credit transaction",
                "transaction_ref_parent": None,
                "amount": 1000.00,
                "customer": {
                    "customer_ref": user.mobile_no,             # *************
                    "firstname": user.first_name,
                    "surname": user.last_name,
                    "email": user.email,
                    "mobile_no": user.mobile_no                 # *************
                },
                "meta": {
                    "a_key": "a_meta_value_1",
                    "b_key": "a_meta_value_2"
                },
                "details": None
            }
        })
        response = requests.request("POST", absolute_url, headers=cls.headers, data=payload)
        return response.json()
    
    
    @classmethod
    def transfer_funds(cls, user, bank_account, bank_code, destination_account, destination_code):
        request_ref = f"{uuid.uuid4()}--Liberty-Pay"
        signature = hashlib.md5(f"{request_ref};{cls.secret_key}".encode("utf-8")).hexdigest()
        transaction_ref = f"{uuid.uuid4()}--Liberty-Pay"
        
        source_account = cls.encrypt_secure_element(element=f"{bank_account};{bank_code}")
        
        cls.headers["Signature"] = signature
        absolute_url = f"{cls.base_url}{cls.transact_url}"

        payload = json.dumps({
            "request_ref": request_ref,
            "request_type": "transfer_funds",
            "auth": {
                "type": "bank.account",
                "secure": source_account,
                "auth_provider": "HeritageBank",
                "route_mode": None
            },
            "transaction": {
                "mock_mode": "Live",
                "transaction_ref": transaction_ref,
                "transaction_desc": "A transfer transaction",
                "transaction_ref_parent": None,
                "amount": 1000,
                "customer": {
                    "customer_ref": user.mobile_no,         # *************
                    "firstname": user.first_name,
                    "surname": user.last_name,
                    "email": user.email,
                    "mobile_no": user.mobile_no             # *************
                },
                "meta": {
                    "a_key": "a_meta_value_1",
                    "b_key": "a_meta_value_2"
                },
                "details": {
                    "destination_account": destination_account,
                    "destination_bank_code": destination_code,
                    "otp_override": True
                }
            }
        })
        response = requests.request("POST", absolute_url, headers=cls.headers, data=payload)
        return response.json()




# print(HeritageBank.create_heritage_wallet(user_id=848491))