from django.db import transaction as django_transaction
from django.db.models import Q
from django.db.models.query import QuerySet
from django.core.cache import cache
from main.models import ConstantTable
from accounts.models import Escrow, OtherCommissionsRecord, OtherAppTransNotify, Transaction, TransferVerificationObject, WalletSystem, AccountSystem, InAppTransactionNotification, DeletedReference, PromoCodeData, DebitCreditRecordOnAccount
from accounts.helpers.vfdbank_manager import VFDBank
from accounts.tasks import send_money_external_bank_transfer_task
from liberty_pay.settings import cloud_messaging
import uuid
import json
import ast
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical



def push_out_new_legs_from_verf(response_data):
    
    account_provider = response_data["account_provider"]
    liberty_reference = response_data["liberty_reference"]
    transaction_status_check = response_data["status"]
    transaction_leg = response_data["transaction_leg"]
    transaction_type = response_data["transaction_type"]
    trans_bank_id = response_data["trans_bank_id"]
    transaction_status_code = response_data["transaction_status_code"]
    escrow_id = response_data["escrow_id"]



    data = response_data["payload"]
    try:
        verf_timestamp = eval(data).get("data", {}).get("transactionDate")
    except TypeError:
        verf_timestamp = data.get("data", {}).get("transactionDate")
    except:
        verf_timestamp = None


    if account_provider == "WOVEN":
        liberty_reversal_reference = Transaction.create_liberty_reference(suffix="LGLP-WOV-RVS")

    elif account_provider == "VFD":
        liberty_reversal_reference = Transaction.create_liberty_reference(suffix="LGLP-VFD-RVS")
    


    # transfer_charge = ConstantTable.get_provider_fee(from_provider_type=account_provider)

    get_all_trans_verf_qs = TransferVerificationObject.objects.filter(escrow_id=escrow_id)

    transaction = get_all_trans_verf_qs.get(liberty_reference=liberty_reference)

    # transaction = TransferVerificationObject.objects.filter(liberty_reference=liberty_reference).last()
    # escrow_id = transaction.transaction_instance.escrow_id

    # Try Caching This
    escrow_instance = Escrow.objects.filter(escrow_id=escrow_id).last()

    transaction.is_verified = True
    transaction.trans_status_code = transaction_status_code
    transaction.transaction_instance.is_verified = True
    transaction.transaction_sub_type = transaction.transaction_instance.transaction_sub_type


    # if transaction_status_code == "108" and transaction.num_of_checks < 2:
    #     transaction.num_of_checks += 1

    #     transaction.save()
    #     return

    if escrow_instance and transaction:
        user_instance = escrow_instance.user
        user_wallet_instance = WalletSystem.objects.get(user=user_instance, wallet_type="COLLECTION")


        if transaction.is_finished_verification == True:
            pass
        else:
        
            log_info("I am Success")
            if transaction_status_check == "SUCCESSFUL": 
                transaction.is_finished_verification = True


                if transaction_leg == "INTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER"\
                        and escrow_instance.internal_escrow == True:



                    total_verf_amount = escrow_instance.amount + (escrow_instance.send_money_transfer_fee if escrow_instance.send_money_transfer_fee else 0.00)

                    if transaction.transaction_instance.bank_float_balance_before:
                        escrow_instance_extra_fee = escrow_instance.send_money_transfer_fee if escrow_instance.send_money_transfer_fee else 0.00
                        escrow_total_amount = escrow_instance.amount + escrow_instance_extra_fee

                        bank_float_balance_after = transaction.transaction_instance.bank_float_balance_before - total_verf_amount
                    else:
                        bank_float_balance_after = None




                    provider_fee = ConstantTable.get_provider_fee(from_provider_type=account_provider)

                    # check_ext_transaction_exist = Transaction.objects.filter(Q(escrow_id=escrow_instance.escrow_id) & Q(transaction_leg="EXTERNAL")).last()
                    # if check_ext_transaction_exist:
                    #     if check_ext_transaction_exist.status != "SUCCESSFUL":
                    #         check_ext_transaction_exist.status = "SUCCESSFUL"
                    #         check_ext_transaction_exist.save()
                    #     else:
                    #         pass


                    send_external = AccountSystem.account_send_money_external(
                        escrow_instance=escrow_instance,
                        from_wallet_type=escrow_instance.from_wallet_type,
                        from_provider_type=account_provider,
                        provider_fee=provider_fee,
                        beneficiary_account_name=escrow_instance.to_account_name,
                        beneficiary_nuban=escrow_instance.to_nuban,
                        beneficiary_bank_code=escrow_instance.to_bank_code,
                        narration=escrow_instance.narration,
                        amount=escrow_instance.amount,
                        escrow_id=escrow_instance.escrow_id,
                        # unique_reference=unique_reference
                    )

                    
                    # Change transaction status depending on response
                    transaction.transaction_instance.status = "SUCCESSFUL"
                    transaction.transaction_instance.leg_one_verification_payload = json.dumps(data)  
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.bank_float_balance_after = bank_float_balance_after

                    transaction.transaction_ver_status = "SUCCESSFUL"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)
                    transaction.first_leg_done = True

                    transaction.transaction_instance.save()
                    transaction.save()

                    escrow_instance.internal_escrow = False
                    escrow_instance.save()

                    cache.delete(transaction.transaction_instance.transaction_id)


                    float_account = AccountSystem.get_float_account(
                        from_wallet_type="FLOAT",
                        from_provider_type=escrow_instance.account_provider
                    )

                    OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                        user = escrow_instance.user,
                        amount = total_verf_amount,
                        charge = 0.00,
                        balance = bank_float_balance_after if bank_float_balance_after is not None else 0.00,
                        entry = "DEBIT",
                        to_account = float_account.account_number,
                        desc = f"Funds Withdrawal For - {transaction.transaction_instance.beneficiary_account_name}",
                        trans_id = transaction.transaction_instance.transaction_id,
                        trans_type = transaction.transaction_instance.transaction_type
                    )



                elif transaction_leg == "EXTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.external_escrow == True:

                    log_info("GOT HERRRE")
                    log_info("GOT HERRRE")
                    log_info("GOT HERRRE")
                    wallet_type = escrow_instance.from_wallet_type
                    total_amount = escrow_instance.amount + escrow_instance.liberty_commission

                    # source_account = AccountSystem.get_account_type(
                    #     user=user_instance,
                    #     from_wallet_type=wallet_type,
                    #     from_provider_type=account_provider,
                    # )

                    source_account = AccountSystem.objects.filter(
                        user=user_instance,
                        account_number = escrow_instance.user_account_number
                    ).first()

                    escrow_instance.internal_escrow = False
                    escrow_instance.external_escrow = False
                    escrow_instance.save()
        
                    # Send Debit Alert
                    send_alert = WalletSystem.transaction_alert_notfication_manager(
                        user=user_instance,
                        amount = total_amount,
                        liberty_commission = escrow_instance.liberty_commission,
                        cr_dr = "DR",
                        narration = escrow_instance.narration,
                        from_wallet_type = wallet_type,
                        transaction_instance_id=transaction.transaction_instance.transaction_id
                    )


                    # Generate Promo Code

                    promo_code = PromoCodeData.generate_promo_code(
                        user=user_instance,
                        transaction_id=transaction.transaction_instance.transaction_id,
                        trans_type=transaction.transaction_instance.transaction_type
                    )
                    
                    # Change transaction status depending on response
                    transaction.transaction_instance.status = "SUCCESSFUL"
                    transaction.transaction_instance.leg_two_verification_payload = json.dumps(data)  
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.promo_code = promo_code
                    
                    transaction.transaction_ver_status = "SUCCESSFUL"
                    transaction.verification_payload = json.dumps(data) 
                    transaction.unique_reference = trans_bank_id 
                    transaction.verf_timestamp = verf_timestamp 
                    transaction.second_leg_done = True

                    transaction.transaction_instance.save()
                    transaction.save()

                    try:
                        int_trans = Transaction.objects.get(escrow_id=escrow_instance.escrow_id, transaction_leg="INTERNAL")
                        if int_trans.status != "SUCCESSFUL":
                            int_trans.status = "SUCCESSFUL"
                            int_trans.save()
                    except:
                        pass

                    
                    # check_ext_transaction_exist = Transaction.objects.filter(Q(escrow_id=escrow_id) & Q(transaction_leg="COMMISSIONS")).exists()
                    # if check_ext_transaction_exist:
                    #     pass
                    # else:

                    
                    
                    not_token=escrow_instance.user.firebase_key
                    not_title="Transaction Successful"
                    not_body=f"You have successfully transfered N{escrow_instance.amount} to {escrow_instance.to_account_name}. COMMISSION - {escrow_instance.liberty_commission}."
                    not_data={"amount_sent": f"{escrow_instance.amount}", "available_balance": f"{user_wallet_instance.available_balance}"}
                    
            
                    send_out_notification = cloud_messaging.send_broadcast(
                        token=not_token,
                        title=not_title,
                        body=not_body,
                        data=not_data
                    )

                    InAppTransactionNotification.create_in_app_transaction_notification(
                        user=escrow_instance.user,
                        title=not_title,
                        message_body=not_body            
                    )

                    # # Pay Commission
                    # pay_commission = pay_commission_to_liberty_task.delay(
                    #     wallet_type = escrow_instance.from_wallet_type,
                    #     from_provider_type = account_provider,
                    #     source_account_id = source_account.id,
                    #     transaction_commission_id = transaction.transaction_instance.transaction_id,
                    #     get_escrow_id = escrow_id
                    # )
                    
                    
                    log_info("I AM HERE NOW FOR COMMISSIONS")
                    log_info("I AM HERE NOW FOR COMMISSIONS")
                    log_info("I AM HERE NOW FOR COMMISSIONS")
                    log_info("I AM HERE NOW FOR COMMISSIONS")
                    log_info("I AM HERE NOW FOR COMMISSIONS")
                    

                    send_out_commission = WalletSystem.pay_commission_to_liberty(
                        user_id = escrow_instance.user.id,
                        wallet_id = escrow_instance.from_wallet_id,
                        wallet_type = wallet_type,
                        liberty_commission = escrow_instance.liberty_commission,
                        from_provider_type = account_provider,
                        get_source_account = source_account,
                        transaction_commission_id = transaction.transaction_instance.transaction_id,
                        get_escrow_id = escrow_instance.escrow_id,
                        transaction_sub_type="MAIN_TRSF_COMM"
                    )

                    if Transaction.objects.filter(escrow_id=escrow_id, transaction_sub_type="NO_RO_EXT_COMM").exists():
                        pass
                    else:
                
                        handle_agent_liberty_ro_commission = OtherCommissionsRecord.handle_extra_commissions_on_transfers(
                            user = escrow_instance.user,
                            amount = escrow_instance.amount,
                            transaction_instance_id = transaction.transaction_instance.transaction_id, 
                            wallet_id = escrow_instance.from_wallet_id,
                            wallet_type = wallet_type,
                            from_provider_type = account_provider,
                            get_escrow_id = escrow_instance.escrow_id
                        )


                elif transaction_leg == "COMMISSIONS" \
                    and transaction_type == "SEND_LIBERTY_COMMISSION":

                    escrow_instance.commissions_escrow = False
                    escrow_instance.save()

                    if escrow_instance.transfer_type == 'SEND_BANK_TRANSFER' and transaction.transaction_instance.transaction_sub_type not in ["NO_RO_EXT_COMM", "FND_BANK_COMM", "CARD_PUR_COMM"]:
                        bank_float_balance_after = None
                    else:
                        if transaction.transaction_instance.bank_float_balance_before:
                            bank_float_balance_after = transaction.transaction_instance.bank_float_balance_before - escrow_instance.amount
                        else:
                            bank_float_balance_after = None

                
                    # Change transaction status depending on response
                    transaction.transaction_instance.status = "SUCCESSFUL"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.commissions_verification_payload = json.dumps(data)  
                    transaction.transaction_instance.bank_float_balance_after = bank_float_balance_after  

                    transaction.transaction_ver_status = "SUCCESSFUL"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  
                    transaction.commissions_leg_done = True


                    transaction.transaction_instance.save()
                    transaction.save()
        

                    log_info("commissions paid")

                    float_account = AccountSystem.get_float_account(
                        from_wallet_type="FLOAT",
                        from_provider_type=escrow_instance.account_provider
                    )

                    if transaction.transaction_instance.transaction_sub_type in ["NO_RO_EXT_COMM", "FND_BANK_COMM", "CARD_PUR_COMM"]:

                        total_verf_amount = transaction.transaction_instance.amount
                        OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                            user = escrow_instance.user,
                            amount = total_verf_amount,
                            charge = 0.00,
                            balance = bank_float_balance_after if bank_float_balance_after is not None else 0.00,
                            entry = "DEBIT",
                            to_account = float_account.account_number,
                            desc = f"Funds Withdrawal For - {transaction.transaction_instance.beneficiary_account_name}",
                            trans_id = transaction.transaction_instance.transaction_id,
                            trans_type = transaction.transaction_instance.transaction_type
                        )



                elif transaction_leg == "REVERSAL" \
                    and transaction_type == "REVERSAL_BANK_TRANSFER" \
                        and escrow_instance.reversed == False:

                    wallet_type = escrow_instance.from_wallet_type
                    total_amount = escrow_instance.amount + escrow_instance.liberty_commission

                    escrow_instance.external_escrow = False
                    escrow_instance.reversed = True
                    escrow_instance.save()


                    if transaction.transaction_instance.bank_float_balance_before:
                        escrow_instance_extra_fee = escrow_instance.send_money_transfer_fee if escrow_instance.send_money_transfer_fee else 0.00
                        escrow_total_amount = escrow_instance.amount + escrow_instance_extra_fee

                        bank_float_balance_after = transaction.transaction_instance.bank_float_balance_before - escrow_total_amount
                    else:
                        bank_float_balance_after = None


                    transaction.transaction_instance.status = "SUCCESSFUL"
                    transaction.transaction_instance.is_reversed = True
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.reversal_verification_payload = json.dumps(data)  
                    transaction.transaction_instance.bank_float_balance_after = bank_float_balance_after
                    
                    transaction.transaction_ver_status = "SUCCESSFUL"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  
                    transaction.reversal_leg_done = True

                    transaction.transaction_instance.save()
                    transaction.save()
        
                    # if Transaction.objects.filter(Q(escrow_id=escrow_instance.escrow_id) & Q(transaction_leg="REVERSAL"), Q(status="SUCCESSFUL")).count() > 1:
                    #     pass
                    # else:

                    if DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=transaction.transaction_instance.transaction_id).exists():
                        pass

                    else:
                        # Fund Wallet
                        # Subtract SMS charge
                        sms_charge = WalletSystem.get_sms_charge(user_instance, "REVERSAL_BANK_TRANSFER", total_amount)
                        fund_user_wallet = WalletSystem.fund_balance(
                            user=user_instance,
                            wallet=user_wallet_instance,
                            amount=total_amount - sms_charge,
                            trans_type="FUNDS_BACK_REVERSAL",
                            transaction_instance_id = transaction.transaction_instance.transaction_id,
                            unique_reference=f"{escrow_id}_RVSL"

                        )


                        # DEBIT ALERT MANAGER
                        manage_alert = WalletSystem.transaction_alert_notfication_manager(
                            user = user_instance,
                            amount = total_amount,
                            cr_dr = "CR",
                            narration = "LP-REVERSAL",
                            from_wallet_type = wallet_type,
                            transaction_instance_id=transaction.transaction_instance.transaction_id
                        )

                        log_info("transaction reversal paid back to float")

                        float_account = AccountSystem.get_float_account(
                            from_wallet_type="FLOAT",
                            from_provider_type=escrow_instance.account_provider
                        )

                        total_verf_amount = escrow_instance.amount + (escrow_instance.send_money_transfer_fee if escrow_instance.send_money_transfer_fee else 0.00)
             
                        OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                            user = escrow_instance.user,
                            amount = total_verf_amount,
                            charge = 0.00,
                            balance = bank_float_balance_after if bank_float_balance_after is not None else 0.00,
                            entry = "CREDIT",
                            to_account = float_account.account_number,
                            desc = f"Reversal For - {transaction.transaction_instance.user_full_name}",
                            trans_id = transaction.transaction_instance.transaction_id,
                            trans_type = transaction.transaction_instance.transaction_type
                        )

                    

                elif transaction_leg == "INFLOW_TO_FLOAT" \
                    and transaction_type == "SEND_BACK_TO_FLOAT_TRANSFER":
                    
                    log_info("I GOT TO INFLOW BACK TO FLOAT")
                    log_info("I GOT TO INFLOW BACK TO FLOAT")
                    log_info("I GOT TO INFLOW BACK TO FLOAT")

                    # user_instance = escrow_instance.user
                    # total_amount = escrow_instance.amount + escrow_instance.liberty_commission

                    wallet_type = escrow_instance.from_wallet_type

                    get_fund_transfer_trans = Transaction.objects.filter(escrow_id=escrow_id, transaction_type="FUND_BANK_TRANSFER").last()
                    if get_fund_transfer_trans:
                        if get_fund_transfer_trans.bank_float_balance_before:
                            bank_float_balance_after = get_fund_transfer_trans.bank_float_balance_before + escrow_instance.amount
                        
                            get_fund_transfer_trans.bank_float_balance_after = bank_float_balance_after
                            get_fund_transfer_trans.save()

                        else:
                            bank_float_balance_after = None
                    else:
                        bank_float_balance_after = None



                    # Change transaction status depending on response
                    transaction.transaction_instance.status = "SUCCESSFUL"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.funding_back_to_float_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "SUCCESSFUL"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  
                    transaction.send_back_to_float_done = True

                    transaction.transaction_instance.save()
                    transaction.save()
        


                    log_info("funding successfully sent back to float")

                    float_account = AccountSystem.get_float_account(
                        from_wallet_type="FLOAT",
                        from_provider_type=escrow_instance.account_provider
                    )

                    total_verf_amount = escrow_instance.amount
                    OtherAppTransNotify.arrange_send_data_for_other_trnas_app_notify(
                        user = escrow_instance.user,
                        amount = total_verf_amount,
                        charge = 0.00,
                        balance = bank_float_balance_after if bank_float_balance_after is not None else 0.00,
                        entry = "CREDIT",
                        to_account = float_account.account_number,
                        desc = f"Inflow For - {transaction.transaction_instance.user_full_name}",
                        trans_id = get_fund_transfer_trans.transaction_id if get_fund_transfer_trans else transaction.transaction_instance.transaction_id,
                        trans_type = get_fund_transfer_trans.transaction_type if get_fund_transfer_trans else transaction.transaction_instance.transaction_type
                    )


                    if escrow_instance.liberty_commission > 0:
                        send_commission = WalletSystem.pay_commission_to_liberty(
                            user_id=escrow_instance.user.id,
                            wallet_id = escrow_instance.from_wallet_id,
                            wallet_type=wallet_type,
                            liberty_commission = escrow_instance.liberty_commission,
                            from_provider_type=account_provider,
                            transaction_commission_id = transaction.transaction_instance.transaction_id,
                            transfer_leg="FND_BANK_COMM",
                            get_escrow_id = escrow_instance.escrow_id,
                            transaction_sub_type="FND_BANK_COMM"
                        )


                elif transaction_leg == "RE_INTERNAL" \
                        and transaction_type == "RE_FAILED_TRANSFER_IN" \
                            and transaction.transaction_instance.status != "IGNORE_HISTORY":


                    get_former_trans = TransferVerificationObject.objects.filter(escrow_id=escrow_id, transaction_leg="INTERNAL", transaction_ver_status="REVERSAL").last()
                    if get_former_trans:

                        log_info("I exist here")
                        
                        transaction.transaction_instance.status = "IGNORE_HISTORY"
                        transaction.transaction_instance.is_reversed = True
                        transaction.transaction_instance.unique_reference = f"{trans_bank_id}-RVSL"
                        transaction.transaction_instance.reversal_verification_payload = json.dumps(data)  
                        
                        transaction.transaction_ver_status = "SUCCESSFUL"
                        transaction.unique_reference = f"{trans_bank_id}-RVSL"
                        transaction.duplicate_reference = get_former_trans.unique_reference
                        transaction.verf_timestamp = verf_timestamp
                        transaction.verification_payload = json.dumps(data)  
                        transaction.has_duplicate = True
                        transaction.reversal_leg_done = True


                        get_former_trans.duplicate_reference = f"{trans_bank_id}-RVSL"


                        transaction.transaction_instance.save()
                        transaction.save()
                        get_former_trans.save()



            elif transaction_status_check == "REVERSAL": 
                log_info("HEREEE TOOO")
                get_all_trans = Transaction.objects.filter(escrow_id=escrow_id)

                check_ext_transaction_exist = get_all_trans.filter(Q(unique_reference=trans_bank_id) & Q(transaction_leg="REVERSAL") & Q(status="SUCCESSFUL")).exists()
                log_info("result_check", f"{get_all_trans}, {check_ext_transaction_exist}")

                if check_ext_transaction_exist:
                    log_info("COULDNT GET PAST")
                    pass
                else:
                    
                    log_info("I AM INSIDE AND TRYING TO RESOLVE")

                    wallet_type = escrow_instance.from_wallet_type
                    total_amount = escrow_instance.amount + escrow_instance.liberty_commission

                    # source_account = AccountSystem.get_account_type(
                    #     user=user_instance,
                    #     from_wallet_type=wallet_type,
                    #     from_provider_type=account_provider,
                    # )

                    source_account = AccountSystem.objects.filter(
                        user=user_instance,
                        account_number = escrow_instance.user_account_number
                    ).first()

                    transaction.is_finished_verification = True
                    
                    if transaction_leg == "INTERNAL" \
                        and transaction_type == "SEND_BANK_TRANSFER" \
                            and escrow_instance.internal_escrow == True \
                                and escrow_instance.reversed == False:
                        
                        with django_transaction.atomic():

                            escrow_instance.reversed = True
                            escrow_instance.save()

                            transaction.transaction_instance.status = "SUCCESSFUL"
                            transaction.transaction_instance.is_reversed = True
                            transaction.transaction_instance.unique_reference = trans_bank_id
                            transaction.transaction_instance.leg_one_verification_payload = json.dumps(data)  

                            transaction.transaction_ver_status = "REVERSAL"
                            transaction.unique_reference = trans_bank_id
                            transaction.verf_timestamp = verf_timestamp
                            transaction.verification_payload = json.dumps(data)  

                            transaction.transaction_instance.save()
                            transaction.save()
                            
                            get_temp_external = get_all_trans.filter(transaction_leg="TEMP_EXTERNAL").last()
                            if get_temp_external:
                                get_temp_external.delete()

                            sms_charge = WalletSystem.get_sms_charge(user_instance, "REVERSAL_BANK_TRANSFER", escrow_instance.amount)
                            total_amount_removed = escrow_instance.amount + escrow_instance.liberty_commission - sms_charge

                            reversal_transaction_id = get_all_trans.filter(Q(transaction_type="REVERSAL_BANK_TRANSFER_IN") | Q(reversal_type="FIRST_LEG_REVERSAL")).first()
                            if not reversal_transaction_id:
                                reversal_transaction_id = Transaction.objects.create(
                                    user=escrow_instance.user,
                                    account_provider=account_provider,
                                    wallet_id=escrow_instance.from_wallet_id,
                                    account_id=source_account.account_id,
                                    wallet_type=escrow_instance.from_wallet_type,
                                    transaction_type="REVERSAL_BANK_TRANSFER_IN",
                                    amount=total_amount_removed,
                                    provider_fee=0.00,
                                    liberty_reference=liberty_reversal_reference,
                                    unique_reference=trans_bank_id,
                                    total_amount_sent_out=total_amount_removed,
                                    escrow_id=escrow_instance.escrow_id,
                                    narration=f"REVERSAL FOR {escrow_instance.to_account_name}-{escrow_instance.to_nuban}/{escrow_instance.amount}/{escrow_instance.to_bank_name}",
                                    status="SUCCESSFUL",
                                    transaction_leg="REVERSAL",
                                    reversal_type="FIRST_LEG_REVERSAL",
                                    is_reversed=True,
                                    sms_charge=sms_charge
                                )
                            else:
                                reversal_transaction_id.status = "SUCCESSFUL"
                                reversal_transaction_id.save()

                            if DebitCreditRecordOnAccount.objects.filter(transaction_instance_id=reversal_transaction_id.transaction_id).exists():
                                pass
                            else:
                                # Fund Wallet
                                reverse_transaction = WalletSystem.fund_balance(
                                    user=escrow_instance.user,
                                    wallet=user_wallet_instance,
                                    amount=escrow_instance.amount + escrow_instance.liberty_commission - sms_charge,
                                    trans_type="FIRST_LEG_REVERSAL",
                                    transaction_instance_id = reversal_transaction_id.transaction_id,
                                    unique_reference=f"{escrow_id}_RVSL"

                                )

                            AccountSystem.handle_other_account_fund_and_debit(entry="CREDIT", amount=total_amount_removed, account_inst=source_account)
                            

                            balance_before = reverse_transaction["balance_before"]

                            balance_after = WalletSystem.get_balance_after(
                                user = user_instance,
                                balance_before=balance_before,
                                total_amount=reversal_transaction_id.total_amount_sent_out,
                                is_credit=True
                            )

                            reversal_transaction_id.balance_before = balance_before
                            reversal_transaction_id.balance_after = balance_after
                            reversal_transaction_id.save()

                        # SMS REVERSALS
                        manage_reversal_sms = WalletSystem.transaction_alert_notfication_manager(
                            user = escrow_instance.user,
                            amount = escrow_instance.amount + escrow_instance.liberty_commission,
                            cr_dr = "CR",
                            narration = "LP-REVERSAL",
                            from_wallet_type = wallet_type,
                            transaction_instance_id = reversal_transaction_id.transaction_id
                        )

                    elif transaction_leg == "EXTERNAL" \
                        and transaction_type == "SEND_BANK_TRANSFER" \
                            and escrow_instance.external_escrow == True:

                            # Carry out reversal_status_codes

                        if Transaction.objects.exclude(status="IGNORE_HISTORY").filter(escrow_id=escrow_id, transaction_type="REVERSAL_BANK_TRANSFER").exists():
                            return False
                        
                        log_info("PASSED")

                        # wallet_to_be_credited = WalletSystem.objects.filter(Q(user=user_instance) & Q(wallet_type=escrow_instance.from_wallet_type)).last()

                        account_to_be_debited_for_reversal = AccountSystem.objects.filter(
                            user=user_instance,
                            account_number = escrow_instance.user_account_number
                        ).first()
                        # account_to_be_debited_for_reversal = AccountSystem.objects.filter(wallet=wallet_to_be_credited, account_provider=escrow_instance.account_provider).last()

                        
                        liberty_commission = 0.00
                        sms_charge = WalletSystem.get_sms_charge(user_instance, "REVERSAL_BANK_TRANSFER", escrow_instance.amount)
                        total_amount_sent_out = escrow_instance.amount + escrow_instance.liberty_commission - sms_charge

                        enquire_on_account = VFDBank.get_vfd_float_balance(
                            account_number=account_to_be_debited_for_reversal.account_number
                        )

                        if enquire_on_account is None or enquire_on_account < (total_amount_sent_out - escrow_instance.extra_fee):
                            log_info("I couldnt pass")
                            pass
                        else: 

                            log_info("HELLOOOOOOOOO")
                            log_info("I AM REVERSAL")
                            log_info("HELLOOOOOOOOO")

                            transaction.transaction_instance.status = "SUCCESSFUL"
                            transaction.transaction_instance.is_reversed = True
                            transaction.transaction_instance.unique_reference = trans_bank_id
                            transaction.transaction_instance.leg_two_verification_payload = json.dumps(data)  
                            
                            transaction.transaction_ver_status = "REVERSAL"
                            transaction.verification_payload = json.dumps(data)  
                            transaction.unique_reference = trans_bank_id
                            transaction.verf_timestamp = verf_timestamp

                            transaction.transaction_instance.save()
                            transaction.save() 

                            float_account = AccountSystem.get_float_account(
                                from_wallet_type="FLOAT",
                                from_provider_type=escrow_instance.account_provider
                            )

                            if ConstantTable.get_constant_table_instance().automatic_reversals == True:
                                
                                total_amount_removed = escrow_instance.amount + escrow_instance.liberty_commission

                                reverse_transaction = AccountSystem.reversals_or_move_money_to_float(
                                    user = escrow_instance.user,
                                    account_number_to_be_debited = account_to_be_debited_for_reversal.account_number,
                                    float_account_name = float_account.account_name,
                                    float_account_number = float_account.account_number,
                                    float_bank_code = float_account.bank_code,
                                    transfer_charge = 0.00,
                                    narration = f"REVERSAL FOR {escrow_instance.to_account_name}-{escrow_instance.to_nuban}/{escrow_instance.amount}/{escrow_instance.to_bank_name}",
                                    amount = total_amount_removed,
                                    escrow_id = escrow_instance.escrow_id,
                                    liberty_reference = liberty_reversal_reference,
                                    liberty_commission=liberty_commission,
                                    total_amount_sent_out=total_amount_sent_out,
                                    account_provider = escrow_instance.account_provider,
                                    transaction_type="REVERSAL_BANK_TRANSFER",
                                    transaction_leg="REVERSAL",
                                    reversal_type="SECOND_LEG_REVERSAL"
                                )


                                AccountSystem.handle_other_account_fund_and_debit(entry="CREDIT", amount=total_amount_removed, account_inst=account_to_be_debited_for_reversal)

                            
                            else:
                                pass



                    elif transaction_leg == "COMMISSIONS" \
                        and transaction_type == "SEND_LIBERTY_COMMISSION":
                        
                        if transaction.trans_status_code == "108":
                            transaction.transaction_instance.status = "NOT_INITIATED"
                        else:
                            transaction.transaction_instance.status = "PENDING"

                        transaction.is_verified = False
                        transaction.is_finished_verification = False
                        transaction.verification_payload = json.dumps(data)


                        transaction.transaction_instance.save()
                        transaction.save()

                        pass
                        
                        # if transaction_status_code == "51":
                        #     pass
                        # else:
                            
                        #     wallet_type = escrow_instance.from_wallet_type

                        #     get_comm_trans = Transaction.objects.filter(escrow_id=escrow_id, transaction_type="SEND_LIBERTY_COMMISSION", transaction_leg="COMMISSIONS").last()
                        #     if get_comm_trans:
                        #         user_nuban = AccountSystem.objects.filter(user=user_instance, account_provider=account_provider, account_type="COLLECTION").last().account_number


                        #         if account_provider == "WOVEN":
                        #             new_reference = f"LGLP-WOV-{str(uuid.uuid4())}"

                        #         elif account_provider == "VFD":
                        #             new_reference = f"LGLP-VFD-CMR-{str(uuid.uuid4())}"

                        #         else:
                        #             new_reference = f"LGLP-ANY-{str(uuid.uuid4())}"


                        #         # Create Delete Reference Object

                        #         DeletedReference.objects.create(
                        #             transaction_instance = transaction.transaction_instance,
                        #             liberty_reference = transaction.transaction_instance.liberty_reference,
                        #             trans_type = transaction_type
                        #         )


                        #         transaction.transaction_instance.liberty_reference = new_reference
                        #         transaction.transaction_instance.status = "PENDING"
                        #         transaction.liberty_reference = new_reference
                        #         transaction.is_verified = False
                        #         transaction.is_finished_verification = False


                        #         transaction.transaction_instance.save()
                        #         transaction.save()


                        #         source_account = AccountSystem.get_account_type(
                        #             user=user_instance,
                        #             from_wallet_type=wallet_type,
                        #             from_provider_type=account_provider,
                        #         )


                        #         if transaction.transaction_instance.transaction_sub_type in ["NO_RO_EXT_COMM", "FND_BANK_COMM", "CARD_PUR_COMM"]:

                        #             send_out_commission = WalletSystem.pay_commission_to_liberty(
                        #                 user_id = escrow_instance.user.id,
                        #                 wallet_id = escrow_instance.from_wallet_id,
                        #                 wallet_type=wallet_type,
                        #                 liberty_commission = transaction.amount,
                        #                 from_provider_type = account_provider,
                        #                 transaction_commission_id = transaction.transaction_instance.transaction_id,
                        #                 transfer_leg=transaction.transaction_instance.transaction_sub_type,
                        #                 get_escrow_id = escrow_instance.escrow_id,
                        #                 transaction_sub_type=transaction.transaction_instance.transaction_sub_type,
                        #                 resend_data = transaction.transaction_instance
                        #             )
                                
                        #         else:
                        #             trans_narr_list = ["LP-VAS-CMS", "LP_CASH_OUT_CMS", "LP_TRNS_CB_COMM", "LP_TRNS_CB_COMM_CASH"]

                        #             if any(transaction.transaction_instance.narration.startswith(item) for item in trans_narr_list):
                        #                 transfer_leg = "VAS_COMMISSIONS"
                        #             else:
                        #                 transfer_leg = None


                                
                        #             send_out_commission = WalletSystem.pay_commission_to_liberty(
                        #                 user_id = escrow_instance.user.id,
                        #                 wallet_id = escrow_instance.from_wallet_id,
                        #                 wallet_type = wallet_type,
                        #                 liberty_commission = escrow_instance.liberty_commission,
                        #                 from_provider_type = account_provider,
                        #                 transaction_commission_id = transaction.transaction_instance.transaction_id,
                        #                 transfer_leg = transfer_leg,
                        #                 get_source_account=source_account if transfer_leg is None else None,
                        #                 get_escrow_id = escrow_instance.escrow_id,
                        #                 resend_data = transaction.transaction_instance,
                        #             )

                              

  
                    elif transaction_leg == "INFLOW_TO_FLOAT" \
                        and transaction_type == "SEND_BACK_TO_FLOAT_TRANSFER" \
                            and transaction.transaction_instance.status not in ["SUCCESSFUL"]:
                        
                        log_info("I GOT INTO RETRYING INFLOW TO FLOAT")

                        # get_funding_trans_qs = Transaction.objects.filter(escrow_id=escrow_id)

                        # if get_funding_trans_qs:
                        #     get_funding_trans = get_funding_trans_qs.filter(transaction_type="FUND_BANK_TRANSFER").last()

                        #     # if get_funding_trans.is_other_account == True:
                        #     #     pass
                        #     # else:

                        #     # user_nuban = AccountSystem.objects.filter(user=user_instance, account_provider=account_provider, account_type="COLLECTION").last().account_number
                        #     # user_nuban = get_funding_trans_qs.filter(transaction_type="FUND_BANK_TRANSFER").last().beneficiary_nuban
                        #     user_nuban = get_funding_trans.beneficiary_nuban
                    
                        #     transfer_charge = 0.00
                        #     liberty_commission=escrow_instance.liberty_commission
                        #     total_amount_sent_out=escrow_instance.amount


                        #     # new_reference = f"LGLP-FF-{account_provider}-{uuid.uuid4()}"

                        #     if account_provider == "WOVEN":
                        #         new_reference = f"LGLP-WOV-{str(uuid.uuid4())}"

                        #     elif account_provider == "VFD":
                        #         new_reference = f"LGLP-VFD-FDR-{str(uuid.uuid4())}"

                        #     else:
                        #         new_reference = f"LGLP-ANY-{str(uuid.uuid4())}"


                        #     # Create Delete Reference Object

                        #     DeletedReference.objects.create(
                        #         transaction_instance = transaction.transaction_instance,
                        #         liberty_reference = transaction.transaction_instance.liberty_reference,
                        #         trans_type = transaction_type
                        #     )


                        #     transaction.transaction_instance.liberty_reference = new_reference
                        #     transaction.transaction_instance.status = "PENDING"
                        #     transaction.liberty_reference = new_reference
                        #     transaction.is_verified = False
                        #     transaction.is_finished_verification = False

                            
                        #     transaction.transaction_instance.save()
                        #     transaction.save()

                        #     float_account = AccountSystem.get_float_account(
                        #         from_wallet_type="FLOAT", from_provider_type=account_provider
                        #     )


                        #     print("I AM CLOSING TO SENDING INFLOW TO FLOAT AGAIN")
                        #     send_money_back_again = AccountSystem.reversals_or_move_money_to_float(
                        #         user=user_instance,
                        #         account_number_to_be_debited=user_nuban,
                        #         float_account_name=float_account.account_name,
                        #         float_account_number=float_account.account_number,
                        #         float_bank_code=float_account.bank_code,
                        #         transfer_charge=transfer_charge,
                        #         narration="QLP_FUND_HOUSE",
                        #         amount=transaction.transaction_instance.amount,
                        #         escrow_id=escrow_id,
                        #         liberty_reference=transaction.transaction_instance.liberty_reference,
                        #         liberty_commission=liberty_commission,
                        #         total_amount_sent_out=total_amount_sent_out,
                        #         account_provider=account_provider,
                        #         transaction_type="SEND_BACK_TO_FLOAT_TRANSFER",
                        #         transaction_leg="INFLOW_TO_FLOAT",
                        #         reversal_type=None,
                        #         resend_transaction_data=transaction.transaction_instance
                        #     )



                
                    elif transaction_leg == "REVERSAL" \
                        and transaction_type == "REVERSAL_BANK_TRANSFER" \
                            and escrow_instance.reversed == False:
                        

                        log_error("I GOT INTO RETRYING FAILED REVERSAL")

                        # Create Delete Reference Object

                        # DeletedReference.objects.create(
                        #     transaction_instance = transaction.transaction_instance,
                        #     liberty_reference = transaction.transaction_instance.liberty_reference,
                        #     trans_type = transaction_type
                        # )


                        # transaction.transaction_instance.liberty_reference = liberty_reversal_reference
                        # transaction.transaction_instance.status = "PENDING"
                        # transaction.liberty_reference = liberty_reversal_reference
                        # transaction.is_verified = False
                        # transaction.is_finished_verification = False

                        
                        # transaction.transaction_instance.save()
                        # transaction.save()


                        # wallet_to_be_credited = WalletSystem.objects.filter(Q(user=user_instance) & Q(wallet_type=escrow_instance.from_wallet_type)).last()
                        # account_to_be_debited_for_reversal = AccountSystem.objects.filter(wallet=wallet_to_be_credited, account_provider=escrow_instance.account_provider).last()

                        
                        # liberty_commission = 0.00
                        # total_amount_sent_out = escrow_instance.amount + escrow_instance.liberty_commission

                        # enquire_on_account = VFDBank.get_vfd_float_balance(
                        #     account_number=account_to_be_debited_for_reversal.account_number
                        # )

                        # if enquire_on_account is None or enquire_on_account < (total_amount_sent_out - escrow_instance.extra_fee):
                        #     print("I couldnt pass")
                        #     pass
                        # else: 

                        #     print("HELLOOOOOOOOO")
                        #     print("I AM REVERSAL AGAIN")


                        #     float_account = AccountSystem.get_float_account(
                        #         from_wallet_type="FLOAT",
                        #         from_provider_type=escrow_instance.account_provider
                        #     )

                        #     if ConstantTable.get_constant_table_instance().automatic_reversals == True:
                                
                        #         total_amount_removed = escrow_instance.amount + escrow_instance.liberty_commission

                        #         reverse_transaction = AccountSystem.reversals_or_move_money_to_float(
                        #             user = escrow_instance.user,
                        #             account_number_to_be_debited = account_to_be_debited_for_reversal.account_number,
                        #             float_account_name = float_account.account_name,
                        #             float_account_number = float_account.account_number,
                        #             float_bank_code = float_account.bank_code,
                        #             transfer_charge = 0.00,
                        #             narration = f"REVERSAL FOR {escrow_instance.to_account_name}-{escrow_instance.to_nuban}/{escrow_instance.amount}/{escrow_instance.to_bank_name}",
                        #             amount = total_amount_removed,
                        #             escrow_id = escrow_instance.escrow_id,
                        #             liberty_reference = transaction.liberty_reference,
                        #             liberty_commission=liberty_commission,
                        #             total_amount_sent_out=total_amount_sent_out,
                        #             account_provider = escrow_instance.account_provider,
                        #             transaction_type="REVERSAL_BANK_TRANSFER",
                        #             transaction_leg="REVERSAL",
                        #             reversal_type="SECOND_LEG_REVERSAL",
                        #             resend_transaction_data=transaction.transaction_instance
                        #         )

            elif transaction_status_check == "PENDING": 

                if transaction_leg == "INTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.internal_escrow == True:

                    transaction.transaction_instance.status = "PENDING"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.leg_one_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "PENDING"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  

                    transaction.transaction_instance.save()
                    transaction.save()


                elif transaction_leg == "EXTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.external_escrow == True:

                    transaction.transaction_instance.status = "PENDING"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.leg_two_verification_payload = json.dumps(data)  
                    
                    transaction.transaction_ver_status = "PENDING"
                    transaction.verification_payload = json.dumps(data)  
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp

                    transaction.transaction_instance.save()
                    transaction.save()

                elif transaction_leg == "COMMISSIONS" \
                    and transaction_type == "SEND_LIBERTY_COMMISSION":
                    
                    transaction.transaction_instance.status = "PENDING"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.commissions_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "PENDING"
                    transaction.verification_payload = json.dumps(data)  
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp

                    transaction.transaction_instance.save()
                    transaction.save()

                elif transaction_leg == "REVERSAL" \
                    and transaction_type == "REVERSAL_BANK_TRANSFER" \
                        and escrow_instance.reversed == False:

                    transaction.transaction_instance.status = "PENDING"
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.reversal_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "PENDING"
                    transaction.verification_payload = json.dumps(data)  
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp

                    transaction.transaction_instance.save()
                    transaction.save()

            elif transaction_status_check == "NOT_FOUND": 
                log_info("GOT TO NOT FOUND")
                log_info("GOT TO NOT FOUND")
                log_info("GOT TO NOT FOUND")

                if transaction_leg == "INTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.internal_escrow == True:

                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.leg_one_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "NOT_FOUND"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  

                    transaction.transaction_instance.save()
                    transaction.save()


                elif transaction_leg == "EXTERNAL" \
                    and transaction_type == "SEND_BANK_TRANSFER" \
                        and escrow_instance.external_escrow == True:

                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.leg_two_verification_payload = json.dumps(data)  
                    
                    transaction.transaction_ver_status = "NOT_FOUND"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  

                    transaction.transaction_instance.save()
                    transaction.save()

                elif transaction_leg == "COMMISSIONS" \
                    and transaction_type == "SEND_LIBERTY_COMMISSION":                    
                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.commissions_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "NOT_FOUND"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  
                
                    transaction.transaction_instance.save()
                    transaction.save()

                elif transaction_leg == "REVERSAL" \
                    and transaction_type == "SEND_BACK_REVERSAL" \
                        and escrow_instance.reversed == False:

                    transaction.transaction_instance.unique_reference = trans_bank_id
                    transaction.transaction_instance.status = "NOT_FOUND"
                    transaction.transaction_instance.reversal_verification_payload = json.dumps(data)  

                    transaction.transaction_ver_status = "NOT_FOUND"
                    transaction.unique_reference = trans_bank_id
                    transaction.verf_timestamp = verf_timestamp
                    transaction.verification_payload = json.dumps(data)  

                    transaction.transaction_instance.save()
                    transaction.save()

            else:
                transaction.verification_payload = json.dumps(data)  
                transaction.unique_reference = trans_bank_id
                transaction.verf_timestamp = verf_timestamp
                transaction.save()


        from accounts.tasks import update_transaction_summary
        update_transaction_summary.apply_async(
            queue="sms_queue",
            kwargs={
                "escrow_id": escrow_id,
            }
        )
        
    else:
        pass

    