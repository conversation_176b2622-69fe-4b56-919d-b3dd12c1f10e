
import uuid
from main.helper.logging_utils import log_debug


def test_create_test_verifiable_transaction_test(new_data):
    from accounts.helpers.v2_transaction_verf_script import push_out_new_legs_from_verf

    transbank_id = str(uuid.uuid4())[0:7]

    log_debug(str(new_data))

    use_reversal = True
    
    response = {
        "account_provider": new_data["account_provider"],
        "trans_bank_id": transbank_id,
        "user_email": new_data["user_email"],
        "amount": new_data["amount"],
        "escrow_id": new_data["escrow_id"],
        "liberty_reference": new_data["liberty_reference"],
        "transaction_leg": new_data["transaction_leg"],
        "transaction_type": new_data["transaction_type"],
        "transaction_status_code": "00", 
    }

    if not use_reversal:
        response.update({
            "status":"SUCCESSFUL",
            "payload":{
                "status":"00",
                "message":"Successful Transaction Retrieval",
                "data":{
                    "TxnId": new_data["liberty_reference"],
                    "amount": new_data["amount"],
                    "accountNo": "**********",
                    "fromAccountNo":"**********",
                    "transactionStatus":"00",
                    "transactionDate":"2022-10-06 14:01:24.0",
                    "toBank":"999999",
                    "fromBank":"999999",
                    "sessionId":"",
                    "bankTransactionId": transbank_id,
                    "transactionType":"OUTFLOW"
                }
            }
        })
    
    else:

        response.update({
            "status":"REVERSAL",
            "account_provider": new_data["account_provider"],
            "trans_bank_id": transbank_id,
            "user_email": new_data["user_email"],
            "amount": new_data["amount"],
            "escrow_id": new_data["escrow_id"],
            "liberty_reference": new_data["liberty_reference"],
            "transaction_leg": new_data["transaction_leg"],
            "transaction_type": new_data["transaction_type"],
            "transaction_status_code": "108",
            "payload":{
                "status":"108",
                "message":"No Transaction Found!",
            }
        })

    log_debug(str(response))

    push_out_new_legs_from_verf(response_data=response)
