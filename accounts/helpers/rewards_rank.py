from django.db.models import Q
from django.db.models import Sum
from datetime import datetime
import decimal
from main.helper.logging_utils import log_info


def get_leader_board_by_date_type(device_type, date_param, user):
    from accounts.models import (
    TransactionReward,
    WeekHistory,
    DailyRewardHistory,
    DailyFundingRecord,
    MonthlyRewardHistory,
    LeaderBoard,
)
    a_week = WeekHistory.objects.last()
    if a_week is None: 
        get_rank = {"message": "data not available"}
    else:
        start_date = a_week.week_start_date
        end_date = a_week.week_end_date
        today_month = datetime.now().month
        today_year = datetime.now().year
        user_email  = str(user.email)
        if date_param == "daily" and device_type == "POS":
            date = datetime.now()
            instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__date=date).values('user','user__first_name', 'user__last_name', 'user__phone_number')
            get_rank = daily_rank(device_type, date, user_email, instance)
        elif date_param == "weekly" and device_type == "POS":
            date = (start_date, end_date)
            instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__range=date).values('user','user__first_name', 'user__last_name', 'user__phone_number')
            get_rank = weekly_rank(device_type, date, user, instance)
        elif date_param == "monthly" and device_type == "POS":
            instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__year=today_year, transaction_date__month=today_month).values('user','user__first_name', 'user__last_name', 'user__phone_number')
            get_rank = monthly_rank(device_type, today_year, today_month, user_email, instance)
        elif date_param == "daily" and device_type == "MOBILE":
            date = datetime.now()
            instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__date=date).values('user','user__first_name', 'user__last_name', 'user__phone_number')
            get_rank = daily_rank(device_type, date, user_email, instance)
        elif date_param == "weekly" and device_type == "MOBILE":
            date = (start_date, end_date)
            instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__range=date).values('user','user__first_name', 'user__last_name', 'user__phone_number')
            get_rank = weekly_rank(device_type, date, user, instance)
        elif date_param == "monthly" and device_type == "MOBILE":
            instance = TransactionReward.objects.distinct("user").all().filter(device_type=device_type, transaction_date__year=today_year, transaction_date__month=today_month).values('user','user__first_name', 'user__last_name', 'user__phone_number')
            get_rank = monthly_rank(device_type, today_year, today_month, user_email, instance)
        else:
            get_rank = {"message":"enter a valid date or device type"}
    return get_rank

def monthly_rank(device_type, today_year, today_month, user, instance):
    from main.models import User
    from accounts.models import TransactionReward

    
    user_history_data = []
    for i in instance:
        user_id = i["user"]
        user_name = f'{i["user__first_name"]} {i["user__last_name"]}'
        phone_number = f'{i["user__phone_number"][:4]}*****{i["user__phone_number"][-3:]}'
        log_info(i["user"])
        transaction_coin = TransactionReward.objects.filter(Q(device_type=device_type, transaction_date__year=today_year, transaction_date__month=today_month, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or decimal.Decimal(0)
        cash_back = TransactionReward.objects.filter(Q(device_type=device_type, transaction_type__in=["SEND_MONEY","WITHDRAWAL"], transaction_date__year=today_year, transaction_date__month=today_month, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
        cash_back_amount = cash_back["transaction_percentage_amount__sum"] or decimal.Decimal(0)
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", device_type=device_type, user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", device_type=device_type, user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", device_type=device_type, user=user_id, transaction_date__year=today_year, transaction_date__month=today_month)).count()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": User.objects.get(id=user_id).email,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "coin":coin,
            "cash_back_amount": cash_back_amount,
            "user_name": user_name,
            "phone_number":phone_number,
            "date": datetime.now()
            # "date": f'{today_month}:{today_year}'
            
        }
        user_history_data.append(history_data)
    
    ### rank user function
    def rankFunk(e):
        return e['coin']
    user_history_data.sort(key=rankFunk, reverse=True)
    def rankFunc(e):
        return e['eligible_transaction_count']
    user_history_data.sort(key=rankFunc, reverse=True)
    # print(user_history_data)
    user_rank = []
    for i in range(0, len(user_history_data)):
        if user == user_history_data[i]["user_id"]:
            user_history_data[i].update({"rank": i+1})
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_rank.append(user_history_data[i])
        else:
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_history_data[i].update({"rank": i+1})
        
    data = {
        "all_ranks": user_history_data,
        "user_rank": user_rank
    }
    return data

def daily_rank(device_type, date, user, instance):
    from main.models import User
    from accounts.models import TransactionReward

    
    user_history_data = []
    for i in instance:
        user_id = i["user"]
        user_name = f'{i["user__first_name"]} {i["user__last_name"]}'
        phone_number = f'{i["user__phone_number"][:4]}*****{i["user__phone_number"][-3:]}'
        log_info(i["user"])
        transaction_coin = TransactionReward.objects.filter(Q(device_type=device_type, transaction_date__date=date, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or decimal.Decimal(0)
        cash_back = TransactionReward.objects.filter(Q(device_type=device_type, transaction_type__in=["SEND_MONEY","WITHDRAWAL"], transaction_date__date=date, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
        cash_back_amount = cash_back["transaction_percentage_amount__sum"] or decimal.Decimal(0)
        
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", device_type=device_type, user=user_id, transaction_date__date=date)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", device_type=device_type, user=user_id, transaction_date__date=date)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", device_type=device_type, user=user_id, transaction_date__date=date)).count()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": User.objects.get(id=user_id).email,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "coin":coin,
            "cash_back_amount": cash_back_amount,
            "user_name": user_name,
            "phone_number":phone_number,
            "date": datetime.now()
            # "date": f'{today_month}:{today_year}'
            
        }
        user_history_data.append(history_data)
    
    ### rank user function
    def rankFunk(e):
        return e['coin']
    user_history_data.sort(key=rankFunk, reverse=True)
    def rankFunc(e):
        return e['eligible_transaction_count']
    user_history_data.sort(key=rankFunc, reverse=True)
    # print(user_history_data)
    user_rank = []
    for i in range(0, len(user_history_data)):
        if user == user_history_data[i]["user_id"]:
            user_history_data[i].update({"rank": i+1})
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_rank.append(user_history_data[i])
        else:
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_history_data[i].update({"rank": i+1})
        
    data = {
        "all_ranks": user_history_data,
        "user_rank": user_rank
    }
    return data

def weekly_rank(device_type, date, user, instance):
    from main.models import User
    from accounts.models import TransactionReward

    
    user_history_data = []
    for i in instance:
        user_id = i["user"]
        user_name = f'{i["user__first_name"]} {i["user__last_name"]}'
        phone_number = f'{i["user__phone_number"][:4]}*****{i["user__phone_number"][-3:]}'
        log_info(i["user"])
        transaction_coin = TransactionReward.objects.filter(Q(device_type=device_type, transaction_date__range=date, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or decimal.Decimal(0)
        cash_back = TransactionReward.objects.filter(Q(device_type=device_type, transaction_type__in=["SEND_MONEY","WITHDRAWAL"], transaction_date__range=date, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
        cash_back_amount = cash_back["transaction_percentage_amount__sum"] or decimal.Decimal(0)
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", device_type=device_type, user=user_id, transaction_date__range=date)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", device_type=device_type, user=user_id, transaction_date__range=date)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", device_type=device_type, user=user_id, transaction_date__range=date)).count()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": User.objects.get(id=user_id).email,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "coin":coin,
            "cash_back_amount": cash_back_amount,
            "user_name": user_name,
            "phone_number":phone_number,
            "date": datetime.now()
            # "date": f'{today_month}:{today_year}'
            
        }
        user_history_data.append(history_data)
    
    ### rank user function
    def rankFunk(e):
        return e['coin']
    user_history_data.sort(key=rankFunk, reverse=True)
    def rankFunc(e):
        return e['eligible_transaction_count']
    user_history_data.sort(key=rankFunc, reverse=True)
    # print(user_history_data)
    user_rank = []
    for i in range(0, len(user_history_data)):
        if user == user_history_data[i]["user_id"]:
            user_history_data[i].update({"rank": i+1})
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_rank.append(user_history_data[i])
        else:
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_history_data[i].update({"rank": i+1})
        
    data = {
        "all_ranks": user_history_data,
        "user_rank": user_rank
    }
    return data

def today_rank(device_type, date, user, instance):
    from main.models import User
    from accounts.models import TransactionReward

    
    user_history_data = []
    for i in instance:
        user_id = i["user"]
        user_name = f'{i["user__first_name"]} {i["user__last_name"]}'
        phone_number = f'{i["user__phone_number"][:4]}*****{i["user__phone_number"][-3:]}'
        log_info(i["user"])
        transaction_coin = TransactionReward.objects.filter(Q(device_type=device_type, transaction_date__date=date, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or decimal.Decimal(0)
        cash_back = TransactionReward.objects.filter(Q(device_type=device_type, transaction_type__in=["SEND_MONEY","WITHDRAWAL"], transaction_date__date=date, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
        cash_back_amount = cash_back["transaction_percentage_amount__sum"] or decimal.Decimal(0)
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", device_type=device_type, user=user_id, transaction_date__date=date)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", device_type=device_type, user=user_id, transaction_date__date=date)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", device_type=device_type, user=user_id, transaction_date__date=date)).count()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": User.objects.get(id=user_id).email,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "coin":coin,
            "cash_back_amount": cash_back_amount,
            "user_name": user_name,
            "phone_number":phone_number,
            "date": datetime.now()
            # "date": f'{today_month}:{today_year}'
            
        }
        user_history_data.append(history_data)
    
    ### rank user function
    def rankFunk(e):
        return e['coin']
    user_history_data.sort(key=rankFunk, reverse=True)
    def rankFunc(e):
        return e['eligible_transaction_count']
    user_history_data.sort(key=rankFunc, reverse=True)
    # print(user_history_data)
    user_rank = []
    for i in range(0, len(user_history_data)):
        if user == user_history_data[i]["user_id"]:
            user_history_data[i].update({"rank": i+1})
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_rank.append(user_history_data[i])
        else:
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_history_data[i].update({"rank": i+1})
        
    data = {
        "all_ranks": user_history_data,
        "user_rank": user_rank
    }
    return data

def last_seven_days_rank(device_type, date, user, instance):
    from main.models import User
    from accounts.models import TransactionReward

    
    user_history_data = []
    for i in instance:
        user_id = i["user"]
        user_name = f'{i["user__first_name"]} {i["user__last_name"]}'
        phone_number = f'{i["user__phone_number"][:4]}*****{i["user__phone_number"][-3:]}'
        log_info(i["user"])
        transaction_coin = TransactionReward.objects.filter(Q(device_type=device_type, transaction_date__range=date, user=user_id)).aggregate(Sum('coin_amount'))
        coin = transaction_coin["coin_amount__sum"] or decimal.Decimal(0)
        cash_back = TransactionReward.objects.filter(Q(device_type=device_type, transaction_type__in=["SEND_MONEY","WITHDRAWAL"], transaction_date__range=date, user=user_id)).aggregate(Sum('transaction_percentage_amount'))
        cash_back_amount = cash_back["transaction_percentage_amount__sum"] or decimal.Decimal(0)
        send_money_count = TransactionReward.objects.filter(Q(transaction_type="SEND_MONEY", device_type=device_type, user=user_id, transaction_date__range=date)).count()
        withdrawal_count = TransactionReward.objects.filter(Q(transaction_type="WITHDRAWAL", device_type=device_type, user=user_id, transaction_date__range=date)).count()
        airtime_data_count = TransactionReward.objects.filter(Q(transaction_type="AIRTIME_DATA", device_type=device_type, user=user_id, transaction_date__range=date)).count()

        transaction_count = int(send_money_count + withdrawal_count + airtime_data_count)
        eligible_transaction_count = int(send_money_count + withdrawal_count)
        history_data = {
            "user_id": User.objects.get(id=user_id).email,
            "send_money_count":send_money_count,
            "withdrawal_count":withdrawal_count,
            "airtime_data_count":airtime_data_count,
            "transaction_count": transaction_count,
            "eligible_transaction_count": eligible_transaction_count,
            "coin":coin,
            "cash_back_amount": cash_back_amount,
            "user_name": user_name,
            "phone_number":phone_number,
            "date": datetime.now()
            # "date": f'{today_month}:{today_year}'
            
        }
        user_history_data.append(history_data)
    
    ### rank user function
    def rankFunk(e):
        return e['coin']
    user_history_data.sort(key=rankFunk, reverse=True)
    def rankFunc(e):
        return e['eligible_transaction_count']
    user_history_data.sort(key=rankFunc, reverse=True)
    # print(user_history_data)
    user_rank = []
    for i in range(0, len(user_history_data)):
        if user == user_history_data[i]["user_id"]:
            user_history_data[i].update({"rank": i+1})
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_rank.append(user_history_data[i])
        else:
            # print("::::::::::", type(user.email), type(user_history_data[i]["user_id"]))
            user_history_data[i].update({"rank": i+1})
        
    data = {
        "all_ranks": user_history_data,
        "user_rank": user_rank
    }
    return data
