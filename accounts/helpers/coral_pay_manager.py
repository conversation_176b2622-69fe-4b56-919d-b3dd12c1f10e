from hashlib import sha256
from random import randint
import time
import requests
from django.conf import settings
import base64
import json
import redis
from datetime import timedelta
from main.helper.logging_utils import log_info


# class FundByUSSDClass():
class ServicesVASApp():
    username = settings.LIBERTY_VAS_AUTH_USERNAME
    password = settings.LIBERTY_VAS_AUTH_PASSWORD


    # generate random alpha numeric trans ref

    STRING_VALUE = f"{username}:{password}"
    AUTH_TOKEN = base64.b64encode(STRING_VALUE.encode("ascii")).decode("utf-8")

    base_url = f"{settings.PRINT_AIRTIME_BASE_URL}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Basic {AUTH_TOKEN}"
    }

    @classmethod
    def send_coralpay_data_to_endpoint(cls, main_data):

        # print(main_data)
        filter_url = cls.base_url + '/user/vaspayment/'
        payload = main_data

        try:

            response = requests.request("POST", url=filter_url, headers=cls.headers, json=payload)
            resp = response.json()

        
        except Exception as e:
            resp = str(e)

        return resp

    @classmethod
    def verify_trans_from_coralpay(cls, liberty_reference):
        
        filter_url = cls.base_url + f'/coralpay/transaction_enquiry/?paymentReference={liberty_reference}'

        try:

            response = requests.request("GET", url=filter_url, headers=cls.headers)
            resp = response.json()

        
        except Exception as e:
            resp = str(e)

        return resp


    @classmethod
    def verify_trans_from_redbiller(cls, batch_id_list):
        
        filter_url = cls.base_url + '/redbiller/single_or_multiple_redbiller_vtu_verify/'
        
        payload = {
            "batch_id": batch_id_list
        }

        try:

            response = requests.request("POST", url=filter_url, headers=cls.headers, json=payload)
            resp = response.json()

        except Exception as e:
            resp = str(e)

        return resp




    @classmethod
    def initiate_transaction(cls, first_name, last_name, phone_number, user_email, user_bvn, bank_code, amount, liberty_reference):

        filter_url = cls.base_url + '/redbiller/merchant/initiate_ussd_payment/'

        payload = {
            "first_name": first_name,
            "surname": last_name,
            "phone_no": phone_number,
            "email": user_email,
            "bvn": user_bvn,
            "bank_code": bank_code,
            "amount": amount,
            "payment_reference": liberty_reference
        }



        
            # SAMPLE RESPONSE
            # {
            #     "response":200,
            #     "status":"true",
            #     "message":"Created successfully.",
            #     "details":{
            #         "profile":{
            #             "first_name":"QILUQ",
            #             "surname":"VUTUP",
            #             "phone_no":"*************",
            #             "email":"<EMAIL>",
            #             "bvn":""
            #         },
            #         "account":{
            #             "bank_name":"ACCESS BANK",
            #             "bank_code":"000014",
            #             "ussd_code":"*901*000*5756#"
            #         },
            #         "amount":100,
            #         "reference":"wyse71c8519292f1c7c386b802d91fa436e63018",
            #         "callback_url":"https://a45a-102-67-1-49.eu.ngrok.io/api/ussd/redbiller_webhook/",
            #         "date":"2022-10-17 15:45:26"
            #     }
            # }
        try:
            response = requests.request("POST", url=filter_url, headers=cls.headers, json=payload)
            resp = response.json()

        except requests.exceptions.RequestException as e:
            resp = {
                "status": False,
                "message": f"{e}"
            }


        return resp


    @classmethod
    def verify_redbiller_payment(cls, liberty_reference):

        filter_url = cls.base_url + '/redbiller/merchant/verify/'

        payload = {
            "payment_reference": liberty_reference,
            "service": "USSD_PAYMENT",
        }


        try:
            response = requests.request("POST", url=filter_url, headers=cls.headers, json=payload)
            resp = response.json()
        except requests.exceptions.RequestException as e:
            resp = {
                "error": f"{e}"
            }


        return resp


            # """
            # SAMPLE VERIFY RESPONSE

            # {
            #     "response":200,
            #     "status":"true",
            #     "message":"Successful",
            #     "details":{
            #         "amount":100,
            #         "charge":1,
            #         "settlement":99,
            #         "profile":{
            #             "first_name":"WECUQ",
            #             "surname":"FOTAF",
            #             "phone_no":"*************",
            #             "email":"<EMAIL>",
            #             "bvn":"***********"
            #         },
            #         "account":{
            #             "bank_name":"GUARANTY TRUST BANK",
            #             "bank_code":"000013",
            #             "ussd_code":"*737*000*4207#"
            #         },
            #         "status":"Approved",
            #         "reference":"wyse4112b8c6e09d188310f99f549a0308393018",
            #         "channel":"API",
            #         "callback_url":"https://a45a-102-67-1-49.eu.ngrok.io/api/ussd/redbiller_webhook/",
            #         "date":"2022-10-17 16:18:35"
            #     }
            # }



def coral_pay_login(*args) -> dict:

    if settings.ENVIRONMENT == "development":
        base_url = "https://testdev.coralpay.com/cgateproxy/api/v2"
    else:
        base_url = "https://cgateweb.coralpay.com:567/api/v2"
    headers = {
        "Content-Type": "application/json",
    }
    username = args[0]
    password = args[1]
    
    filter_url = base_url + '/authentication'
    log_info(str(username, password))
    payload = {
        "username": username,
        "password": password
    }
    log_info(payload, "payload")
    try:
        response = requests.request("POST", url=filter_url, headers=headers, json=payload, timeout=5)
        resp = response.json()
    except requests.exceptions.RequestException as e:
        resp = {
            "status": False,
            "message": f"{e}"
        }

    return resp
    
def get_coralpay_ussd_token():
    # from accounts.helpers.coral_pay_manager import CoralWithdrawByUSSDClass
    """
    Returns the super token for auto debit of the savings user
    Responses:
        response = {
            "error": "890",
            "message": "Incorrect Service User on Agency Banking List or Cannot Generate Super Token"
        }
        response = {
                        "error": "891",
                        "message": "IP not whitelisted"
                    }
        response = {
                        "Token": "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                        "Key": "xxxxxxxxxxxx",
                        "status": "Success"
                    }
    """
    redis_db = redis.StrictRedis(
        host="localhost",
        port="6379",
        db=0,
        decode_responses=True,
        encoding="utf-8",
    )
    Username = settings.CORALPAY_WITHDRAWAL_USSD_USERNAME
    Password = settings.CORALPAY_WITHDRAWAL_USSD_PASSWORD
    coralpay_ussd_token = redis_db.get("coralpay_ussd_token")
    coralpay_ussd_key = redis_db.get("coralpay_ussd_key")
   
    if coralpay_ussd_token is None:          
        try: 
            log_info("I AM HERE", f"{Username}, {Password}")
            res = coral_pay_login(Username,Password)
            if res.get("status") == "Success":
                redis_db.set("coralpay_ussd_token", res.get("Token"), ex=3590)
                redis_db.set("coralpay_ussd_key", res.get("Key"))
                return res.get("Token"), res.get("Key")
            else:
                return None
        except:
            return get_coralpay_ussd_token()
    else:
        return coralpay_ussd_token, coralpay_ussd_key
    
class CoralWithdrawByUSSDClass():
    # from accounts.helpers.coral_pay_manager import CoralpayUSSDTokenKey
    MerchantId = settings.CORALPAY_WITHDRAWAL_USSD_MERCHANT_ID
    TerminalId = settings.CORALPAY_WITHDRAWAL_USSD_TERMINAL_ID
    # coralpay_ussd_redis = get_coralpay_ussd_token()
    # print(coralpay_ussd_redis)
    # Token = coralpay_ussd_redis[0]
    # KEY = coralpay_ussd_redis[1]
    # TimeStamp = f"{int(time.time())}"
    # hash_string = MerchantId + TerminalId + TimeStamp + KEY
    # Signature = sha256(hash_string.encode("utf-8")).hexdigest()
    if settings.ENVIRONMENT == "development":
        base_url = "https://testdev.coralpay.com/cgateproxy/api/v2"
    else:
        base_url = "https://cgateweb.coralpay.com:567/api/v2"
        
    headers = {
        "Content-Type": "application/json",
        # "Authorization": f"Bearer {Token}"
    }
    
    @classmethod
    def create_ussd_redis_token(cls):
        coralpay_ussd_redis = get_coralpay_ussd_token()
        cls.headers["Authorization"] = f"Bearer {coralpay_ussd_redis[0]}"
        cls.Key = coralpay_ussd_redis[1]
        return cls.headers, cls.Key

    @classmethod
    def invoke_reference(cls, sub_merchant_name, bank_code, amount, reference):
        TimeStamp = f"{int(time.time())}"
        MerchantId = cls.MerchantId
        TerminalId = cls.TerminalId
        KEY = cls.create_ussd_redis_token()[1]
        token_header = cls.create_ussd_redis_token()[0]
        hash_string = MerchantId + TerminalId + TimeStamp + KEY
        Signature = sha256(hash_string.encode("utf-8")).hexdigest()
        filter_url = cls.base_url + '/invokereference'
        payload = {
            "MerchantId":MerchantId,
            "TerminalId":TerminalId,
            "TraceId":reference,
            "TimeStamp":TimeStamp,
            "Signature":Signature,
            "SubMerchantName": sub_merchant_name,
            "BankCode": bank_code,
            "Amount": amount,
        }
        log_info(payload, "payload")
        log_info(KEY, "key")
        log_info(TimeStamp, "timestamp")
        log_info(Signature, "signature")
        log_info(TerminalId, "terminal id")
        log_info(MerchantId, "merchant id")
        cls.headers.update(token_header)
            # SAMPLE RESPONSE
            # {
            #     'ResponseHeader': {
            #         'ResponseCode': '00',
            #         'ResponseMessage': 'Success'
            #     }, 
            #     'ResponseDetails': {
            #         'Reference': '3141',
            #         'Amount': '100.0',
            #         'TransactionId': '23050880912173303141',
            #         'TraceId': 'cu-**********-04cb7710-f63e-4fc7-a5f1-c1ea5bb2e41b',
            #         'UssdString': '*737*000*3141#',
            #         'TimeStamp': **********, 
            #         'Signature': '402522d4516dbbaf977ee2e1321f45b42d9e359591b5e6da2d5415a8d5c9d874'
            #     }
            # }
       
        try:
            response = requests.request("POST", url=filter_url, headers=cls.headers, json=payload)
            resp = response.json()

        except requests.exceptions.RequestException as e:
            resp = {
                "status": False,
                "message": f"{e}"
            }

        return resp
    

    @classmethod
    def verify_transaction(cls, reference, amount):
        TimeStamp = f"{int(time.time())}"
        MerchantId = cls.MerchantId
        TerminalId = cls.TerminalId
        KEY = cls.create_ussd_redis_token()[1]
        hash_string = MerchantId + TerminalId + TimeStamp + KEY
        Signature = sha256(hash_string.encode("utf-8")).hexdigest()
        token_header = cls.create_ussd_redis_token()[0]
        cls.headers.update(token_header)
        filter_url = cls.base_url + '/statusquery'

        payload = {
            "MerchantId":MerchantId,
            "TerminalId":TerminalId,
            "Amount":f'{amount}',
            "TransactionId":reference,
            "TimeStamp":TimeStamp,
            "Signature":Signature,
        }
        log_info(str(payload))
        try:
            response = requests.request("POST", url=filter_url, headers=cls.headers, json=payload)
            resp = response.json()
            
        except requests.exceptions.RequestException as e:
            resp = {
                "status": False,
                "message": f"{e}"
            }

        return resp
