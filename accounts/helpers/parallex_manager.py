import binascii
import base64
import uuid
import hashlib
import requests
import json
import datetime

from django.conf import settings
from django.utils import timezone
from time import sleep
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
from binascii import unhexlify
from io import BytesIO
from django.core.cache import cache

from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


# from accounts.models import ParallexToken


class Token:
    
    # def __init__(self):
    #     self.username = settings.PARALLEX_USERNAME
    #     self.password = settings.PARALLEX_PASSWORD
    #     self.agent_id = settings.PARALLEX_AGENT_ID
    #     self.key = settings.PARALLEX_KEY
    #     self.iv = settings.PARALLEX_IV
    #     self.url = "https://sandboxapi.parallexbank.com/agentbankingsevice/api"
    #     self.headers = {
    #         'Content-Type': 'application/json',
    #         'agentID': f'{settings.PARALLEX_AGENT_ID}'
    #     }
    
    username = settings.PARALLEX_USERNAME
    password = settings.PARALLEX_PASSWORD
    agent_id = settings.PARALLEX_AGENT_ID
    key = settings.PARALLEX_KEY
    iv = settings.PARALLEX_IV
    url = "https://sandboxapi.parallexbank.com/agentbankingsevice/api"
    headers = {
        'Content-Type': 'application/json',
        'agentID': f'{settings.PARALLEX_AGENT_ID}'
    }

    # @classmethod
    # def generate_token(cls):
    #     print("I am hereeee")
        # url = f"{cls.url}/Authentication/Login"
        # key = cls.key.encode('utf-8')
        # iv = cls.iv.encode('utf-8')
        # source = json.dumps({
        #     "username": f"{cls.username}", 
        #     "password": f"{cls.password}"
        # })
    #     BS = 16
    #     pad_it = lambda s: bytes(s + (BS - len(s) % BS) * chr(BS - len(s) %BS), encoding='utf8')
    #     generator = AES.new(key, AES.MODE_CBC, iv)
    #     crypt = generator.encrypt(pad_it(source)).hex().upper()

        # try:
        #     response = requests.request(f"POST", url=url, headers=cls.headers, json=f'{crypt}')

        #     cip = response.text
        #     print(cip)
        #     print(cip)
        #     print(cip)

        #     unpad_it = lambda s : bytes(s[:-ord(s[len(s)-1:])]) 
        #     cipher = unhexlify(cip.lower())
        #     # print(cipher)
        #     degenerator = AES.new(key, AES.MODE_CBC, iv)
        #     decrypt = unpad_it(degenerator.decrypt(cipher))
        #     result = json.loads(decrypt.decode("utf-8"))
        #     # print(result)
            
    #         data = result
    #         if "responseCode" in data.keys():
    #             if data["responseCode"]=="00":
    #                 expiry_date = data["expiration"]
    #                 token = data["token"]
    #                 key = cls.key.encode('utf-8')
    #                 iv = cls.iv.encode('utf-8')
    #                 pad_it = lambda s: bytes(s + (BS - len(s) % BS) * chr(BS - len(s) %BS), encoding='utf8')
    #                 source = token
    #                 BS = 16
    #                 generator = AES.new(key, AES.MODE_CBC, iv)
    #                 crypt = generator.encrypt(pad_it(source)).hex().upper()
    #                 db_token_instance = ParallexToken.objects.filter(is_active=True).last()
    #                 if db_token_instance:
    #                     db_token_instance.token_field = crypt
    #                     db_token_instance.save()
    #                     return True
    #                 else:
    #                     return None
    #             else:
    #                 return None
    #         else:
    #             return None
        
    #     except requests.exceptions.RequestException as err:
    #         return None

            
    # @classmethod
    # def get_token(cls):


    #     key = cls.key.encode('utf-8')
    #     iv = cls.iv.encode('utf-8')

    #     db_token_instance = ParallexToken.objects.filter(is_active=True).last()
    #     if db_token_instance:
    #         token_time = db_token_instance.token_date
    #         time_now = timezone.now()
    #         c = time_now - token_time
  
    #         minutes = divmod(c.total_seconds(), 60) 
    #         if (minutes[0]) < 55:
    #             cip = db_token_instance.token_field
    #             unpad_it = lambda s : bytes(s[:-ord(s[len(s)-1:])]) 
    #             cipher = unhexlify(cip.lower())
    #             # print(cipher)
    #             degenerator = AES.new(key, AES.MODE_CBC, iv)
    #             decrypt = unpad_it(degenerator.decrypt(cipher))
    #             token = decrypt.decode("utf-8")

    #             return token
    #         else:
    #             print("Generating Token")
    #             generate_token = Token().generate_token()
    #             if generate_token is True:
    #                 token = Token().get_token()
    #                 return token
    #     else:
    #         return None


class ParallexBank:

    base_url = "https://sandboxapi.parallexbank.com/agentbankingsevice/api/"
    base_headers = {
        "Content-Type": "application/json"
    }
    agent_id = settings.PARALLEX_AGENT_ID
    headers = {
        "Content-Type": "application/json",
        "agentID": f"{agent_id}",
    }
    # PARALLEX_TOKEN = Token.get_token()
    username = settings.PARALLEX_USERNAME
    password = settings.PARALLEX_PASSWORD
    str_key = settings.PARALLEX_NOTIFY_KEY
    str_iv = settings.PARALLEX_NOTIFY_IV
    key = base64.b64decode(str_key)
    iv = base64.b64decode(str_iv)

    @classmethod
    def encrypt_data(cls, source):
        try:
            BS = 16
            pad_it = lambda s: bytes(s + (BS - len(s) % BS) * chr(BS - len(s) %BS), encoding='utf8')
            generator = AES.new(cls.key, AES.MODE_CBC, cls.iv)
            crypt = generator.encrypt(pad_it(source)).hex().upper()
        except Exception as ex:
            crypt = None
        
        return crypt
    

    @classmethod
    def decrypt_data(cls, source):  
        try:
            cipher = AES.new(cls.key, AES.MODE_CBC, cls.iv)
            decrypted_bytes = cipher.decrypt(source)
            plaintext = unpad(decrypted_bytes, AES.block_size).decode('utf-8')
        except Exception as ex:
            plaintext = None

        return plaintext



    @classmethod
    def retrieve_token(cls):

        cache_key = 'parallex_token_data'
        parallex_token_data = cache.get(cache_key)

        if parallex_token_data is not None:
            now = datetime.datetime.now()
            expiry = parallex_token_data.get("expiry")
            token = parallex_token_data.get("token")

            time_difference = expiry - now

            if time_difference >= datetime.timedelta(minutes=60) and token:
                return token
            else:
                return cls.generate_token()
        
        else:
            return cls.generate_token()


    @classmethod
    def generate_token(cls):
        url = f"{cls.base_url}/Authentication/Login"

        source = json.dumps({
            "username": f"{cls.username}", 
            "password": f"{cls.password}"
        })

        crypt = cls.encrypt_data(source=source)

        try:
            response = requests.request(f"POST", url=url, headers=cls.headers, json=f'{crypt}')

            cip = response.text
            log_info(str(cip))
            log_info(str(cip))
            log_info(str(cip))

            data = cls.decrypt_data(cip)

            log_info(str(data))

            cache_key = 'parallex_token_data'
            cache.set(cache_key, {
                "expriry": datetime.datetime.now(),
                "token": data
            })
        
        except requests.exceptions.RequestException as err:
            
            data = None
            log_info("Nothing")

        

            # unpad_it = lambda s : bytes(s[:-ord(s[len(s)-1:])]) 
            # cipher = unhexlify(cip.lower())
            # degenerator = AES.new(key, AES.MODE_CBC, iv)
            # decrypt = unpad_it(degenerator.decrypt(cipher))
            # data = json.loads(decrypt.decode("utf-8"))
            # # print(result)


            # if "responseCode" in data.keys():
            #     if data["responseCode"]=="00":
            #         expiry_date = data["expiration"]
            #         token = data["token"]
            #         key = cls.key.encode('utf-8')
            #         iv = cls.iv.encode('utf-8')
            #         pad_it = lambda s: bytes(s + (BS - len(s) % BS) * chr(BS - len(s) %BS), encoding='utf8')
            #         source = token
            #         BS = 16
            #         generator = AES.new(key, AES.MODE_CBC, iv)
            #         crypt = generator.encrypt(pad_it(source)).hex().upper()
            #         db_token_instance = ParallexToken.objects.filter(is_active=True).last()
 
            


        return data
    

    @classmethod
    def reset_default_password(cls, **kwargs):
        filter_url = "Authentication/DoReset"
        url = cls.base_url + filter_url
        headers = {
            "Content-Type": "application/json"
        }

        data = json.dumps(kwargs)
        log_debug("--------------------------")
        log_info(str(data))
        log_debug("--------------------------")

        response = requests.request("POST", url, headers=headers, data=data)
        resp = json.loads(response.text)
        log_info(str(resp))
        return resp


    @classmethod
    def encrypt_decrypt(cls, url, method, data=None):

        headers = {
            "Content-Type": "application/json",
            "AgentID": cls.agent_id,
            "Authorization": f"Bearer {cls.PARALLEX_TOKEN}",
        }
        if data is not None:

            key = cls.key.encode("utf-8")
            iv = cls.iv.encode("utf-8")

            pad_it = lambda s: bytes(
                s + (BS - len(s) % BS) * chr(BS - len(s) % BS), encoding="utf8"
            )
            source = data
            BS = 16
            generator = AES.new(key, AES.MODE_CBC, iv)
            crypt = generator.encrypt(pad_it(source)).hex().upper()
            # print(crypt)
            
            try:

                response = requests.request(
                    f"{method}", url=url, headers=headers, json=f"{crypt}"
                )
                # print(response)

                cip = response.text
                unpad_it = lambda s: bytes(s[: -ord(s[len(s) - 1 :])])
                cipher = unhexlify(cip.lower())
                degenerator = AES.new(key, AES.MODE_CBC, iv)
                decrypt = unpad_it(degenerator.decrypt(cipher))
                result = json.loads(decrypt.decode("utf-8"))
                # print(result)
                return result
            
            except requests.exceptions.RequestException as err:
                return {
                    "status": "99",
                    "message": str(err)
                }


        else:
            key = cls.key.encode("utf-8")
            iv = cls.iv.encode("utf-8")

            try:
                response = requests.request(f"{method}", url=url, headers=headers)
                
                cip = response.text
                # print(cip, ">>>>>>>>>>>>>>>>>>>")
                unpad_it = lambda s: bytes(s[: -ord(s[len(s) - 1 :])])
                cipher = unhexlify(cip.lower())
                # print(cipher)
                degenerator = AES.new(key, AES.MODE_CBC, iv)
                decrypt = unpad_it(degenerator.decrypt(cipher))
                result = json.loads(decrypt.decode("utf-8"))
                return result

            except requests.exceptions.RequestException as err:
                return {
                    "status": "99",
                    "message": str(err)
                }


    @classmethod
    def parallex_account_enquiry(cls, **kwargs):
        filter_url = "Account/DoAccountNameEnquiry"
        url = cls.base_url + filter_url
        method = "POST"
        data = json.dumps(kwargs)
        details = ParallexBank().encrypt_decrypt(url, method, data)
        return details


    @classmethod
    def get_all_banks(cls):
        filter_url = "Account/GetAllFinancialInstitutions"
        url = cls.base_url + filter_url
        method = "GET"
        banks = ParallexBank().encrypt_decrypt(url, method)
        return banks

    @classmethod
    def get_dropdown(cls, value):
        key = cls.key.encode("utf-8")
        iv = cls.iv.encode("utf-8")

        pad_it = lambda s: bytes(
            s + (BS - len(s) % BS) * chr(BS - len(s) % BS), encoding="utf8"
        )
        source = value
        BS = 16
        generator = AES.new(key, AES.MODE_CBC, iv)
        crypt = generator.encrypt(pad_it(source)).hex().upper()
        # print(crypt)
        filter_url = f"Account/GetRequiredDropdowns?requestType={crypt}"
        url = cls.base_url + filter_url
        method = "GET"
        drop_down = ParallexBank().encrypt_decrypt(url, method)
        return drop_down



    @classmethod
    def bvn_validation(cls, **kwargs):
        filter_url = "Bvn/DoBvnValidation1"
        url = cls.base_url + filter_url
        method = "POST"
        data = json.dumps(kwargs)
        bvn_validation = ParallexBank().encrypt_decrypt(url, method, data)
        return bvn_validation


    @classmethod
    def intra_bank_transfer(cls, data):
        filter_url = "Transfer/DoIntraBankTransfer"
        url = cls.base_url + filter_url
        method = "POST"
        transfer = ParallexBank().encrypt_decrypt(url, method, str(data))
        return transfer


    
    @classmethod
    def inter_bank_transfer(cls, data):

        filter_url = "Transfer/DoInterBankTransfer"
        url = cls.base_url + filter_url
        method = "POST"
        transfer = ParallexBank().encrypt_decrypt(url, method, str(data))
        return transfer
    

    @classmethod
    def open_account(cls, data):

        filter_url = "Account/DoAccountOpening"
        url = cls.base_url + filter_url
        method = "POST"
        open_account = ParallexBank().encrypt_decrypt(url, method, str(data))
        return open_account
    

    @classmethod
    def request_debit_card(cls, data):

        filter_url = "Cards/DoCardRequest"
        url = cls.base_url + filter_url
        method = "POST"
        request_debit_card = ParallexBank().encrypt_decrypt(url, method, str(data))
        return request_debit_card
    
    
    @classmethod
    def transfer_query(cls, data):
        
        filter_url = "Transfer/DoTransferRequery"
        url = cls.base_url + filter_url
        method = "POST"
        transfer_query = ParallexBank().encrypt_decrypt(url, method, str(data))
        return transfer_query

    #############################################################################################################################

    @classmethod
    def notify_create_user(cls, email, ip):
        url = f"{settings.PARALLEX_BASE_URL}/AuthenticationServiceWH/api/Authenticate/register"
        headers = {
            "Content-Type": "application/json"
        }

        data = {
            "username": settings.PARALLEX_USERNAME,
            "name": "Liberty Pay",
            "email": email,
            "password": settings.PARALLEX_PASSWORD,
            "ipAddress": ip
        }

        log_debug("--------------------------")
        log_info(str(data))
        log_debug("--------------------------")

        response = requests.request("POST", url, headers=headers, json=data)
        resp = response.json()
        log_info(str(resp))
        return resp
    

    @classmethod
    def notify_webhook_login(cls):
        url = f"{settings.PARALLEX_BASE_URL}/AuthenticationServiceWH/api/Authenticate/login"
        headers = {
            "Content-Type": "application/json"
        }

        data = {
            "username": settings.PARALLEX_USERNAME,
            "password": settings.PARALLEX_PASSWORD,
        }

        log_debug("--------------------------")
        log_info(str(data))
        log_debug("--------------------------")

        response = requests.request("POST", url, headers=headers, json=data)
        resp = response.json()
        log_info(str(resp))
        token = resp.get("token")
        log_info(str(token))
        return token
    
    
    @classmethod
    def notify_webhook_get_trans_det(cls, trans_id):
        url = f"{settings.PARALLEX_BASE_URL}/WebhookAPI/WebHook/getTransactionDetails?transId={trans_id}"

        headers = {
            "Content-Type": "application/json",
        }

        log_info(str(url))
        log_info(str(headers))

        response = cls.make_authenticated_request(url, method='GET', data=None, headers=headers)

        resp = response.json()
        log_info(str(resp))
        return resp
    

    @classmethod
    def get_token(cls, regenerate=False):
        if not regenerate:
            cache_key = "parallex_token"
            cached_token = cache.get(cache_key)
            if cached_token:
                return cached_token
            else:
                return cls.notify_webhook_login()
        else:
            return cls.notify_webhook_login()
        

    @classmethod
    def make_authenticated_request(cls, url, method='GET', data=None, headers=None):
        # Ensure that the token is valid or regenerate it
        token = cls.get_token()
        request_headers = headers or {}
        request_headers['Authorization'] = f'Bearer {token}'
        
        response = requests.request(method, url, data=data, headers=request_headers)
        
        # Handle 401 unauthorized response by regenerating the token and retrying the request
        if response.status_code == 401:
            token = cls.get_token(regenerate=True)
            request_headers['Authorization'] = f'Bearer {token}'
            response = requests.request(method, url=url, data=data, headers=request_headers)
        
        return response
    

    








# class VFDBank:
#     base_url, access_token, wallet_credentials = False, False, False
#     if settings.ENVIRONMENT == "development":
#         base_url = "https://devesb.vfdbank.systems:8263/vfd-wallet/1.1/"
#         access_token = f"{settings.VFD_ACCESS_TOKEN_TEST}"
#         wallet_credentials = f"{settings.VFD_WALLET_CREDENTIALS_TEST}"

#     elif settings.ENVIRONMENT == "production":
#         base_url = f"{settings.VFD_BASE_URL_LIVE}"
#         # "https://prodesb.vfdbank.systems:8263/vfd-wallet/1.1/"
#         # https://prodesb.vfdbank.systems:8263/vfd-wallet/1.1/wallet2/clientdetails/create?
#         access_token = f"{settings.VFD_ACCESS_TOKEN_LIVE}"
#         wallet_credentials = f"{settings.VFD_WALLET_CREDENTIALS_LIVE}"

#     headers = {
#         "Authorization": f"Bearer {access_token}",
#         "accept": "application/json"
#     }
#     params = {
#         "wallet-credentials": f"{wallet_credentials}"
#     }



#     @classmethod
#     def create_vfd_wallet(cls, user, user_phone_number, retry_count, ):

#         filter_url = "wallet2/clientdetails/create"
#         url = cls.base_url + filter_url

#         if user.bvn_acct_num_count < 2:
#             firstname = user.check_kyc.bvn_rel.bvn_first_name
#             lastname = user.check_kyc.bvn_rel.bvn_last_name
#             bvn = user.check_kyc.bvn_rel.bvn_number
        
#         else:
#             number_to_add = user.bvn_acct_num_count - 1
#             firstname = user.check_kyc.bvn_rel.bvn_first_name
#             lastname = user.check_kyc.bvn_rel.bvn_last_name
#             bvn = f"{user.check_kyc.bvn_rel.bvn_number}-{number_to_add}"

            
#         payload = {
#             "firstname": firstname,
#             "lastname": lastname,
#             "dob": user.check_kyc.bvn_rel.bvn_birthdate,
#             "address": f"{user.street} {user.lga} {user.state}",
#             "gender": user.check_kyc.bvn_rel.bvn_gender,
#             "phone": user.check_kyc.bvn_rel.bvn_phone_number,
#             "bvn": bvn
#         }

        
#         middle_name = user.check_kyc.bvn_rel.bvn_middle_name
#         if middle_name is not None:
#             payload["middlename"] = middle_name
#         else:
#             pass

#         gender = user.check_kyc.bvn_rel.bvn_gender
#         if gender is not None:
#             payload["gender"] = gender
#         else:
#             pass

#         # response = {
#         #     "status": "00",
#         #     "message": "Successful Creation",
#         #     "data": {
#         #         "firstname": "Ogba",
#         #         "middlename": "middlename",
#         #         "lastname": "lastname",
#         #         "bvn": "***********",
#         #         "phone": "************",
#         #         "dob": "12-Aug-1954",
#         #         "accountNo": "**********"
#         #     }
#         # }


#         #         {
#         # "status": "929",
#         # "message": "BVN Exist"
#         # }



#         try:

#             response = requests.request(
#                 "POST", url=url, headers=cls.headers, params=cls.params, json=payload, timeout=15
#             )
#             res = response.json()
#                 # if res["status"] == "success":
#             return res

#         except requests.exceptions.RequestException as err:
#             return {
#                 "status": "99",
#                 "message": str(err)
#             }
#         # except ConnectionError as err:
#         #     return str(err)
#         # except requests.ConnectTimeout as err:
#         #     return str(err)
#         # finally as e:
#         #     return None



#     @classmethod
#     def vfd_account_enquiry(cls, account_number=None):

#         filter_url = "wallet2/account/enquiry"
#         url = cls.base_url + filter_url

#         cls.params["accountNumber"] = account_number

#         try:
#             response = requests.request(
#                 "GET", url=url, headers=cls.headers, params=cls.params
#             )

#             print(response.json())
#             return response.json()
#         except requests.exceptions.RequestException as e:
#             return {
#                 "vfd_error": True,
#                 "status": "91",
#                 "message": f"VFD Request Broken {e}"
#             }


#     @classmethod
#     def vfd_get_transfer_recipient(cls, transfer_type, account_number, bank_code):
        
#         if settings.ENVIRONMENT == "production":

#             filter_url = f"wallet2/transfer/recipient"
#             url = cls.base_url + filter_url

#             cls.params["accountNo"] = account_number
#             cls.params["transfer_type"] = transfer_type
#             cls.params["bank"] = bank_code

#             try:

#                 response = requests.request(
#                     "GET", url=url, headers=cls.headers, params=cls.params
#                 )

#                 print(response.json())
#                 return response.json()

#             except requests.exceptions.RequestException as e:

#                 return {
#                     "status": "91",
#                     "message": f"VFD Request Broken {e}"
#                 }
        
#         else:
#             return {
#                 "status": "00",
#                 "message": "Account Found"
#             }


#     @classmethod
#     def initiate_payout(
#         cls,
#         beneficiary_account_name,
#         beneficiary_nuban,
#         beneficiary_bank_code,
#         source_account,
#         narration,
#         amount,
#         transfer_type,
#         user_bvn,
#         reference,
#         transfer_leg=None
#     ):
        
#         if settings.ENVIRONMENT == "development":
#             print("I AM HERE FOR TEST SEND BANK TRANS")
#             print("I AM HERE FOR TEST SEND BANK TRANS")
#             return {
#                 "status":"00",
#                 "message":"****************",
#                 "data":{
#                     "txnId": reference,
#                     "sessionId":"090110226514433719724158835748",
#                     "reference":"****************"
#                 }
#             }

#         else:

#             filter_url = "wallet2/transfer"
#             url = cls.base_url + filter_url


#             if transfer_leg is not None and transfer_leg in ["FIRST_LEG", "CASH_OUT", "VAS_COMMISSIONS", "TRNS_CB_COMM", "TRNS_CB_COMM_CASH"]:
#                 enquire_for_user_account = cls.vfd_account_enquiry()

#             else:
#                 enquire_for_user_account = cls.vfd_account_enquiry(
#                     account_number=source_account
#                 )

#             if enquire_for_user_account.get("vfd_error"):
#                 return {
#                     "error": "VFD Request Broken",
#                     "message": f"{enquire_for_user_account}"
#                 }
                

#             fromSavingsId = enquire_for_user_account.get("data")["accountId"]
#             fromClient = enquire_for_user_account.get("data")["client"]
#             fromClientId = enquire_for_user_account.get("data")["clientId"]
#             fromAccount = enquire_for_user_account.get("data")["accountNo"]


#             get_recipient = cls.vfd_get_transfer_recipient(
#                 transfer_type=transfer_type,
#                 account_number=beneficiary_nuban,
#                 bank_code=beneficiary_bank_code,
#             )


#             if get_recipient.get("status") == "00":

#                 toClient = get_recipient.get("data")["name"]
#                 toClientBvn = get_recipient.get("data")["bvn"]
#                 toClientId = get_recipient.get("data")["clientId"]
#                 toSavings_or_sessions_id = get_recipient.get("data")["account"]["id"]


#                 get_signature = f"{fromAccount}{beneficiary_nuban}"
#                 signature_code = hashlib.sha512(str(get_signature).encode("utf-8") ).hexdigest()

#                 payload = {
#                     "fromSavingsId": fromSavingsId,
#                     "amount": float(amount),
#                     "toAccount": beneficiary_nuban,
#                     "fromBvn": user_bvn,
#                     "signature": signature_code,
#                     "fromAccount": fromAccount,
#                     "toBvn": toClientBvn,
#                     "remark": narration,
#                     "fromClientId": fromClientId,
#                     "fromClient": fromClient,
#                     "toKyc": "99",
#                     "reference": reference,
#                     "toClientId": toClientId,
#                     "toClient": toClient,
#                     "toSession": toSavings_or_sessions_id,
#                     "transferType": transfer_type,
#                     "toBank": beneficiary_bank_code,
#                     "toSavingsId": toSavings_or_sessions_id
#                 }


#                 try:
#                     response = requests.request(
#                         "POST", url=url, headers=cls.headers, params=cls.params, json=payload)

#                     print(response.json())
#                     return response.json()

#                 except requests.exceptions.RequestException as e:
#                     return {
#                         "error": "VFD Request Broken",
#                         "message": f"{e}"
#                     }

#             return {
#                 "error": "No Recipient",
#                 "message": f"{get_recipient}"
#             }


#     @classmethod
#     def vfd_transaction_verification_handler(cls, reference):

#         filter_url = f"wallet2/transactions"
#         url = cls.base_url + filter_url

#         cls.params["reference"] = reference

#         response = requests.request(
#             "GET", url=url, headers=cls.headers, params=cls.params
#         )

#         {
#             "status": "00",
#             "message": "Successful Transaction Retrieval",
#             "data": {
#                 "TxnId": "LGLP-VFD-33eac366-21ee-48ea-9850-f1fd2dc3bddf",
#                 "amount": "30.0",
#                 "accountNo": "**********",
#                 "transactionStatus": "00",
#                 "transactionDate": "2022-07-15 13:37:39.0",
#                 "toBank": "999999",
#                 "fromBank": "999999",
#                 "sessionId": "",
#                 "bankTransactionId": "*********"
#             }
#         }
#         resp = response.json()
#         print(resp)

#         return resp


#     @classmethod
#     def get_vfd_float_balance(cls, account_number=None):

#         filter_url = "wallet2/account/enquiry"
#         url = cls.base_url + filter_url

#         cls.params["accountNumber"] = account_number

#         response = requests.request(
#             "GET", url=url, headers=cls.headers, params=cls.params
#         )

#         print(response.json())
#         resp = response.json()

#         if resp.get("status") == "00":
#             vfd_balance = float(resp.get("data").get("accountBalance"))
#         else:
#             vfd_balance = None
        
#         return vfd_balance


#     @classmethod
#     def create_corporate_account(cls, rc_number, company_name, incorp_date, bvn):

#         filter_url = "wallet2/corporateclient/create"
#         url = cls.base_url + filter_url


#         payload = {
#             "rcNumber": f"{rc_number}",
#             "companyName": f"{company_name}",
#             "incorporationDate": f"{incorp_date}",
#             "bvn": f"{bvn}"
#         }



#         response = requests.request(
#             "POST", url=url, json=payload, headers=cls.headers, params=cls.params
#         )

#         print(response.json())
#         resp = response.json()
#         return resp

#         # if resp.get("status") == "00":
#         #     vfd_balance = float(resp.get("data").get("accountBalance"))
#         # else:
#         #     vfd_balance = None
        
#         # return vfd_balance