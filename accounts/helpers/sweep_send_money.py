from django.http import HttpRequest
import requests
from datetime import datetime
from main.models import User, ConstantTable
from accounts.models import MerchantDisbursements, WalletSystem
import pytz
from django.conf import settings
from main.helper.logging_utils import log_info

TIMEZONE = pytz.timezone(settings.TIME_ZONE)
BASEURL = settings.BASE_URL

def send_money(sweep_instance):
    from send_money.views import InternalFetchAccountName, InternalSendMoneyBankAccountAPIView

    total_amount: float
    total_amount_with_charge: float
    email = sweep_instance.user.email
    merchant = User.objects.get(email=email)
    if not merchant:    # should never happen
        return
    wallet = WalletSystem.objects.get(user=merchant)
    wallet_balance = wallet.available_balance
    min_balance = None
    sweep_limit = None
    try:
        min_balance = sweep_instance.min_balance
    except Exception as e:
        pass
    try:
        sweep_limit = sweep_instance.sweep_limit
    except Exception as e:
        pass

    if min_balance:
        if wallet_balance < min_balance:
            return "empty"

        withdrawable = wallet_balance - min_balance
        if sweep_limit:
            if withdrawable > sweep_limit:
                total_amount = sweep_limit
            else:
                total_amount = withdrawable
        else:
            total_amount = withdrawable
    else:
        withdrawable = wallet_balance
        if sweep_limit:
            if withdrawable > sweep_limit:
                total_amount = sweep_limit
            else:
                total_amount = withdrawable
        else:
            total_amount = withdrawable
    transaction_charge = ConstantTable.calculate_send_money_transaction_charge(amount=total_amount)
    total_amount_with_charge = total_amount + transaction_charge
    from_wallet_type = "COLLECTION"
    account_obj = WalletSystem.get_wallet(merchant, from_wallet_type)

    # fetch account details based on bank code and account number
    bank_code = sweep_instance.bank_code
    account_number = sweep_instance.account_number
    data = {
        "account_number": account_number,
        "bank_code": bank_code
    }
    IFAN = InternalFetchAccountName()
    account_details = IFAN.parse_details(data)

    payload = {
        "from_wallet_type": from_wallet_type,
        "data": {
            "account_number": account_number,
            "Account_name": account_details['data']['account_name'],
            "bank_code": bank_code,
            "bank_name": "",
            "amount": total_amount_with_charge,
            "narration": "Merchant sweep",
            "is_beneficiary": False,
            "save_beneficiary": True,
            "remove_beneficiary": False,
            "is_recurring": False,
            "ledger_commissions": 20,
            "commission_type": None
        },
        "total_amount": total_amount,
        "total_amount_with_charge": total_amount_with_charge,
        "transaction_pin": "1234"
    }
    check_balance = User.check_sender_balance(user=merchant, amount_with_charge=total_amount_with_charge,
                                              account_obj=account_obj)
    # last check
    if check_balance:
        # send money
        url = BASEURL + '/send/internal_send_money_bank_account/'
        response = requests.post(url, payload)
        log_info(str(response))

    # change merchant disbursement status to DORMANT
    sweep_instance.status = 'DORMANT'
    sweep_instance.previous_runtime = TIMEZONE.localize(datetime.now())
    sweep_instance.save()
