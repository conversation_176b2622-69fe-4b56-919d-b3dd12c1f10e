from celery import uuid

# from accounts.models import WovenAccountSystem
from django.conf import settings
from time import sleep
import requests
import json
import uuid

from main.models import ConstantTable
from main.helper.logging_utils import log_info


class Woven:
    base_url = "https://api.woven.finance/v2/api/"
    headers = {"api_secret": settings.WOVEN_API_KEY}
    # def __init__(self):

    @classmethod
    def create_reserved_account(cls, account_name, retry_count):
        filter_url = "reserved_vnuban"
        url = cls.base_url + filter_url
        payload = {"account_name": account_name, "collection_bank": "000017"}
        
        for _ in range(retry_count):
            response = requests.request(
                "POST", url=url, headers=cls.headers, data=payload
            )
            res = json.loads(response.text)
            if res["status"] == "success":
                break
        else:
            res = json.loads(response.text)

        # print(res)
        return res

    @classmethod
    def initiate_payout(
        cls,
        beneficiary_account_name,
        beneficiary_nuban,
        beneficiary_bank_code,
        source_account,
        narration,
        amount,
        reference
    ):
        filter_url = "payouts/request?command=initiate"
        url = cls.base_url + filter_url
        pin = settings.WOVEN_PAYOUT_PIN


        payload = {
            "source_account": source_account,
            "PIN": pin,
            "beneficiary_account_name": beneficiary_account_name,
            "beneficiary_nuban": beneficiary_nuban,
            "beneficiary_bank_code": beneficiary_bank_code,
            "bank_code_scheme": "NIP",
            "currency_code": "NGN",
            "narration": f"{narration}",
            "amount": f"{amount}",
            "reference": reference,
            "callback_url": f"{settings.BASE_URL}/accounts/callback/woven_send/",
        }

        response = requests.request("POST", url=url, headers=cls.headers, json=payload)
        res = response.json()
        log_info(str(res))
        return res

    @classmethod
    def woven_verify_funding_transaction(cls, transaction_reference, user_nuban, amount):

        filter_url = "transactions"
        url = cls.base_url + filter_url
        # pin = settings.WOVEN_PAYOUT_PIN

        headers = {'api_secret' : settings.WOVEN_API_KEY}

        params = {"unique_reference": transaction_reference}



        response = requests.request("GET", url, headers=headers, params=params)
        res = response.json()

        if res["status"] == "success" \
            and len(res["data"]["transactions"]) != 0 \
                and res["data"]["transactions"]["nuban"] == user_nuban \
                    and res["data"]["transactions"]["amount"] == amount:

            return True
        else:
            return False


    @classmethod
    def woven_verify_payout_transaction(cls, reference):

        filter_url = "merchant/payouts" 
        url = cls.base_url + filter_url

        log_info(str(url))

        headers = {'api_secret' : settings.WOVEN_API_KEY}

        params = {"payout_reference": reference}

        response = requests.request("GET", url, headers=headers, params=params)

        log_info(str(response.json()))
        return response.json()

    @classmethod
    def get_woven_balance(cls, source_account) -> dict:
        """The function returns details of woven balance"""

        url = f"{cls.base_url}/reserved_vnuban/{source_account}"

        headers = {'api_secret' : settings.WOVEN_API_KEY}

        response = requests.request("GET", url, headers=headers)

        log_info(str(response.json()))
        resp = response.json()

        if resp.get("status") == "success":
            woven_balance = float(resp.get("data").get("available_balance"))
        else:
            woven_balance = None
        
        return woven_balance
