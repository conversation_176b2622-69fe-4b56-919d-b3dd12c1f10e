from rest_framework import authentication
from rest_framework import exceptions
from main.helper.logging_utils import log_warning, log_info


class CustomTokenAuthentication(authentication.TokenAuthentication):
    keyword = 'Bearer'


class CustomTokenAuthenticationWithKeyword(authentication.TokenAuthentication):
    keyword = 'Token'


class APIKeyAuthentication(authentication.BaseAuthentication):
    """
    API Key authentication for Django REST Framework.

    Clients should authenticate by passing the API key in the "Authorization"
    HTTP header, prepended with the string "Api<PERSON>ey ".  For example:

        Authorization: ApiKey ak_1234567890abcdef:secret_key_here
    """
    keyword = 'ApiK<PERSON>'

    def authenticate(self, request):
        """
        Authenticate the request and return a two-tuple of (user, api_key).
        """
        auth_header = request.headers.get("Authorization").split()

        if not auth_header or auth_header[0].lower() != self.keyword.lower():
            return None

        if len(auth_header) == 1:
            msg = 'Invalid API key header. No credentials provided.'
            raise exceptions.AuthenticationFailed(msg)
        elif len(auth_header) > 2:
            msg = 'Invalid API key header. AI key string should not contain spaces.'
            raise exceptions.AuthenticationFailed(msg)

        try:
            api_key_string = auth_header[1]
        except UnicodeError:
            msg = 'Invalid API key header. API key string should not contain invalid characters.'
            raise exceptions.AuthenticationFailed(msg)

        return self.authenticate_credentials(api_key_string)

    def authenticate_credentials(self, api_key_string):
        """
        Authenticate the API key string and return (user, api_key).
        """
        # Import here to avoid circular imports
        from main.models import APIKey

        # Parse the API key string (format: ak_xxxxx:secret)
        try:
            key_id, secret_key = api_key_string.split(':', 1)
        except ValueError:
            raise exceptions.AuthenticationFailed('Invalid API key format. Expected format: ak_xxxxx:secret')

        # Validate key_id format
        if not key_id.startswith('ak_'):
            raise exceptions.AuthenticationFailed('Invalid API key format. Key ID must start with "ak_"')

        # Get API key from database
        try:
            api_key = APIKey.objects.select_related('user').get(key_id=key_id)
        except APIKey.DoesNotExist:
            raise exceptions.AuthenticationFailed('Invalid API key')

        # Verify the secret key
        if not api_key.verify_key(secret_key):
            log_warning(f"Failed API key authentication attempt for key {key_id}")
            raise exceptions.AuthenticationFailed('Invalid API key')

        # Check if user is active
        if not api_key.user.is_active:
            raise exceptions.AuthenticationFailed('User account is disabled')

        # Check rate limiting
        is_allowed, current_count, reset_time = api_key.check_rate_limit()
        if not is_allowed:
            reset_time_str = reset_time.strftime('%Y-%m-%d %H:%M:%S UTC') if reset_time else 'unknown'
            raise exceptions.Throttled(
                detail=f'Rate limit exceeded. Limit: {api_key.rate_limit_requests} requests per {api_key.rate_limit_window} seconds. Reset at: {reset_time_str}'
            )

        # Increment rate limit counter
        api_key.increment_rate_limit()

        # Update usage statistics
        api_key.update_usage()
        user = api_key.user

        log_info(f"Successful API key authentication for user {user.email} with API Key")

        return user, api_key

