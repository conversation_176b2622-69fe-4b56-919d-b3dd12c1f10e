from django.contrib.postgres.fields import Array<PERSON>ield
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.db import models
from django.conf import settings

from admin_dashboard.helpers.helpers import generate_random_3_digit_number, generate_referer_code
from main.models import User
from accounts.models import Transaction, WalletSystem

import uuid, uuid, ast, datetime, json


# Create your models here.

class BunchOfBalance(models.Model):
    balance_name = models.CharField(max_length=300, blank=True, null=True)
    amount = models.FloatField(default=0.00)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

class Zone(models.Model):
    name = models.CharField(max_length=1000, blank=True, null=True, unique=True)

    def __str__(self):
        return self.name


class Branch(models.Model):
    zone = models.ForeignKey(Zone, blank=True, null=True, on_delete=models.CASCADE)
    branch_name = models.CharField(max_length=500, blank=True, null=True)
    reserved_terminals = models.CharField(max_length=1000, default='[]')

    class Meta:
        unique_together = ('zone','branch_name')

    def __str__(self):
        return self.branch_name


class SalesRep(models.Model):
    
    TYPE_OF_REP_CHOICES = [
        ("REFERER", "REFERER"),
        ("RO", "RO"),
    ]

    sales_rep = models.ForeignKey(User, on_delete=models.CASCADE)
    sales_rep_code = models.CharField(max_length=10, null=True, blank=True)
    transfers_comm = models.FloatField(default=20)
    cash_out_comm = models.FloatField(default=20)
    rep_type = models.CharField(max_length=10, choices=TYPE_OF_REP_CHOICES, default="REFERER")
    # tran_comm = models.FloatField(default=20)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name="sales_rep", null=True, blank=True)
    is_branch_head = models.BooleanField(default=False)
    reserved_terminals = models.CharField(max_length=1000, default='[]')

    def __str__(self):
        return self.sales_rep.email

    def add_reserved_terminal(self, terminal_id):
        reserved = ast.literal_eval(self.reserved_terminals)
        reserved.append(terminal_id)
        self.reserved_terminals = str(reserved)

    def remove_reserved_terminal(self, terminal_id):
        reserved = ast.literal_eval(self.reserved_terminals)
        reserved.remove(terminal_id)
        self.reserved_terminals = str(reserved)

    def get_reserved_terminal_count(self):
        return len(ast.literal_eval(self.reserved_terminals))

    def get_reserved_terminals(self):
        return ast.literal_eval(self.reserved_terminals)

    def save(self, *args, **kwargs):
        if self.sales_rep_code is None:
            if self.rep_type == "REFERER":
                self.sales_rep_code = generate_referer_code()
            else:
                self.sales_rep_code = generate_random_3_digit_number()
        super(SalesRep, self).save(*args, **kwargs)

    @property
    def ussd_code(self):
        service_code = settings.MARKETING_USSD_CODE
        if "*" in service_code and "#" in service_code:
            idx = service_code.index("#")
            code = "*" + self.sales_rep_code
            _service_code = service_code[:idx] + code + service_code[idx:]
            return _service_code

        return self.sales_rep_code

    @classmethod
    def settle_sales_rep_commisson(cls, sales_rep_code, transaction_type, transaction_instance, agent_instance):
        if transaction_type == "SEND_BANK_TRANSFER":
            rep_commission="http://127.0.0.1:8000/api/admin/float(settings.SALES_REP_TRANSFER_COMMISSION)"

        elif transaction_type == "CARD_WITHDRAW_FUND":
            rep_commission = float(settings.SALES_REP_CARD_WITHDRAW_COMMISSION)

        sales_rep = SalesRep.objects.filter(sales_rep_code=sales_rep_code).last()

        SalesRepCommissionLog.objects.create(
            sales_rep = sales_rep,
            agent = agent_instance,
            transaction_instance = transaction_instance,
            commission = rep_commission
        )

        get_rep = SalesRep.objects.filter(sales_rep_code=sales_rep_code).last()
        if get_rep:
            get_commissions_wallet = WalletSystem.objects.filter(wallet_type="COMMISSIONS").last()
            if get_commissions_wallet:
                get_commissions_wallet.available_balance += rep_commission
                get_commissions_wallet.save()
            else:
                pass
                # new_commissions_wallet = WalletSystem.objects.create(
                #     user = cls.sales_rep,
                #     wallet_type = "COMMISSIONS"
                # )

                # new_commissions_wallet.available_balance += rep_commission
                # new_commissions_wallet.save()

        else:
            pass

class SalesRepCommissionLog(models.Model):
    sales_rep = models.ForeignKey(SalesRep, on_delete=models.CASCADE)
    agent = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction_instance = models.ForeignKey(Transaction, on_delete=models.Model, null=True, blank=True)
    commission = models.FloatField(default=0.00)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class Stock(models.Model):
    LibertyPayPlus = "LIBERTYPAYPLUS"
    HoirzonPay = "HORIZONPAY"
    NineNairaPoint = "9NAIRAPOINT"
    NPSB = "NPSB"
    Paydia = "PAYDIA"

    DEVICE_NAME_TYPES_CHOICES = [
        (LibertyPayPlus, "LIBERTYPAYPLUS"),
        (HoirzonPay, "HOIRZONPAY"),
        (NineNairaPoint, "9NAIRAPOINT"),
        (NPSB, "NPSB"),
        (Paydia, "PAYDIA"),
    ]
    STATUS_CHOICES = [
        ('ACTIVE', "ACTIVE"),
        ('INACTIVE', "INACTIVE"),
        ('SUSPENDED', "SUSPENDED"),
        ('SUSPENDED || TO-BE-RECOVERED', "SUSPENDED || TO-BE-RECOVERED")
    ]

    STOCK_LOCATION_CHOICES = [
        ('HEAD_OFFICE', "HEAD_OFFICE"),
        ('IN_STATE', "IN_STATE"),
        ('AT_BRANCH', "AT_BRANCH"),
        ('IN_TRANSIT', "IN_TRANSIT"),
        ('WITH_RO', "WITH_RO"),
        ('WITH_AGENT', "WITH_AGENT")
    ]


    PAYMENT_STATUS_CHOICES = (
        ("PAID", "PAID"),
        ("PENDING", "PENDING"),
        ("NOT_PAID", "NOT_PAID")
    )

    device_name = models.CharField(max_length=300, choices=DEVICE_NAME_TYPES_CHOICES)
    serial_number = models.CharField(max_length=300)
    model_number = models.CharField(max_length=300)
    terminal_id = models.CharField(max_length=300)
    is_assigned = models.BooleanField(default=False)
    date_assigned = models.DateTimeField(null=True)
    is_reserved = models.BooleanField(default=False)
    is_active = models.BooleanField(default=False)
    date_added = models.DateTimeField(auto_now_add=True)
    sold_by = models.ForeignKey(SalesRep, blank=True, null=True, on_delete=models.SET_NULL)
    payment_method = models.CharField(max_length=300, null=True, blank=True)
    assigned_to = models.OneToOneField(User, blank=True, null=True, on_delete=models.SET_NULL)
    status = models.CharField(max_length=200, choices=STATUS_CHOICES, default='INACTIVE')
    suspension_count = models.IntegerField(default=0)
    stock_location = models.CharField(max_length=255, default='HEAD_OFFICE', choices=STOCK_LOCATION_CHOICES)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, null=True, blank=True)
    zone = models.ForeignKey(Zone, on_delete=models.CASCADE, null=True, blank=True)
    stock_location_evidence = models.FileField(blank=True, null=True)
    payment_status = models.CharField(max_length=255, choices=PAYMENT_STATUS_CHOICES, default="NOT_PAID")
    comment = models.CharField(max_length=255, blank=True, null=True)
    delivery_requested = models.BooleanField(default=False)

    # class Meta:
    #     unique_together = ('zone',)

    def __str__(self):
        return self.device_name

    def save(self, *args, **kwargs):
        if self.status == 'ACTIVE':
            self.is_active = True
        elif self.status == 'INACTIVE':
            self.is_active = False
        elif self.status == 'SUSPENDED':
            self.is_active = False
        super(Stock, self).save(*args, **kwargs)


class TerminalTypePrice(models.Model):
    terminal_type = models.CharField(max_length=200)
    price = models.FloatField(default=0.00)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    

    def __str__(self):
        return f"{self.stock.device_name}"
    

class StockHistory(models.Model):
    ASSIGNED = "ASSIGNED"
    PICKED_UP = "PICKED_UP"
    UN_ASSIGNED = "UN_ASSIGNED"
    SUSPENDED = "SUSPENDED"
    DORMANT = "DORMANT"

    STOCK_STATUS_TYPE = [
        (ASSIGNED, "ASSIGNED"),
        (PICKED_UP, "PICKED_UP"),
        (UN_ASSIGNED, "UN_ASSIGNED"),
        (SUSPENDED, "SUSPENDED"),
        (DORMANT, "DORMANT")
        ]

    STOCK_IN = "STOCK_IN"
    STOCK_OUT = "STOCK_OUT"

    ACTION_TYPE_CHOICES = [
        (STOCK_IN, "STOCK_IN"),
        (STOCK_OUT, "STOCK_OUT")
    ]

    action = models.CharField(max_length=200, choices=ACTION_TYPE_CHOICES)
    admin = models.ForeignKey(User, on_delete=models.CASCADE)
    sales_repr = models.ForeignKey(SalesRep, related_name="stock_history", on_delete=models.PROTECT, blank=True, null=True)
    stock = models.ForeignKey(Stock,related_name="history", on_delete=models.CASCADE)
    batch_id = models.CharField(max_length=300, default=uuid.uuid4)
    batch_date_created = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=200, choices=STOCK_STATUS_TYPE, default="UN_ASSIGNED")
    date_created = models.DateTimeField(auto_now_add=True)
    approved_date = models.DateTimeField(auto_now_add=True)
    pick_up_date = models.DateTimeField(auto_now_add=True)
    stocks = models.CharField(max_length=3000,blank = True, null = True)
    branch = models.ForeignKey(Branch, null=True, blank=True, on_delete=models.CASCADE)
    zone = models.ForeignKey(Zone, blank=True, null=True, on_delete=models.CASCADE)
    comment = models.CharField(max_length=255, blank=True, null=True)


class StockInTransit(models.Model):
    STOCK_LOCATION_CHOICES = [
        ('HEAD_OFFICE', "HEAD_OFFICE"),
        ('IN_STATE', "IN_STATE"),
        ('AT_BRANCH', "AT_BRANCH"),
        ('IN_TRANSIT', "IN_TRANSIT"),
        ('WITH_RO', "WITH_RO"),
        ('WITH_AGENT', "WITH_AGENT")
    ]

    stocks = ArrayField(
                        models.CharField(max_length=100000),blank = True, null = True
                        )      
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE)
    zone = models.ForeignKey(Zone, on_delete=models.CASCADE)
    comment = models.CharField(max_length=255)
    stock_location = models.CharField(max_length=255, choices=STOCK_LOCATION_CHOICES, default="IN_TRANSIT", blank=True, null=True)
    stock_location_evidence = models.FileField(blank=True, null=True)
    date_started = models.DateTimeField(auto_now_add=True)


class TerminalRequest(models.Model):
    ASSIGNED = "ASSIGNED"
    PICKED_UP = "PICKED_UP"
    AWAITING_PICKUP = "AWAITING_PICKUP"

    STOCK_STATUS_TYPE = [
        (ASSIGNED, "ASSIGNED"),
        (PICKED_UP, "PICKED_UP"),
        (AWAITING_PICKUP, "AWAITING_PICKUP"),
    ]

    DELIVERY_STATUS_CHOICES = [
         (AWAITING_PICKUP, "AWAITING_PICKUP"),
    ]

    STOCK_IN = "STOCK_IN"
    STOCK_OUT = "STOCK_OUT"

    ACTION_TYPE_CHOICES = [
        (STOCK_IN, "STOCK_IN"),
        (STOCK_OUT, "STOCK_OUT")
    ]

    requested_by = models.CharField(max_length=200, choices=ACTION_TYPE_CHOICES)
    stock = models.ForeignKey(Stock,related_name="sales_history", on_delete=models.CASCADE)
    sales_rep = models.ForeignKey(SalesRep, related_name="requested_by", on_delete=models.CASCADE, null=True)
    batch_id = models.UUIDField(default=uuid.uuid4, editable=False)
    batch_date_created = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=200, choices=STOCK_STATUS_TYPE)
    date_created = models.DateTimeField(auto_now_add=True)
    approved_date = models.DateTimeField(auto_now_add=False)
    pick_up_date = models.DateTimeField(auto_now_add=False)
    requested_payload = models.CharField(max_length=2300, null=True, blank=False)
    requested_terminal_num = models.IntegerField(null=True, blank=False)


class ProspectiveAgent(models.Model):
    Jotform = "Jotform"
    Mobile_App = "Mobile_App"
    WEB = "WEB"
    
    CREATED_SOURCE = [
        (Jotform, "Jotform"),
        (Mobile_App, "Mobile_App"),
        (WEB, "WEB")
    ]
    
    PROSPECT = "PROSPECT"
    ON_HOLD = "ON_HOLD"
    NOT_INTERESTED = "NOT_INTERESTED"
    PURCHASED = "PURCHASED"    
    
    PROSPECT_ACTION_STATUS = [
        (PROSPECT, "PROSPECT"),
        (ON_HOLD, "ON_HOLD"),
        (NOT_INTERESTED, "NOT_INTERESTED"),
        (PURCHASED, "PURCHASED")
    ]

    MALE = "MALE"
    FEMALE = "FEMALE"

    GENDER_OPTIONS = [
        (MALE, "MALE"),
        (FEMALE, "FEMALE")
    ]

    YES = "YES"
    NO = "NO"

    YES_NO_OPTIONS = [
        (YES, "YES"),
        (NO, "NO")
    ]

    MERCHANT = "MERCHANT"
    AGENT = "AGENT"
    PERSONAL = "PERSONAL"
    SALES_REP = "SALES_REP"
    ADMIN = "ADMIN"
    LOTTO_AGENT = "LOTTO_AGENT"
    LIBERTY_RETAIL = "LIBERTY_RETAIL"



    AGENT_TYPE_CHOICES = [
        (MERCHANT, "MERCHANT"),
        (AGENT, "AGENT"),
        (PERSONAL, "PERSONAL"),
        (SALES_REP, "SALES_REP"),
        (ADMIN, "ADMIN"),
        (LOTTO_AGENT, "LOTTO_AGENT"),
        (LIBERTY_RETAIL, "LIBERTY_RETAIL")
    ]

    
    sales_rep = models.ForeignKey(SalesRep, related_name="sales_agent", on_delete=models.CASCADE)
    first_name = models.CharField(max_length=500)
    last_name = models.CharField(max_length=500)
    phone_number = models.CharField(max_length=22, unique=True)
    email = models.EmailField(unique=True)
    source_account = models.CharField(max_length=200, choices=CREATED_SOURCE)
    prospect_action =models.CharField(max_length=200, choices=PROSPECT_ACTION_STATUS)
    prospect_status =models.CharField(max_length=200)
    first_address = models.CharField(max_length=2200)
    second_address = models.CharField(max_length=2200, null=True, blank=True)
    house_number = models.CharField(max_length=10, null=True, blank=True)
    landmark = models.CharField(max_length=100, null=True, blank=True)
    street = models.CharField(max_length=2200, null=True, blank=True)
    city = models.CharField(max_length=2200)
    state = models.CharField(max_length=2200)
    lga = models.CharField(max_length=2200)
    postal_zipcode = models.CharField(max_length=2200, null=True, blank=True)
    bvn_number = models.CharField(max_length=22)
    bvn_payload = models.TextField(null=True, blank=False)
    business_name = models.CharField(max_length=2200)
    device = models.CharField(max_length=2200)
    unique_id = models.CharField(max_length=2200)
    pick_up_option = models.CharField(max_length=2200)
    next_of_kin_name = models.CharField(max_length=2200)
    next_of_kin_phone_number = models.CharField(max_length=2200)
    next_of_kin_relationship = models.CharField(max_length=2200)
    next_of_kin_address = models.CharField(max_length=2200, null=True, blank=True)
    location = models.CharField(max_length=2200)
    profile_picture = models.CharField(max_length=30000, null=True, blank=True)
    date = models.DateField(auto_now_add=True)
    gender = models.CharField(max_length=10, choices=GENDER_OPTIONS, blank=True, null=True)
    is_above_18 = models.CharField(max_length=10, choices=YES_NO_OPTIONS, blank=True, null=True)
    onboarding_stage_level = models.IntegerField(default=0) # stage1 for personal form, stage2 for guarantor1 & stage3 for guarantor2
    signature = models.CharField(max_length=30000, null=True, blank=True)
    guarantor_one_first_name = models.CharField(max_length=2300, null=True, blank=True)
    guarantor_one_last_name = models.CharField(max_length=2300, null=True, blank=True)
    guarantor_one_email = models.EmailField(max_length=2300, null=True, blank=True)
    guarantor_one_phone_number = models.CharField(max_length=20, null=True, blank=True)
    guarantor_one_street_address = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_one_state = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_one_city = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_one_postal_zipcode = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_one_bvn_number = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_one_profile_picture = models.CharField(max_length=30000, null=True, blank=True)
    guarantor_one_signature = models.CharField(max_length=30000, null=True, blank=True)   
    guarantor_two_first_name = models.CharField(max_length=2300, null=True, blank=True)
    guarantor_two_last_name = models.CharField(max_length=2300, null=True, blank=True)
    guarantor_two_email = models.EmailField(max_length=2300, null=True, blank=True)
    guarantor_two_phone_number = models.CharField(max_length=20, null=True, blank=True)
    guarantor_two_street_address = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_two_state = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_two_city = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_two_postal_zipcode = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_two_bvn_number = models.CharField(max_length=2500, null=True, blank=True)
    guarantor_two_profile_picture = models.CharField(max_length=30000, null=True, blank=True)
    guarantor_two_signature = models.CharField(max_length=30000, null=True, blank=True)
    agent_type = models.CharField(max_length=200, choices=AGENT_TYPE_CHOICES, blank=True, null=True)
    
    @staticmethod
    def format_number_from_back_add_234(phone_number) -> str:
        formatted_num = phone_number[-10:]
        if formatted_num[0] == "0":
            return None
        else:
            return "234" + formatted_num



class StockRequest(models.Model):
    REQUEST_TYPES = [
        ('PAID', "PAID"),
        ('UNPAID', "UNPAID")
    ]
    
    STOCK_REQUEST_LOCATION_CHOICES = [
        ('HEAD_OFFICE', "HEAD_OFFICE"),
        ('IN_STATE', "IN_STATE"),
        ('AT_BRANCH', "AT_BRANCH"),
        ('IN_TRANSIT', "IN_TRANSIT"),
        ('WITH_RO', "WITH_RO"),
        ('WITH_AGENT', "WITH_AGENT")
    ]

    sales_rep = models.ForeignKey(SalesRep, related_name="stock_request", on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, related_name="stock_request", on_delete=models.CASCADE, null=True, blank=True)
    request_type = models.CharField(max_length=100, choices=REQUEST_TYPES, default="UNPAID")
    liberty_pay_plus = models.IntegerField(default=0)
    hoirzon_pay = models.IntegerField(default=0)
    nine_naira_point = models.IntegerField(default=0)
    npsb = models.IntegerField(default=0)
    paydia = models.IntegerField(default=0)
    request_id = models.CharField(max_length=200, default=uuid.uuid4)
    request_msg = models.CharField(max_length=200, default="")
    pending_approval = models.BooleanField(default=True)
    is_approved = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    payment_made = models.BooleanField(default=False)
    payment_meta = models.CharField(max_length=1000, default="")
    stock_request_location = models.CharField(max_length=255, default="HEAD_OFFICE", choices=STOCK_REQUEST_LOCATION_CHOICES)
    zone = models.ForeignKey(Zone, blank=True, null=True, on_delete=models.CASCADE)
    stock_location_evidence = models.FileField(blank=True, null=True)
    is_disapproved = models.BooleanField(default=False, blank=True, null=True)
    disapproval_note = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return self.request_id


class AssignedTerminal(models.Model):

    ASSIGNED = "ASSIGNED"
    UN_ASSIGNED = "UN_ASSIGNED"
    PICKED_UP = "PICKED_UP"
 
    STOCK_STATUS_TYPE = [
        (ASSIGNED, "ASSIGNED"),
        (PICKED_UP, "PICKED_UP"),
        (UN_ASSIGNED, "UN_ASSIGNED")
    ]
    assigned_by = models.ForeignKey(User, on_delete=models.CASCADE)
    sales_rep = models.ForeignKey(SalesRep, related_name="assigned_terminal", on_delete=models.CASCADE)
    stock = models.ForeignKey(Stock, related_name="assigned_terminal", on_delete=models.CASCADE)
    status = models.CharField(max_length=200, choices=STOCK_STATUS_TYPE, default=ASSIGNED)
    request_id = models.CharField(max_length=300)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.stock.terminal_id
    
class AgentTerminal(models.Model):
    agent = models.ForeignKey(ProspectiveAgent, related_name="agent_terminal", on_delete=models.CASCADE)
    terminal_id = models.CharField(max_length=300, unique=True)
    

class Dispute(models.Model):
    DISPUTE_TYPE_CHOICES = (
        ("TRANSFER","TRANSFER"),
        ("CASH_OUT", "CASH_OUT"),
        ("BILLS_AND_AIRTIME", "BILLS_AND_AIRTIME")
    )

    STATUS_CHOICES = (
        ("RESOLVED","RESOLVED"),
        ("NEED_RESPONSE", "NEED_RESPONSE")
    )

    DISPUTE_ISSUE_CHOICES = (
        ("CUSTOMER_DISPENSE_ERROR", "CUSTOMER_DISPENSE_ERROR"),
        ("CARD_TRANSFER", "CARD_TRANSFER"),
        ("CARD_PAYMENT", "CARD_PAYMENT"),
        ("WALLET_PAYMENT", "WALLET_PAYMENT"),
        ("WALLET_TRANSFER", "WALLET_TRANSFER"),
        ("AGENT_WALLET_NOT_FUNDED", "AGENT_WALLET_NOT_FUNDED"),
    )

    APPROVAL_STATUS_CHOICES = [
            ("ACCEPTED", "ACCEPTED"),
            ("DECLINED", "DECLINED"),
            ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    narration = models.CharField(max_length=1000, blank=True, null=True)
    support_id = models.UUIDField(default=uuid.uuid4, editable=False)
    dispute_type = models.CharField(max_length=100, choices=DISPUTE_TYPE_CHOICES, default="CASH_OUT")
    resolved_by = models.CharField(max_length=200, blank=True, null=True)
    customer_mobile_number = models.CharField(max_length=100, blank=True, null=True)
    customer_account_number = models.CharField(max_length=100, blank=True, null=True)
    receiver_account_number = models.CharField(max_length=100, blank=True, null=True)
    card_first_six_digits = models.CharField(max_length=100, blank=True, null=True)
    card_last_four_digits = models.CharField(max_length=100, blank=True, null=True)
    customer_name = models.CharField(max_length=255, blank=True, null=True)
    terminal_id = models.CharField(max_length=255, blank=True, null=True)
    is_resolved = models.BooleanField(default=False)
    respond_time = models.DateTimeField(auto_now=False, blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    stan = models.CharField(max_length=255, blank=True, null=True)
    pan = models.CharField(max_length=255, blank=True, null=True)
    issue = models.CharField(max_length=255, choices=DISPUTE_ISSUE_CHOICES, blank=True, null=True)
    transaction_date = models.DateField(blank=True, null=True)
    transaction_rrn = models.CharField(max_length=255, blank=True, null=True)
    requested_amount = models.IntegerField(default=0)
    dispensed_amount = models.IntegerField(default=0)
    approval_status = models.CharField(max_length=255, choices=APPROVAL_STATUS_CHOICES, blank=True, null=True)
    status = models.CharField(max_length=255, default="NEED_RESPONSE", null=True, blank=True)

    def save(self, *args, **kwargs):
        self.terminal_id = self.user.terminal_id
        self.pan = f"{self.card_first_six_digits}******{self.card_last_four_digits}"

        return super(Dispute, self).save(*args, **kwargs)
      

    def __str__(self):
        return self.dispute_type
    

    @staticmethod
    def admin_create_bulk_disputes(payload):
        disputes_list = payload

        for dispute in disputes_list:
            terminal_id = dispute.get("terminal_id", "")
            issue = dispute.get("issue")
            customer_name = dispute.get("customer_name")
            respond_time = dispute.get("respond_time")
            dispute_type = dispute.get("dispute_type", "")
            narration = dispute.get("narration", "")
            is_resolved = dispute.get("is_resolved")
            date_created = dispute.get("date_created")
            date_created = datetime.datetime.strptime(date_created, "%Y-%m-%d")
            respond_time = datetime.datetime.strptime(respond_time, "%Y-%m-%d")

            user = User.objects.filter(terminal_id=terminal_id).last()
            
            Dispute.objects.create(
                                   user=user,
                                   narration=narration,
                                   date_created=date_created,
                                   is_resolved = is_resolved,
                                   respond_time = respond_time,
                                   issue = issue,
                                   customer_name = customer_name,
                                   dispute_type = dispute_type
                                   )
            new_dispute = Dispute.objects.latest("date_created")
            new_dispute.date_created = date_created
            new_dispute.save()


class DisputeBatchUpload(models.Model):
    disputes = models.CharField(max_length=1000000, blank=True, null=True)
    date_uploaded = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.date_uploaded}"


 
class Terminal(models.Model):
    terminal_id = models.UUIDField(default=uuid.uuid4)

    def __str__(self):
        return self.terminal_id



class NewUploadDisputeTable(models.Model):
    pan = models.CharField(max_length=100, blank=True, null=True)
    stan = models.CharField(max_length=1000, blank=True, null=True)
    terminal_id = models.CharField(max_length=100, blank=True, null=True)
    dispense_type = models.CharField(max_length=150, blank=True, null=True)
    transaction_date = models.CharField(max_length=200, blank=True, null=True)
    retrieval_reference = models.CharField(max_length=100, blank=True, null=True)
    customer_account_number = models.CharField(max_length=100, blank=True, null=True)
    log_comments = models.CharField(max_length=100, blank=True, null=True)
    requested_amount = models.CharField(max_length=100, blank=True, null=True)
    dispensed_amount = models.CharField(max_length=100, blank=True, null=True)
    notification_sent = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.retrieval_reference if self.retrieval_reference else None

    def save(self, *args, **kwargs):
        # if self.notification_sent == False:
        #     from admin_dashboard.tasks import send_sms_and_email_on_success_dispute_add

        #     send_sms_and_email_on_success_dispute_add.apply_async(
        #         # queue="processbulksheet",
        #         queue="ussdbills",
        #         kwargs={
        #             "rrn": self.retrieval_reference
        #         }
        #     )

        return super(NewUploadDisputeTable, self).save(*args, **kwargs)




class StockRecovery(models.Model):
    STATUS_CHOICES = [
        ('TO-BE-RECOVERED', "TO-BE-RECOVERED"),
        ('RECOVERED', "RECOVERED")
    ]
    terminal_id = models.CharField(max_length=255)
    stock = models.ForeignKey(Stock, on_delete=models.CASCADE)
    recovered_by = models.ForeignKey(User, related_name="recovered_stock", on_delete=models.SET_NULL, null=True, blank=True)
    status = models.CharField(max_length=100, choices=STATUS_CHOICES, default="TO-BE-RECOVERED")
    date_recovered = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)


class WithdrawalBankDetails(models.Model):
    bank_name = models.CharField(max_length=255, default="")
    account_number = models.CharField(max_length=255, default="")
    account_name = models.CharField(max_length=255, default="")
    user = models.ForeignKey(User, related_name="bank_details", on_delete=models.CASCADE)

class AdminDashboardLogHistory(models.Model):
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

    MOBILE = "MOBILE"
    DESKTOP = "DESKTOP"

    STATUS_CHOICES = (
        (SUCCESS, "SUCCESS"),
        (FAILED, "FAILED")
        )

    DEVICE_CHOICES = (
        (MOBILE, "MOBILE"),
        (DESKTOP, "DESKTOP")
        )

    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    content_type = models.ForeignKey(ContentType, on_delete=models.SET_NULL, blank=True, null=True)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey()
    action_performed = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=255, choices=STATUS_CHOICES, blank=True, null=True)
    action_time = models.DateTimeField(auto_now_add=True)
    device = models.CharField(max_length=255, choices=DEVICE_CHOICES, blank=True, null=True)
    user_agent = models.CharField(max_length=2500, blank=True, null=True)

    def __str__(self):
        return f"{self.user.first_name} interracted with {self.content_object}"


class AgentActivityDumpData(models.Model):
    week_runs_count = models.IntegerField(default=0)
    date_added = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    date_updated = models.DateTimeField(auto_now=True, blank=True, null=True)


class ProspectiveAgentsVerifiedBvnTable(models.Model):
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    bvn_number = models.CharField(max_length=25, blank=True, null=True)
    uverify_payload = models.CharField(max_length=10000, blank=True, null=True)
    verification_successful = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class DevelopersEmail(models.Model):
    email = models.EmailField(max_length=150)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.email}"


class AdminUserRole(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)

    def __str__(self):
        return str(self.user.email)


class UserCreationJotForm(models.Model):

    USER_TYPE_CHOICES = (
        ("MERCHANT", "MERCHANT"),
        ("AGENT", "AGENT"),
        ("PERSONAL", "PERSONAL"),
        ("LOTTO_AGENT", "LOTTO_AGENT"),
        ("LIBERTY_RETAIL", "LIBERTY_RETAIL"),
        ("AJO_AGENT", "AJO_AGENT"),
        ("MERCHANT_AGENT", "MERCHANT_AGENT"),
        ("STAFF_AGENT", "STAFF_AGENT"),
        ("PROSPER_AGENT", "PROSPER_AGENT"),
        ("DMO_AGENT", "DMO_AGENT"),
        ("MICRO_SAVER", "MICRO_SAVER"),
        ("PHARMACIST", "PHARMACIST"),
    )

    GENDER_CHOICES = (
        ("MALE", "MALE"), ("FEMALE", "FEMALE")
    )

    MARITAL_STATUS_CHOICES = (
        ("MARRIED", "MARRIED"),
        ("SINGLE", "SINGLE"),
        ("DIVORCED", "DIVORCED")
    )

    phone_number = models.CharField(max_length=20)
    username = models.CharField(max_length=100)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    lga = models.CharField(max_length=200)
    nearest_landmark = models.CharField(max_length=200)
    street = models.CharField(max_length=200)
    user_type = models.CharField(max_length=50, choices=USER_TYPE_CHOICES, default="PERSONAL")
    business_name = models.CharField(max_length=200, blank=True, null=True)
    bvn_number = models.CharField(max_length=50)
    gender = models.CharField(max_length=200, choices=GENDER_CHOICES, default="MALE")
    email = models.EmailField()
    marital_status = models.CharField(max_length=100, choices=MARITAL_STATUS_CHOICES, default="SINGLE")
    payload_response = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.email


