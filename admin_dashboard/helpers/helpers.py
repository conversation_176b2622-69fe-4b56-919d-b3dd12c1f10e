from datetime import datetime, timedelta
from django.core.paginator import Paginator as django_core_paginator
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from sendgrid.helpers.mail import Mail, HtmlContent, Email, To, Content, Attachment, FileContent, FileName, FileType, Disposition
from sendgrid import SendGridAPIClient
from main.helper.api import *

from string import Template
import os
import uuid
import random
import requests
import base64, calendar
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical

# class Paginator:
#     @staticmethod
#     def paginate(request, queryset):
#         request_get_data = request.GET
#         page = request.GET.get("page", 1)
#         if page is None or page == "":
#             page = 1
#         elif int(page) < 0:
#             page = 1
#         else:
#             page = page

#         paginator = django_core_paginator(
#             queryset, int(request_get_data.get("size", 15))
#         )
#         requested_page = int(request_get_data.get("page", page))

#         verified_page = (
#             requested_page
#             if requested_page < paginator.num_pages
#             else paginator.num_pages
#         )

#         page = paginator.page(verified_page)

#         return page


class Paginator:

    @staticmethod
    def paginate(request, queryset, size=None):

        if size is None or isinstance(size, int) == False:
            size = 100
        else:
            size = size

        request_get_data = request.GET
    
        paginator = django_core_paginator(queryset.order_by("-id"), int(request_get_data.get('size', size)))
        requested_page = int(request_get_data.get('page', 1))

        verified_page = requested_page if requested_page < paginator.num_pages else paginator.num_pages

        page = paginator.page(verified_page)

        return page
        

    def paginate_list(request, queryset, size=None):

        if size is None or isinstance(size, int) == False:
            size = 100
        else:
            size = size

        request_get_data = request.GET
    
        paginator = django_core_paginator(queryset, int(request_get_data.get('size', size)))
        requested_page = int(request_get_data.get('page', 1))

        verified_page = requested_page if requested_page < paginator.num_pages else paginator.num_pages

        page = paginator.page(verified_page)

        return page




def generate_referer_code() -> str:
    """
    Generate a random 9 digit number
    """

    from admin_dashboard.models import SalesRep

    gen_uuid = str(uuid.uuid4()).replace('-', '').upper()

    generated_code = ''.join(random.choice(gen_uuid) for i in range(9))

    if SalesRep.objects.filter(sales_rep_code=generated_code).exists():
        return generate_referer_code()

    return generated_code


def generate_random_3_digit_number() -> str:
    """
    Generate a random 3 digit number
    """

    from admin_dashboard.models import SalesRep

    n = 3
    range_start = 10 ** (n - 1)
    range_end = (10**n) - 1
    generated_code =  str(random.randint(range_start, range_end))

    if generated_code == 666:
        return generate_random_3_digit_number()

    if SalesRep.objects.filter(sales_rep_code=generated_code).exists():
        return generate_random_3_digit_number()

    return generated_code





def get_percentage_diff(previous, current):
    try:
        percentage = abs(previous - current)/max(previous, current) * 100
    except ZeroDivisionError:
        percentage = float('inf')
        percentage = 0
    except TypeError:
        percentage = 0

    try:

        if current > previous:
            change = "up"
        elif previous > current:
            change = "down"
        else:
            change = "no change"

        return {
            "percentage": percentage,
            "change": change
        }
    except:
        return {
            "percentage": 0,
            "change": "none"
        }


#############################################################################################################################
# POS AGENTS SEND EMAIL AND SMS

def send_dan_email(email, message, email_subject, template, **kwargs):
    # SendGrid
    sg = SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)

    TEMPLATE_DIR = os.path.join("templates", template)
    html_temp = os.path.abspath(TEMPLATE_DIR)    

    with open(html_temp) as temp_file:
        template = temp_file.read()
    template = Template(template).safe_substitute(email=email, message=message)

    from_email = "<EMAIL>"  # Change to your verified sender
    to_email = email  
    subject = email_subject
    content = HtmlContent(template)

    mail = Mail(from_email, to_email, subject, content)

    try:
        files = kwargs["attach"]

        for f in files:
            path = default_storage.save('static/uploadfile', ContentFile(f.read()))  
            tmp_file = os.path.join(settings.MEDIA_ROOT, path)
        
            with open(tmp_file, "rb") as attached_file:
                file = attached_file.read()
                attached_file.close()
            encoded_file = base64.b64encode(file).decode()

            attachedFile = Attachment(
            FileContent(encoded_file),
            FileName(f"{f.name}/{datetime.now()}"),
            FileType(f.content_type),
            Disposition('attachment')
            )
            mail.attachment = attachedFile
    except KeyError:
        attach = " "

    response = sg.send(mail)

def send_pos_agent_email(email, template, email_subject, message, **kwargs):
    TEMPLATE_DIR = os.path.join("templates", template)
    tmp_file = os.path.abspath(TEMPLATE_DIR)
    tmp_file = os.path.join(settings.BASE_DIR, f"templates/{template}")

    attachments = []
    rm_files_list = []
    files = kwargs["files"]
    for file in files:
        imt = random.randint(0,1000)
        paths = default_storage.save(f'{settings.BASE_DIR}/media/agent_report/{imt}/{file}', ContentFile(file.read()))  
        tmp_files = f'{settings.BASE_DIR}/media/agent_report/{imt}/{file}' #os.path.join(settings.MEDIA_ROOT, paths)
        rm_files_list.append(tmp_files)

        with open(tmp_files, "rb") as code_file:
            d_file = code_file.read()
            code_file.close()

        encoded_file = base64.b64encode(d_file).decode()

        attachments.append(("attachment", (f"{file}", d_file)))

    with open(tmp_file) as template_file:
        template = template_file.read()
        template_file.close()

    template = Template(template).safe_substitute(email=email, message=message)

    try:
        message = requests.post(
                f"https://api.mailgun.net/v3/libertypayng.com/messages",
                auth=("api", f"{settings.MAILGUN_API_KEY}"),
                data={"from": "Liberty Pay <<EMAIL>>",
                    "to": email,
                    "subject": email_subject,
                    "html": template,
                    },
                    files=attachments
                    )
    except Exception as e:
        log_error(f"failed to send email: {e}", "admin.helpers.helpers.send_pos_agent_email")

    for path in rm_files_list:
        os.remove(path)


def send_dispute_email(
        message, email, email_subject, template, dispute_type, 
        account_number, support_id, card_details,
        date_created, customer_phone
    ):

    TEMPLATE_DIR = os.path.join("templates", template)
    html_temp = os.path.abspath(TEMPLATE_DIR)

    with open(html_temp) as temp_file:
        template = temp_file.read()

    template = Template(template).safe_substitute(
            email=email,
            message=message,
            dispute_type=dispute_type, account_number=account_number, 
            support_id=support_id,
            card_details = card_details,
            date_created = date_created, customer_phone=customer_phone
        )
    
    try:
        message = requests.post(
                f"https://api.mailgun.net/v3/libertypayng.com/messages",
                auth=("api", f"{settings.MAILGUN_API_KEY}"),
                data={"from": "Liberty Pay <<EMAIL>>",
                    "to": email,
                    "subject": email_subject,
                    "html": template,
                    })
    except Exception as e:
        log_error(f"failed to send email: {e}", "admin.helpers.helpers.send_dispute_email")
    

def send_sms_to_pos_agent(phone_number, message):
    url = settings.WHISPER_URL
    payload = json.dumps(
        {
            "receiver": f"{phone_number}",
            "template": f"{settings.AGENT_ACTIVITIES_SMS_TEMPLATE}",
            "place_holders": {
                "message": f"{message}",
            },
        }
    )
    headers = {
        # "Authorization": "Api_key 6tyghbhgfctrdxrg",
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }
    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        res = json.loads(response.text)
        return res
    except Exception as e:
        pass

## Send Disputes WhatsApp Notification
def notify_admin_whatsapp_on_disputes_log(phone_number, message): 

    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    whatsapp_payload = json.dumps(
            {
                "token": f"{settings.PICKY_ASSIST_TOKEN}",
                "priority ": "0",
                "application": "10",
                "data": [
                    {
                        "number": f"{phone_number}",
                        "message": f"Hello, \n{message}"
                    }
                ]
            }
        )
    whatsapp_Headers = {'Content-type': 'application/json'}

    whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
    whatsapp_res = json.loads(whatsapp_response.text)

    return "WhatsApp Notification sent out"

def format_phone_number(phone_number):
    if not phone_number.startswith("+234"):
        phone_number = "+234" + phone_number[1:]
        return phone_number


def get_transaction_queryset(self, model, q):
    transaction_qs = model.objects.filter(~q(transaction_type__in=["AIRTIME_PIN", "BILLS_AND_PAYMENT", "REVERSAL_BANK_TRANSFER", "SEND_BACK_TO_FLOAT_TRANSFER", "SEND_LIBERTY_COMMISSION"],
            transaction_leg="INTERNAL") &
            q(user__terminal_id__isnull=False) & q(amount__isnull=False, status="SUCCESSFUL")
        )

    return transaction_qs


def filter_by_date_two(request, user, datetime):
    date_filter_gte = request.GET.get("date_filter_gte")
    date_filter_lte = request.GET.get("date_filter_lte")

    if date_filter_gte and date_filter_lte is not None:
        start_date = datetime.strptime(date_filter_gte, "%Y-%m-%d")
        end_date = datetime.strptime(date_filter_lte, "%Y-%m-%d")
    else:
        start_of_all_agents = user.objects.order_by("date_joined").first().date_joined
        start_date = start_of_all_agents
        end_date = datetime.today()

    data = {
        "start_date": start_date,
        "end_date": end_date + timedelta(days=1)
        }

    return data


def date_utility(datetime):
    start_of_all_agents = datetime(2021, 1, 1)

    previous_day = timezone.now() - timedelta(days=1)

    date_today = timezone.now()

    if date_today.month > 1:
        previous_month_start = datetime(date_today.year, date_today.month-1, 1)
    else:
        previous_month_start = datetime(date_today.year, 12, 1)
    current_month_start = datetime(date_today.year, date_today.month, 1)
    previous_month_end = current_month_start + timedelta(days=-1)

    current_date = timezone.now().date()
    month_start = datetime(current_date.year, current_date.month, 1)
    year_start = datetime(current_date.year, 1, 1)
    year_end = datetime(current_date.year, 12, 31)

    week_start = date_today - timedelta(days=current_date.weekday())
    previous_week_end = week_start - timedelta(days=1)
    previous_week_start = previous_week_end - timedelta(days=previous_week_end.weekday())

    previous_year_start = datetime(date_today.year-1, 1, 1)
    previous_year_end = datetime(date_today.year-1, 12, 31)

    first_day, last_day = calendar.monthrange(date_today.year-1, date_today.month)
    previous_year_current_month_start = datetime(date_today.year-1, current_date.month, 1)
    previous_year_current_month_end = datetime(date_today.year-1, current_date.month, last_day)
    previous_year_current_following_month = current_date.month + 1
    date_from = datetime.now() - timedelta(days=1)
    month_ago = timezone.now() - timedelta(days=30)
    datetime_today_6am = datetime(date_today.year, date_today.month, date_today.day, 6, 0)
    previous_day_six_am = datetime_today_6am - timedelta(days=1)
    midnight_time = datetime(current_date.year, current_date.month, current_date.day)
    day_ago = timezone.now() - timedelta(hours=24)


    data = {
        "start_of_all_agents": start_of_all_agents,
        "start_of_all_transactions": start_of_all_agents,
        "previous_day": previous_day,
        "today": date_today + timedelta(days=1),
        "previous_month_end": previous_month_end,
        "previous_month_start": previous_month_start,
        "init_start": start_of_all_agents,
        "month_start": month_start,
        "year_start": year_start,
        "year_end": year_end,
        "week_start": week_start.date(),
        "previous_week_start": previous_week_start.date(),
        "previous_week_end": previous_week_end.date(),
        "previous_year_start": previous_year_start,
        "previous_year_end": previous_year_end,
        "previous_year_current_month_start": previous_year_current_month_start,
        "previous_year_current_month_end": previous_year_current_month_end,
        "previous_year_current_following_month": previous_year_current_following_month,
        "date_from": date_from,
        "month_ago": month_ago,
        "date_today": date_today,
        "previous_day_six_am": previous_day_six_am,
        "datetime_today_6am": datetime_today_6am,
        "midnight_time": midnight_time,
        "day_ago": day_ago,
        "current_date": current_date
    }

    return data



def send_agent_activity_email(message, file, file_name, email_subject, air=False):
    template_dir = os.path.join(settings.BASE_DIR, "templates/agent_activities.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()
    
    template = Template(template).safe_substitute(message=message, email_subject=email_subject)
    # attachment = kwargs["file"]
    if air:
        if settings.ENVIRONMENT == "development":
            data = {
                "from": "Liberty Pay <<EMAIL>>",
                "to": "<EMAIL>",
                # "cc": "<EMAIL>",
                # "cc": "<EMAIL>",
                "subject": email_subject,
                "html": template,
            }

        else:

            data = {
                "from": "Liberty Pay <<EMAIL>>",
                "to": "<EMAIL>",
                "cc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "subject": email_subject,
                "html": template,
            }
    else:
        if settings.ENVIRONMENT == "development":
            data = {
                "from": "Liberty Pay <<EMAIL>>",
                "to": "<EMAIL>",
                "cc": "<EMAIL>",
                "subject": email_subject,
                "html": template,
            }

        else:

            data = {
                "from": "Liberty Pay <<EMAIL>>",
                "to": "<EMAIL>",
                "cc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "bcc": "<EMAIL>",
                "subject": email_subject,
                "html": template,
            }
                

    message = requests.post(
            f"https://api.mailgun.net/v3/libertypayng.com/messages",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file))],
        )

    return True


# -----------------------------------------------------------------------------------
def send_agent_activity_email_task(message, file, file_name, email_subject, email):
    template_dir = os.path.join(settings.BASE_DIR, "templates/agent_activities.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(message=message, email_subject=email_subject)
    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }           
    message = requests.post(
            f"https://api.mailgun.net/v3/libertypayng.com/messages",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file))],
        )

    return True


def send_transaction_monitoring_email(message, file, file_name, email_subject, email, success_rate=None, failure_rate=None, overall_failure_cause=None):
    template_dir = os.path.join(settings.BASE_DIR, "templates/transactions_monitoring.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(message=message, success_rate=success_rate, failure_rate=failure_rate, 
                                                  overall_failure_cause=overall_failure_cause)
    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }           
    message = requests.post(
            f"https://api.mailgun.net/v3/libertypayng.com/messages",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file))],
        )

    return True



def create_admin_dashboard_history(user, model, action_performed, status, device_type, user_agent):
    from admin_dashboard.models import AdminDashboardLogHistory

    AdminDashboardLogHistory.objects.create(
        user = user,
        content_object = ContentType.objects.get_for_model(model),
        object_id = user.id,
        action_performed = action_performed,
        status = status,
        device = device_type,
        user_agent = user_agent
        )

def notify_agent_transaction_monitor_ran(phone_number, e): 

    whatsapp_url = "https://pickyassist.com/app/api/v2/push"

    
    message = f"NOTIFICATION!!\n\nThe Transaction monitor task ran with an error: \n{e}"



    whatsapp_payload = json.dumps(
            {
                "token": f"{settings.PICKY_ASSIST_TOKEN}",
                "priority ": "0",
                "application": "10",
                "data": [
                    {
                        "number": f"{phone_number}",
                        "message": message
                    }
                ]
            }
        )
    whatsapp_Headers = {'Content-type': 'application/json'}

    whatsapp_response = requests.request("POST", whatsapp_url, headers=whatsapp_Headers, data=whatsapp_payload)
    whatsapp_res = json.loads(whatsapp_response.text)


    return "Transaction Monitoring notification"


def send_agent_activity_email_multiple_files(message, files, email_subject, email):
    template_dir = os.path.join(settings.BASE_DIR, "templates/agent_activities.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(message=message, email_subject=email_subject)
    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }           
    message = requests.post(
            f"https://api.mailgun.net/v3/libertypayng.com/messages",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file)) for file_name, file in files],
        )

    return True


def send_agent_performance_ranking_email(message, 
                                        email_subject, email,
                                        overall_best_amount, 
                                        overall_best_count,
                                        cashout_best_count,
                                        cashout_best_amount,
                                        transfer_best_amount,
                                        transfer_best_count,
                                        overall_best_agent_count,
                                        overall_best_agent_amount,
                                        cashout_best_agent_amount,
                                        cashout_best_agent_count,
                                        transfer_best_agent_amount,
                                        transfer_best_agent_count
                                        ):
    
    template_dir = os.path.join(settings.BASE_DIR, "templates/agents_ranking.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
                overall_best_amount = overall_best_amount, 
                overall_best_count = overall_best_count,
                cashout_best_count = cashout_best_count,
                cashout_best_amount = cashout_best_amount,
                transfer_best_amount = transfer_best_amount,
                transfer_best_count = transfer_best_count,
                overall_best_agent_count = overall_best_agent_count,
                overall_best_agent_amount = overall_best_agent_amount,
                cashout_best_agent_amount = cashout_best_agent_amount,
                cashout_best_agent_count = cashout_best_agent_count,
                transfer_best_agent_amount = transfer_best_agent_amount,
                transfer_best_agent_count = transfer_best_agent_count
                )
    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }           
    message = requests.post(
            f"https://api.mailgun.net/v3/libertypayng.com/messages",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            # files=[(f"attachment", (f"{file_name}", file))],
        )

    return True


def get_agent_transactions_performance_from_lotto():
    try:
        lotto_url = "https://libertydraw.com/api/v1/admin/agency-banking-transactions-metrics"
        # headers = {
        #     "Content-Type": "application/json",
        #     "Authorization": "Bearer {settings.Token}"
        #     }
        resp = requests.request("GET", url=lotto_url)
        return json.loads(resp.text)
    except Exception as e:
        return str(e)
    

def send_retail_agents_inactivity_email(message, 
                                        file, 
                                        file_name, 
                                        email_subject, 
                                        email,
                                        number_of_inactive_agents,
                                        number_of_suspended_agents,
                                        total_cash_at_hand,
                                        total_cash_in_bank,
                                        total_revenue,
                                        number_of_retrievals,
                                        total_cash_available,
                                        date
                                        ):
    template_dir = os.path.join(settings.BASE_DIR, "templates/retail/report.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message, 
        email_subject=email_subject,
        number_of_inactive_agents = number_of_inactive_agents,
        number_of_suspended_agents = number_of_suspended_agents,
        total_cash_at_hand = total_cash_at_hand,
        total_cash_in_bank = total_cash_in_bank,
        total_revenue = total_revenue,
        number_of_retrievals = number_of_retrievals,
        total_cash_available = total_cash_available,
        date = date
        )
    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }           
    message = requests.post(
            f"https://api.mailgun.net/v3/libertypayng.com/messages",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file))],
        )

    return True


def send_retail_agents_performance_email(message, 
    file, 
    file_name, 
    email_subject, 
    email,
    number_of_inactive_agents,
    number_of_retail_agents,
    number_of_suspended_agents,
    number_of_active_agents,
    total_cash_at_hand,
    total_cash_in_bank,
    total_revenue,
    number_of_retrievals,
    total_cash_available,
    date,
    peak_cash_at_bank
                                        ):
    template_dir = os.path.join(settings.BASE_DIR, "templates/retail/performance.html")

    with open(template_dir) as temp_file:
        template = temp_file.read()
        temp_file.close()

    template = Template(template).safe_substitute(
        message=message,
        email_subject=email_subject,
        number_of_inactive_agents=number_of_inactive_agents,
        number_of_suspended_agents=number_of_suspended_agents,
        number_of_active_agents=number_of_active_agents,
        total_cash_at_hand=total_cash_at_hand,
        total_cash_in_bank=total_cash_in_bank,
        total_revenue=total_revenue,
        number_of_retrievals=number_of_retrievals,
        total_cash_available=total_cash_available,
        date=date,
        peak_cash_at_bank=peak_cash_at_bank,
        number_of_retail_agents=number_of_retail_agents
        )
    data = {
        "from": "Liberty Pay <<EMAIL>>",
        "to": email,
        "subject": email_subject,
        "html": template,
    }           
    message = requests.post(
            f"https://api.mailgun.net/v3/libertypayng.com/messages",
            auth=("api", f"{settings.MAILGUN_API_KEY}"),
            data = data,
            files=[(f"attachment", (f"{file_name}", file))],
        )

    return True


def is_last_day_of_month():
    """
    If today is Friday. This function tells
    if this Friday is the last Friday of the 
    current month.
    """
    from datetime import date

    is_last_day = False

    date_today = date.today()
    last_day_of_month = calendar.monthrange(
        date_today.year, date_today.month
        )[1]
    remaining_days = last_day_of_month - date_today.day

    if remaining_days < 7:
        is_last_day = True
        
    return is_last_day


def query_dict_to_dict(query_dict):
    """
    Converts a QueryDict into a dictionary, decoding JSON strings if needed.
    """
    result = {}
    for key, value_list in query_dict.lists():
        # Access the first value (assuming single-value QueryDict entries)
        raw_value = value_list[0]
        try:
            # Attempt to parse as JSON
            parsed_value = json.loads(raw_value)
            result[key] = parsed_value
        except (json.JSONDecodeError, TypeError):
            # If not JSON, store the raw value
            result[key] = raw_value
    return result
