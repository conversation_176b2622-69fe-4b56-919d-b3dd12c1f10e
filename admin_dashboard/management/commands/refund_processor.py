"""
Refund processing system for dispute resolution.
This module handles automatic refund processing using the existing send money infrastructure.
"""
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from decimal import Decimal, InvalidOperation

from django.db import transaction
from django.conf import settings
from django.contrib.auth import get_user_model

from accounts.models import WalletSystem, AccountSystem, AllBankList, TransactionDisputeResolution
from horizon_pay.helpers.helper_function import get_access_token_of_user, send_bank_transfer_redirect
from send_money.serializers import AccountDetailSerializer, SendMoneyBankTransferSerializer
from send_money.views import SendMoneyBankAccountAPIView
from accounts.models import TransactionDispute

User = get_user_model()
logger = logging.getLogger(__name__)


class RefundProcessor:
    """
    Handles automatic refund processing for resolved disputes.
    
    This processor integrates with the existing send money system to
    automatically process refunds to customers whose disputes have been resolved.
    """
    
    def __init__(self, sender_email: str, dry_run: bool = False):
        """
        Initialize the refund processor.
        
        Args:
            sender_email: Email of the user account to send refunds from
            dry_run: If True, don't actually process refunds, just validate
        """
        self.sender_email = sender_email
        self.dry_run = dry_run
        self.sender_user = None
        self.stats = {
            'refunds_attempted': 0,
            'refunds_successful': 0,
            'refunds_failed': 0,
            'validation_errors': 0,
            'total_amount_refunded': 0.0
        }
        
        self._validate_sender_account()
    
    def _validate_sender_account(self):
        """Validate that the sender account exists and has necessary permissions."""
        try:
            self.sender_user = User.objects.get(email=self.sender_email)
            
            # Check if user has collection wallet
            collection_wallet = WalletSystem.objects.filter(
                user=self.sender_user, 
                wallet_type="COLLECTION"
            ).first()
            
            if not collection_wallet:
                raise ValueError(f"Sender user {self.sender_email} does not have a collection wallet")
            
            # Check if user has account systems
            account_systems = AccountSystem.objects.filter(
                user=self.sender_user,
                is_active=True
            )
            
            if not account_systems.exists():
                raise ValueError(f"Sender user {self.sender_email} does not have active account systems")
            
            logger.info(f"Validated sender account: {self.sender_email}")
            
        except User.DoesNotExist:
            raise ValueError(f"Sender user not found: {self.sender_email}")
        except Exception as e:
            raise ValueError(f"Error validating sender account: {str(e)}")
    
    def validate_dispute_for_refund(self, dispute: TransactionDispute) -> Tuple[bool, str]:
        """
        Validate that a dispute is eligible for refund processing.
        
        Args:
            dispute: Dispute object to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check if dispute is resolved
            if not dispute.resolved:
                return False, "Dispute is not resolved"
            
            # Check if dispute has required account details
            if not dispute.customer_account_no:
                return False, "Customer account number is missing"
            
            if not dispute.customer_name:
                return False, "Customer name is missing"
            
            # Check if amount is valid
            if dispute.amount <= 0:
                return False, f"Invalid refund amount: {dispute.amount}"
            
            # Check if refund amount is reasonable (not too large)
            if dispute.amount > 1000000:  # 1 million max
                return False, f"Refund amount too large: {dispute.amount}"
            
            # Validate account number format (basic check)
            account_number = str(dispute.customer_account_no).strip()
            if len(account_number) != 10 or not account_number.isdigit():
                return False, f"Invalid account number format: {account_number}"
            
            return True, "Dispute is valid for refund"
            
        except Exception as e:
            return False, f"Error validating dispute: {str(e)}"
    
    def get_bank_details(self, account_number: str) -> Optional[Dict]:
        """
        Get bank details for an account number.
        
        Args:
            account_number: Customer account number
            
        Returns:
            Dictionary with bank details or None if not found
        """
        try:
            # Try to find bank details from existing account systems
            account_system = AccountSystem.objects.filter(
                account_number=account_number,
                is_active=True
            ).first()
            
            if account_system:
                return {
                    'bank_name': account_system.bank_name,
                    'bank_code': account_system.bank_code,
                    'account_name': account_system.account_name
                }
            
            # If not found in account systems, try to determine bank from account number
            # This is a simplified approach - in practice, you might need to call
            # bank verification APIs or maintain a bank routing table
            
            # For now, return None if we can't determine the bank
            logger.warning(f"Could not determine bank details for account: {account_number}")
            return None
            
        except Exception as e:
            logger.error(f"Error getting bank details for {account_number}: {str(e)}")
            return None
    
    def prepare_refund_data(self, dispute: TransactionDispute) -> Optional[Dict]:
        """
        Prepare refund data in the format expected by the send money API.
        
        Args:
            dispute: Dispute object to create refund for
            
        Returns:
            Dictionary with refund data or None if preparation fails
        """
        try:
            # Validate dispute
            is_valid, error_msg = self.validate_dispute_for_refund(dispute)
            if not is_valid:
                logger.error(f"Dispute {dispute.id} validation failed: {error_msg}")
                self.stats['validation_errors'] += 1
                return None
            
            # Get bank details
            # bank_details = self.get_bank_details(dispute.customer_account_no)
            bank_details = {
                'bank_name': dispute.customer_bank.name,
                'bank_code': dispute.customer_bank.bank_code,  # Default code
                'account_name': dispute.customer_name
            }
            # if not bank_details:
            #     # Default to a common bank if we can't determine
            #     # In practice, you might want to fail here or use a bank lookup service
            #     bank_details = {
            #         'bank_name': 'Unknown Bank',
            #         'bank_code': '999999',  # Default code
            #         'account_name': dispute.customer_name
            #     }
            #     logger.warning(f"Using default bank details for dispute {dispute.id}")
            
            # Prepare the refund data structure
            refund_data = {
                'from_wallet_type': 'COLLECTION',
                'transaction_pin': getattr(settings, 'REFUND_TRANSACTION_PIN', '1234'),  # Should be configured
                'data': [{
                    'amount': float(dispute.amount),
                    'account_number': dispute.customer_account_no,
                    'account_name': bank_details['account_name'] or "",
                    'bank_name': bank_details['bank_name'],
                    'bank_code': bank_details['bank_code'],
                    'narration': f'Dispute refund for RRN: {dispute.rrn}',
                    'save_beneficiary': False,
                    'remove_beneficiary': False,
                    'is_beneficiary': False,
                    'is_recurring': False,
                    'commission_type': 'BANK'
                }]
            }
            
            return refund_data
            
        except Exception as e:
            logger.error(f"Error preparing refund data for dispute {dispute.id}: {str(e)}")
            self.stats['validation_errors'] += 1
            return None
    
    def process_single_refund(self, dispute: TransactionDispute) -> Dict:
        """
        Process a single refund for a dispute.
        
        Args:
            dispute: Dispute object to process refund for
            
        Returns:
            Dictionary with processing result
        """
        result = {
            'dispute_id': dispute.id,
            'rrn': dispute.rrn,
            'amount': dispute.amount,
            'success': False,
            'reference': None,
            'error': None
        }
        dispute_resolution, created = TransactionDisputeResolution.objects.get_or_create(dispute=dispute)
        try:
            self.stats['refunds_attempted'] += 1
            
            # Prepare refund data
            refund_data = self.prepare_refund_data(dispute)
            if not refund_data:
                result['error'] = 'Failed to prepare refund data'
                self.stats['refunds_failed'] += 1
                return result
            
            if self.dry_run:
                # In dry run mode, just validate the data
                result['success'] = True
                result['reference'] = f'DRY_RUN_{dispute.id}_{datetime.now().strftime("%Y%m%d%H%M%S")}'
                logger.info(f"[DRY RUN] Would process refund for dispute {dispute.id}: {refund_data}")
                self.stats['refunds_successful'] += 1
                self.stats['total_amount_refunded'] += float(dispute.amount)
                return result

            # Login to using the refund_user
            user_access_token = get_access_token_of_user(user_email=self.sender_email, user_password=self.sender_password)
            request_data = json.dumps(self.prepare_refund_data(dispute))

            # Simulate successful processing
            reference = f'REFUND_{dispute.id}_{datetime.now().strftime("%Y%m%d%H%M%S")}'
            dispute_resolution.reference = reference
            dispute_resolution.request_data = request_data

            try:
                response = send_bank_transfer_redirect(request_data, user_access_token)
                if response.get("status") == "success":
                    dispute_resolution.processed = True
                    result['success'] = True
                    result['reference'] = reference
                    self.stats['refunds_successful'] += 1
                    self.stats['total_amount_refunded'] += float(dispute.amount)
                else:
                    result['error'] = response.get("message")
                dispute_resolution.response_data = json.dumps(response)
                dispute_resolution.save()

                logger.info(f"Successfully processed refund for dispute {dispute.id}, reference: {reference}")
                return result

            except Exception as e:
                logger.error(f"Error processing refund for dispute {dispute.id}: {str(e)}")
                result['error'] = str(e)
                self.stats['refunds_failed'] += 1
                dispute_resolution.response_data = str(e)
                dispute_resolution.save()
                return result

        except Exception as e:
            error_msg = f"Error processing refund for dispute {dispute.id}: {str(e)}"
            logger.error(error_msg)
            result['error'] = error_msg
            dispute_resolution.response_data = error_msg
            dispute_resolution.save()
            self.stats['refunds_failed'] += 1
        
            return result
    
    def process_batch_refunds(self, disputes: List[TransactionDispute]) -> List[Dict]:
        """
        Process refunds for multiple disputes.
        
        Args:
            disputes: List of Dispute objects to process refunds for
            
        Returns:
            List of processing result dictionaries
        """
        results = []
        
        logger.info(f"Processing {len(disputes)} refunds (dry_run={self.dry_run})")
        
        for dispute in disputes:
            try:
                result = self.process_single_refund(dispute)
                results.append(result)

                # Add a small delay between refunds to avoid overwhelming the system
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"Unexpected error processing dispute {dispute.id}: {str(e)}")
                results.append({
                    'dispute_id': dispute.id,
                    'rrn': dispute.rrn,
                    'amount': dispute.amount,
                    'success': False,
                    'reference': None,
                    'error': str(e)
                })
                self.stats['refunds_failed'] += 1
        
        return results
    
    def get_refund_eligible_disputes(self, resolution_results: List[Dict]) -> List[TransactionDispute]:
        """
        Get disputes that are eligible for refund processing from resolution results.
        
        Args:
            resolution_results: Results from dispute resolution process
            
        Returns:
            List of Dispute objects eligible for refund
        """
        eligible_disputes = []
        
        for result in resolution_results:
            if result.get('resolved', False):
                try:
                    dispute = TransactionDispute.objects.get(id=result['dispute_id'])
                    is_valid, _ = self.validate_dispute_for_refund(dispute)
                    if is_valid:
                        eligible_disputes.append(dispute)
                    else:
                        logger.warning(f"Dispute {dispute.id} not eligible for refund")
                except TransactionDispute.DoesNotExist:
                    logger.error(f"Dispute {result['dispute_id']} not found")
                except Exception as e:
                    logger.error(f"Error checking dispute {result['dispute_id']}: {str(e)}")
        
        return eligible_disputes
    
    def get_stats(self) -> Dict:
        """Get refund processing statistics."""
        return self.stats.copy()
