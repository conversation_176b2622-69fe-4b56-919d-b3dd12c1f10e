"""
Dispute resolution logic for matching Excel transaction data with unresolved disputes.
This module handles the core business logic for resolving disputes based on RRN matching.
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Set
from decimal import Decimal, InvalidOperation

from django.db import transaction
from django.db.models import Q
from django.utils import timezone

from accounts.models import TransactionDispute
from admin_dashboard.models import Dispute

logger = logging.getLogger(__name__)


class DisputeResolver:
    """
    Core logic for matching Excel transaction data with unresolved disputes
    and marking them as resolved.
    """
    
    def __init__(self, dry_run: bool = False):
        """
        Initialize the dispute resolver.
        
        Args:
            dry_run: If True, don't actually update disputes, just report what would be done
        """
        self.dry_run = dry_run
        self.stats = {
            'disputes_queried': 0,
            'transactions_processed': 0,
            'matches_found': 0,
            'disputes_resolved': 0,
            'amount_mismatches': 0,
            'errors': 0
        }
    
    def get_unresolved_disputes(self, date_filter: Optional[str] = None, 
                              days_back: int = 30) -> List[TransactionDispute]:
        """
        Query unresolved disputes from the database.
        
        Args:
            date_filter: Filter disputes by specific date (YYYY-MM-DD format)
            days_back: Number of days to look back for disputes
            
        Returns:
            List of unresolved Dispute objects
        """
        try:
            # Base query for unresolved disputes
            query = Q(resolved=False)
            
            # Add date filtering
            if date_filter:
                # Filter by specific date
                query &= Q(date_created=date_filter)
            else:
                # Filter by date range (last N days)
                cutoff_date = timezone.now().date() - timedelta(days=days_back)
                query &= Q(date_created__gte=cutoff_date)
            
            # Only include disputes with RRN
            query &= Q(rrn__isnull=False) & ~Q(rrn='')
            
            disputes = TransactionDispute.objects.filter(query).order_by('-date_created')
            
            self.stats['disputes_queried'] = len(disputes)
            logger.info(f"Found {len(disputes)} unresolved disputes to process")
            
            return list(disputes)
            
        except Exception as e:
            logger.error(f"Error querying unresolved disputes: {str(e)}")
            self.stats['errors'] += 1
            return []
    
    def create_rrn_lookup(self, disputes: List[TransactionDispute]) -> Dict[str, List[TransactionDispute]]:
        """
        Create a lookup dictionary mapping RRNs to disputes.
        
        Args:
            disputes: List of Dispute objects
            
        Returns:
            Dictionary mapping RRN to list of disputes (handles duplicates)
        """
        rrn_lookup = {}
        
        for dispute in disputes:
            if dispute.rrn:
                # Clean and normalize RRN
                rrn = self.normalize_rrn(dispute.rrn)
                if rrn:
                    if rrn not in rrn_lookup:
                        rrn_lookup[rrn] = []
                    rrn_lookup[rrn].append(dispute)
        
        logger.info(f"Created RRN lookup with {len(rrn_lookup)} unique RRNs")
        return rrn_lookup
    
    def normalize_rrn(self, rrn: str) -> Optional[str]:
        """
        Normalize RRN for consistent matching.
        
        Args:
            rrn: Raw RRN string
            
        Returns:
            Normalized RRN or None if invalid
        """
        if not rrn:
            return None
        
        # Convert to string and strip whitespace
        normalized = str(rrn).strip().upper()
        
        # Remove common prefixes and suffixes
        normalized = normalized.replace('RRN:', '').replace('REF:', '')
        normalized = normalized.strip()
        
        # Must be at least 6 characters
        if len(normalized) < 6:
            return None
        
        return normalized
    
    def validate_amount_match(self, dispute_amount: float, excel_amount: float, 
                            tolerance_percent: float = 0.01) -> Tuple[bool, str]:
        """
        Validate that dispute amount matches Excel amount within tolerance.
        
        Args:
            dispute_amount: Amount from dispute record
            excel_amount: Amount from Excel file
            tolerance_percent: Allowed percentage difference (default 1%)
            
        Returns:
            Tuple of (is_match, reason)
        """
        try:
            if dispute_amount == 0 and excel_amount == 0:
                return True, "Both amounts are zero"
            
            if dispute_amount == 0 or excel_amount == 0:
                return False, f"One amount is zero: dispute={dispute_amount}, excel={excel_amount}"
            
            # Calculate percentage difference
            diff = abs(dispute_amount - excel_amount)
            avg_amount = (dispute_amount + excel_amount) / 2
            percent_diff = (diff / avg_amount) * 100
            
            if percent_diff <= tolerance_percent * 100:
                return True, f"Amounts match within tolerance: dispute={dispute_amount}, excel={excel_amount}"
            else:
                return False, f"Amount mismatch: dispute={dispute_amount}, excel={excel_amount}, diff={percent_diff:.2f}%"
                
        except Exception as e:
            return False, f"Error validating amounts: {str(e)}"
    
    def match_transactions_with_disputes(self, transactions: List[Dict], 
                                       disputes: List[TransactionDispute],
                                       validate_amounts: bool = True) -> List[Dict]:
        """
        Match Excel transactions with disputes based on RRN.
        
        Args:
            transactions: List of transaction dictionaries from Excel
            disputes: List of unresolved Dispute objects
            validate_amounts: Whether to validate amount matching
            
        Returns:
            List of match dictionaries
        """
        matches = []
        rrn_lookup = self.create_rrn_lookup(disputes)
        processed_rrns = set()
        
        self.stats['transactions_processed'] = len(transactions)
        
        for transaction in transactions:
            try:
                excel_rrn = self.normalize_rrn(transaction.get('rrn'))
                if not excel_rrn:
                    continue
                
                # Skip if we've already processed this RRN
                if excel_rrn in processed_rrns:
                    continue
                
                # Look for matching disputes
                matching_disputes = rrn_lookup.get(excel_rrn, [])
                
                if not matching_disputes:
                    continue
                
                excel_amount = transaction.get('amount', 0)
                
                # Process each matching dispute
                for dispute in matching_disputes:
                    match_result = {
                        'transaction': transaction,
                        'dispute': dispute,
                        'rrn': excel_rrn,
                        'excel_amount': excel_amount,
                        'dispute_amount': dispute.amount,
                        'amount_match': True,
                        'amount_match_reason': '',
                        'can_resolve': True,
                        'resolution_reason': ''
                    }
                    
                    # Validate amount if required
                    if validate_amounts and dispute.amount > 0:
                        amount_match, reason = self.validate_amount_match(
                            dispute.amount, excel_amount
                        )
                        match_result['amount_match'] = amount_match
                        match_result['amount_match_reason'] = reason
                        
                        if not amount_match:
                            match_result['can_resolve'] = False
                            match_result['resolution_reason'] = f"Amount mismatch: {reason}"
                            self.stats['amount_mismatches'] += 1
                    
                    # Additional validation checks
                    if dispute.resolved:
                        match_result['can_resolve'] = False
                        match_result['resolution_reason'] = "Dispute already resolved"

                    matches.append(match_result)
                    
                    if match_result['can_resolve']:
                        self.stats['matches_found'] += 1
                
                processed_rrns.add(excel_rrn)
                
            except Exception as e:
                logger.error(f"Error processing transaction {transaction}: {str(e)}")
                self.stats['errors'] += 1
                continue
        
        logger.info(f"Found {len(matches)} total matches, {self.stats['matches_found']} can be resolved")
        return matches
    
    def resolve_disputes(self, matches: List[Dict], resolved_by: str = "AUTO_RESOLUTION") -> List[Dict]:
        """
        Mark disputes as resolved based on matches.
        
        Args:
            matches: List of match dictionaries from match_transactions_with_disputes
            resolved_by: String identifying who/what resolved the dispute
            
        Returns:
            List of resolution result dictionaries
        """
        results = []
        
        for match in matches:
            if not match['can_resolve']:
                results.append({
                    'dispute_id': match['dispute'].id,
                    'rrn': match['rrn'],
                    'resolved': False,
                    'reason': match['resolution_reason']
                })
                continue
            
            try:
                dispute = match['dispute']
                
                if not self.dry_run:
                    # Update dispute in database
                    with transaction.atomic():
                        dispute.resolved = True
                        dispute.resolved_by = resolved_by
                        dispute.save()
                
                self.stats['disputes_resolved'] += 1
                
                results.append({
                    'dispute_id': dispute.id,
                    'rrn': match['rrn'],
                    'resolved': True,
                    'reason': 'Successfully resolved via Excel data match',
                    'excel_amount': match['excel_amount'],
                    'dispute_amount': match['dispute_amount']
                })
                
                logger.info(f"{'[DRY RUN] ' if self.dry_run else ''}Resolved dispute {dispute.id} for RRN {match['rrn']}")
                
            except Exception as e:
                error_msg = f"Error resolving dispute {match['dispute'].id}: {str(e)}"
                logger.error(error_msg)
                self.stats['errors'] += 1
                
                results.append({
                    'dispute_id': match['dispute'].id,
                    'rrn': match['rrn'],
                    'resolved': False,
                    'reason': error_msg
                })
        
        return results
    
    def process_excel_data(self, excel_results: List[Dict], 
                          date_filter: Optional[str] = None,
                          days_back: int = 30,
                          validate_amounts: bool = True) -> Dict:
        """
        Main processing method that combines Excel data with dispute resolution.
        
        Args:
            excel_results: List of Excel processing results
            date_filter: Filter disputes by specific date
            days_back: Number of days to look back for disputes
            validate_amounts: Whether to validate amount matching
            
        Returns:
            Dictionary with processing results
        """
        # Get unresolved disputes
        disputes = self.get_unresolved_disputes(date_filter, days_back)
        
        if not disputes:
            logger.info("No unresolved disputes found")
            return {
                'stats': self.stats,
                'matches': [],
                'resolutions': []
            }
        
        # Combine all transactions from Excel files
        all_transactions = []
        for excel_result in excel_results:
            all_transactions.extend(excel_result.get('transactions', []))
        
        if not all_transactions:
            logger.info("No transactions found in Excel files")
            return {
                'stats': self.stats,
                'matches': [],
                'resolutions': []
            }
        
        # Match transactions with disputes
        matches = self.match_transactions_with_disputes(
            all_transactions, disputes, validate_amounts
        )
        
        # Resolve disputes
        resolutions = self.resolve_disputes(matches)
        
        return {
            'stats': self.stats,
            'matches': matches,
            'resolutions': resolutions
        }
    
    def get_stats(self) -> Dict:
        """Get processing statistics."""
        return self.stats.copy()
