"""
Configuration management for dispute resolution system.
This module handles environment variables, settings validation, and configuration defaults.
"""

import os
from typing import Dict, Any, Optional, List
from django.conf import settings


class DisputeResolutionConfig:
    """
    Configuration manager for the dispute resolution system.
    
    Handles environment variables, validates settings, and provides
    default values for all configurable parameters.
    """
    
    # Default configuration values
    DEFAULTS = {
        # Google Drive settings
        'GOOGLE_DRIVE_CREDENTIALS_PATH': '',
        'GOOGLE_DRIVE_FOLDER_ID': '',
        'GOOGLE_DRIVE_DOWNLOAD_DIR': 'data/downloads',
        
        # Refund processing settings
        'DISPUTE_REFUND_SENDER_EMAIL': '<EMAIL>',
        'REFUND_TRANSACTION_PIN': '1234',  # Should be changed in production
        'REFUND_MAX_AMOUNT': 1000000.0,  # 1 million max refund
        'REFUND_BATCH_SIZE': 50,  # Process refunds in batches
        
        # Email settings
        'DISPUTE_EMAIL_FROM_NAME': 'Liberty Pay Support',
        'DISPUTE_EMAIL_FROM_EMAIL': '<EMAIL>',
        'EMAIL_BATCH_SIZE': 20,  # Send emails in batches
        'EMAIL_DELAY_SECONDS': 1,  # Delay between emails
        
        # Processing settings
        'AMOUNT_VALIDATION_TOLERANCE_PERCENT': 1.0,  # 1% tolerance for amount matching
        'DEFAULT_DAYS_BACK': 7,  # Default days to look back for disputes
        'MAX_DAYS_BACK': 90,  # Maximum days to look back
        'CACHE_RETENTION_DAYS': 30,  # How long to keep cache entries
        
        # File processing settings
        'MAX_FILE_SIZE_MB': 100,  # Maximum Excel file size
        'SUPPORTED_FILE_EXTENSIONS': ['.xlsx', '.xls'],
        'MAX_TRANSACTIONS_PER_FILE': 10000,  # Safety limit
        
        # Logging settings
        'LOG_LEVEL': 'INFO',
        'LOG_RETENTION_DAYS': 30,
        'AUDIT_LOG_RETENTION_DAYS': 90,
        
        # Performance settings
        'ENABLE_PERFORMANCE_MONITORING': True,
        'MEMORY_MONITORING_INTERVAL': 100,  # Log memory every N operations
        
        # Safety settings
        'DRY_RUN_BY_DEFAULT': False,
        'REQUIRE_CONFIRMATION_FOR_LARGE_BATCHES': True,
        'LARGE_BATCH_THRESHOLD': 100,  # Disputes
        'MAX_ERRORS_BEFORE_STOP': 50,
        
        # Database settings
        'CACHE_DB_PATH': 'data/dispute_cache.db',
        'ENABLE_DATABASE_BACKUP': True,
        'BACKUP_RETENTION_DAYS': 7,
    }
    
    def __init__(self):
        """Initialize configuration manager."""
        self._config = {}
        self._load_configuration()
        self._validate_configuration()
    
    def _load_configuration(self):
        """Load configuration from environment variables and Django settings."""
        # Start with defaults
        self._config = self.DEFAULTS.copy()
        
        # Override with Django settings
        for key in self.DEFAULTS.keys():
            if hasattr(settings, key):
                self._config[key] = getattr(settings, key)
        
        # Override with environment variables
        for key in self.DEFAULTS.keys():
            env_value = os.environ.get(key)
            if env_value is not None:
                # Convert string values to appropriate types
                self._config[key] = self._convert_env_value(key, env_value)
    
    def _convert_env_value(self, key: str, value: str) -> Any:
        """Convert environment variable string to appropriate type."""
        # Boolean values
        if key in ['DRY_RUN_BY_DEFAULT', 'REQUIRE_CONFIRMATION_FOR_LARGE_BATCHES', 
                  'ENABLE_PERFORMANCE_MONITORING', 'ENABLE_DATABASE_BACKUP']:
            return value.lower() in ('true', '1', 'yes', 'on')
        
        # Integer values
        if key in ['REFUND_BATCH_SIZE', 'EMAIL_BATCH_SIZE', 'EMAIL_DELAY_SECONDS',
                  'DEFAULT_DAYS_BACK', 'MAX_DAYS_BACK', 'CACHE_RETENTION_DAYS',
                  'LOG_RETENTION_DAYS', 'AUDIT_LOG_RETENTION_DAYS', 'MEMORY_MONITORING_INTERVAL',
                  'LARGE_BATCH_THRESHOLD', 'MAX_ERRORS_BEFORE_STOP', 'BACKUP_RETENTION_DAYS',
                  'MAX_TRANSACTIONS_PER_FILE', 'MAX_FILE_SIZE_MB']:
            try:
                return int(value)
            except ValueError:
                return self.DEFAULTS[key]
        
        # Float values
        if key in ['REFUND_MAX_AMOUNT', 'AMOUNT_VALIDATION_TOLERANCE_PERCENT']:
            try:
                return float(value)
            except ValueError:
                return self.DEFAULTS[key]
        
        # List values
        if key == 'SUPPORTED_FILE_EXTENSIONS':
            return [ext.strip() for ext in value.split(',')]
        
        # String values (default)
        return value
    
    def _validate_configuration(self):
        """Validate configuration values."""
        errors = []
        
        # Validate required settings for production
        if not self.get('GOOGLE_DRIVE_CREDENTIALS_PATH') and not self.get('DRY_RUN_BY_DEFAULT'):
            errors.append("GOOGLE_DRIVE_CREDENTIALS_PATH is required for production use")
        
        if not self.get('GOOGLE_DRIVE_FOLDER_ID') and not self.get('DRY_RUN_BY_DEFAULT'):
            errors.append("GOOGLE_DRIVE_FOLDER_ID is required for production use")
        
        # Validate email settings
        sender_email = self.get('DISPUTE_REFUND_SENDER_EMAIL')
        if not sender_email or '@' not in sender_email:
            errors.append("DISPUTE_REFUND_SENDER_EMAIL must be a valid email address")
        
        # Validate numeric ranges
        if self.get('AMOUNT_VALIDATION_TOLERANCE_PERCENT') < 0 or self.get('AMOUNT_VALIDATION_TOLERANCE_PERCENT') > 100:
            errors.append("AMOUNT_VALIDATION_TOLERANCE_PERCENT must be between 0 and 100")
        
        if self.get('DEFAULT_DAYS_BACK') < 1:
            errors.append("DEFAULT_DAYS_BACK must be at least 1")
        
        if self.get('MAX_DAYS_BACK') < self.get('DEFAULT_DAYS_BACK'):
            errors.append("MAX_DAYS_BACK must be greater than or equal to DEFAULT_DAYS_BACK")
        
        if self.get('REFUND_MAX_AMOUNT') <= 0:
            errors.append("REFUND_MAX_AMOUNT must be greater than 0")
        
        # Validate file paths
        credentials_path = self.get('GOOGLE_DRIVE_CREDENTIALS_PATH')
        if credentials_path and not os.path.exists(credentials_path):
            errors.append(f"Google Drive credentials file not found: {credentials_path}")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value.
        
        Args:
            key: Configuration key
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value.
        
        Args:
            key: Configuration key
            value: Configuration value
        """
        self._config[key] = value
    
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration values."""
        return self._config.copy()
    
    def get_google_drive_config(self) -> Dict[str, str]:
        """Get Google Drive specific configuration."""
        return {
            'credentials_path': self.get('GOOGLE_DRIVE_CREDENTIALS_PATH'),
            'folder_id': self.get('GOOGLE_DRIVE_FOLDER_ID'),
            'download_dir': self.get('GOOGLE_DRIVE_DOWNLOAD_DIR')
        }
    
    def get_refund_config(self) -> Dict[str, Any]:
        """Get refund processing specific configuration."""
        return {
            'sender_email': self.get('DISPUTE_REFUND_SENDER_EMAIL'),
            'transaction_pin': self.get('REFUND_TRANSACTION_PIN'),
            'max_amount': self.get('REFUND_MAX_AMOUNT'),
            'batch_size': self.get('REFUND_BATCH_SIZE')
        }
    
    def get_email_config(self) -> Dict[str, Any]:
        """Get email specific configuration."""
        return {
            'from_name': self.get('DISPUTE_EMAIL_FROM_NAME'),
            'from_email': self.get('DISPUTE_EMAIL_FROM_EMAIL'),
            'batch_size': self.get('EMAIL_BATCH_SIZE'),
            'delay_seconds': self.get('EMAIL_DELAY_SECONDS')
        }
    
    def get_processing_config(self) -> Dict[str, Any]:
        """Get processing specific configuration."""
        return {
            'amount_tolerance_percent': self.get('AMOUNT_VALIDATION_TOLERANCE_PERCENT'),
            'default_days_back': self.get('DEFAULT_DAYS_BACK'),
            'max_days_back': self.get('MAX_DAYS_BACK'),
            'cache_retention_days': self.get('CACHE_RETENTION_DAYS')
        }
    
    def get_safety_config(self) -> Dict[str, Any]:
        """Get safety and validation configuration."""
        return {
            'dry_run_by_default': self.get('DRY_RUN_BY_DEFAULT'),
            'require_confirmation_for_large_batches': self.get('REQUIRE_CONFIRMATION_FOR_LARGE_BATCHES'),
            'large_batch_threshold': self.get('LARGE_BATCH_THRESHOLD'),
            'max_errors_before_stop': self.get('MAX_ERRORS_BEFORE_STOP')
        }
    
    def is_production_ready(self) -> tuple[bool, List[str]]:
        """
        Check if configuration is ready for production use.
        
        Returns:
            Tuple of (is_ready, list_of_issues)
        """
        issues = []
        
        # Check required settings
        if not self.get('GOOGLE_DRIVE_CREDENTIALS_PATH'):
            issues.append("Google Drive credentials path not configured")
        
        if not self.get('GOOGLE_DRIVE_FOLDER_ID'):
            issues.append("Google Drive folder ID not configured")
        
        if self.get('REFUND_TRANSACTION_PIN') == '1234':
            issues.append("Default transaction PIN should be changed for production")
        
        if self.get('DRY_RUN_BY_DEFAULT'):
            issues.append("Dry run mode is enabled by default")
        
        # Check file existence
        credentials_path = self.get('GOOGLE_DRIVE_CREDENTIALS_PATH')
        if credentials_path and not os.path.exists(credentials_path):
            issues.append(f"Google Drive credentials file not found: {credentials_path}")
        
        return len(issues) == 0, issues
    
    def create_directories(self) -> None:
        """Create necessary directories based on configuration."""
        directories = [
            self.get('GOOGLE_DRIVE_DOWNLOAD_DIR'),
            os.path.dirname(self.get('CACHE_DB_PATH')),
            'logs/dispute_resolution'
        ]
        
        for directory in directories:
            if directory:
                full_path = os.path.join(settings.BASE_DIR, directory)
                os.makedirs(full_path, exist_ok=True)
    
    def export_env_template(self) -> str:
        """
        Export environment variable template.
        
        Returns:
            String with environment variable template
        """
        template_lines = [
            "# Dispute Resolution System Environment Variables",
            "# Copy this file to .env and update the values",
            "",
            "# Google Drive Configuration",
            "GOOGLE_DRIVE_CREDENTIALS_PATH=/path/to/service-account-credentials.json",
            "GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id",
            "",
            "# Refund Processing Configuration",
            "DISPUTE_REFUND_SENDER_EMAIL=<EMAIL>",
            "REFUND_TRANSACTION_PIN=your_secure_pin",
            "",
            "# Processing Configuration",
            f"AMOUNT_VALIDATION_TOLERANCE_PERCENT={self.get('AMOUNT_VALIDATION_TOLERANCE_PERCENT')}",
            f"DEFAULT_DAYS_BACK={self.get('DEFAULT_DAYS_BACK')}",
            "",
            "# Safety Configuration",
            f"DRY_RUN_BY_DEFAULT={str(self.get('DRY_RUN_BY_DEFAULT')).lower()}",
            f"REQUIRE_CONFIRMATION_FOR_LARGE_BATCHES={str(self.get('REQUIRE_CONFIRMATION_FOR_LARGE_BATCHES')).lower()}",
            "",
            "# Logging Configuration",
            f"LOG_LEVEL={self.get('LOG_LEVEL')}",
            "",
        ]
        
        return "\n".join(template_lines)


# Global configuration instance
config = DisputeResolutionConfig()
