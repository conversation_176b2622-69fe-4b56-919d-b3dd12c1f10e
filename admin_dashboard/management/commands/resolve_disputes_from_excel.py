"""
Django management command for automated dispute resolution using Excel files.

This command downloads Excel files from Google Drive, processes transaction data,
matches it with unresolved disputes, processes refunds, and sends email notifications.

Usage:
    python manage.py resolve_disputes_from_excel --help
    python manage.py resolve_disputes_from_excel --dry-run
    python manage.py resolve_disputes_from_excel --date 2025-09-09
    python manage.py resolve_disputes_from_excel --days-back 7 --sender-email <EMAIL>
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.utils import timezone

from .dispute_cache import DisputeTransactionCache
from .google_drive_service import GoogleDriveService
from .excel_processor import ExcelTransactionProcessor
from .dispute_resolver import DisputeResolver
from .refund_processor import RefundProcessor
from .email_notifier import DisputeEmailNotifier

# Set up logging
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Django management command for automated dispute resolution.
    
    This command orchestrates the entire dispute resolution workflow:
    1. Downloads Excel files from Google Drive
    2. Processes transaction data from Excel files
    3. Matches transactions with unresolved disputes
    4. Processes refunds for resolved disputes
    5. Sends email notifications to customers
    6. Caches processed data to avoid reprocessing
    """
    
    help = 'Resolve disputes automatically using Excel transaction data from Google Drive'
    
    def add_arguments(self, parser):
        """Add command line arguments."""
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode (no actual changes made)',
        )
        
        parser.add_argument(
            '--date',
            type=str,
            help='Process files for specific date (YYYY-MM-DD format)',
        )
        
        parser.add_argument(
            '--days-back',
            type=int,
            default=7,
            help='Number of days to look back for files (default: 7)',
        )
        
        parser.add_argument(
            '--sender-email',
            type=str,
            default=getattr(settings, 'DISPUTE_REFUND_SENDER_EMAIL', '<EMAIL>'),
            help='Email of the account to send refunds from',
        )
        
        parser.add_argument(
            '--google-credentials',
            type=str,
            default=getattr(settings, 'GOOGLE_DRIVE_CREDENTIALS_PATH', ''),
            help='Path to Google Drive service account credentials JSON file',
        )
        
        parser.add_argument(
            '--google-folder-id',
            type=str,
            default=getattr(settings, 'GOOGLE_DRIVE_FOLDER_ID', ''),
            help='Google Drive folder ID containing Excel files',
        )
        
        parser.add_argument(
            '--skip-download',
            action='store_true',
            help='Skip downloading files from Google Drive (use local files)',
        )
        
        parser.add_argument(
            '--skip-refunds',
            action='store_true',
            help='Skip refund processing (only resolve disputes)',
        )
        
        parser.add_argument(
            '--skip-emails',
            action='store_true',
            help='Skip email notifications',
        )
        
        parser.add_argument(
            '--validate-amounts',
            action='store_true',
            default=True,
            help='Validate that dispute amounts match Excel amounts',
        )
        
        parser.add_argument(
            '--local-files',
            type=str,
            nargs='*',
            help='Process specific local Excel files instead of downloading',
        )
        
        parser.add_argument(
            '--clear-cache',
            action='store_true',
            help='Clear the processing cache before running',
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose logging',
        )
    
    def setup_logging(self, verbose: bool):
        """Set up logging configuration."""
        log_level = logging.DEBUG if verbose else logging.INFO
        
        # Configure logger
        logger.setLevel(log_level)
        
        # Create console handler if not already present
        if not logger.handlers:
            handler = logging.StreamHandler()
            handler.setLevel(log_level)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
    
    def validate_arguments(self, options: Dict) -> None:
        """Validate command line arguments."""
        if not options['skip_download']:
            if not options['google_credentials']:
                raise CommandError(
                    "Google Drive credentials path is required. "
                    "Use --google-credentials or set GOOGLE_DRIVE_CREDENTIALS_PATH in settings."
                )
            
            if not options['google_folder_id']:
                raise CommandError(
                    "Google Drive folder ID is required. "
                    "Use --google-folder-id or set GOOGLE_DRIVE_FOLDER_ID in settings."
                )
            
            if not os.path.exists(options['google_credentials']):
                raise CommandError(f"Google credentials file not found: {options['google_credentials']}")
        
        if options['date']:
            try:
                datetime.strptime(options['date'], '%Y-%m-%d')
            except ValueError:
                raise CommandError("Date must be in YYYY-MM-DD format")
        
        if options['days_back'] < 1:
            raise CommandError("Days back must be at least 1")
    
    def download_excel_files(self, options: Dict) -> List[str]:
        """Download Excel files from Google Drive."""
        if options['skip_download'] or options['local_files']:
            if options['local_files']:
                # Validate local files exist
                for file_path in options['local_files']:
                    if not os.path.exists(file_path):
                        raise CommandError(f"Local file not found: {file_path}")
                return options['local_files']
            else:
                # Look for Excel files in current directory
                excel_files = [f for f in os.listdir('.') if f.endswith(('.xlsx', '.xls'))]
                if not excel_files:
                    raise CommandError("No Excel files found in current directory")
                return excel_files
        
        # Download from Google Drive
        self.stdout.write("Connecting to Google Drive...")
        
        drive_service = GoogleDriveService(
            credentials_path=options['google_credentials'],
            folder_id=options['google_folder_id']
        )

        # Validate folder access
        if not drive_service.validate_folder_access():
            raise CommandError("Cannot access Google Drive folder")
        
        # Get files to download
        if options['date']:
            files = drive_service.get_files_for_date_range(options['date'])
        else:
            files = drive_service.get_latest_files(days_back=options['days_back'])
        
        if not files:
            self.stdout.write(self.style.WARNING("No Excel files found to download"))
            return []
        
        # Download files
        download_dir = os.path.join(settings.BASE_DIR, 'data', 'downloads')
        os.makedirs(download_dir, exist_ok=True)
        
        self.stdout.write(f"Downloading {len(files)} files...")
        
        download_results = drive_service.download_files_batch(files, download_dir)
        downloaded_files = []
        
        for file_info, local_path, success in download_results:
            if success:
                downloaded_files.append(local_path)
                self.stdout.write(f"Downloaded: {file_info['name']}")
            else:
                self.stdout.write(
                    self.style.ERROR(f"Failed to download: {file_info['name']}")
                )
        
        return downloaded_files
    
    def process_excel_files(self, file_paths: List[str], cache: DisputeTransactionCache) -> List[Dict]:
        """Process Excel files to extract transaction data."""
        processor = ExcelTransactionProcessor()
        results = []
        
        self.stdout.write(f"Processing {len(file_paths)} Excel files...")
        
        for file_path in file_paths:
            try:
                file_name = os.path.basename(file_path)
                
                # Check cache first
                if cache.is_file_processed(file_name):
                    self.stdout.write(f"Skipping already processed file: {file_name}")
                    continue
                
                self.stdout.write(f"Processing: {file_name}")
                
                # Process the file
                result = processor.process_file(file_path)
                
                if result['errors']:
                    for error in result['errors']:
                        self.stdout.write(self.style.ERROR(f"Error in {file_name}: {error}"))
                
                if result['total_transactions'] > 0:
                    results.append(result)
                    
                    # Cache the processed file
                    cache.mark_file_processed(
                        file_name=file_name,
                        file_size=result['file_size'],
                        file_hash=result['file_hash'],
                        total_transactions=result['total_transactions']
                    )
                    
                    self.stdout.write(
                        f"Processed {result['total_transactions']} transactions from {file_name}"
                    )
                else:
                    self.stdout.write(self.style.WARNING(f"No transactions found in {file_name}"))
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error processing {file_path}: {str(e)}"))
                logger.error(f"Error processing {file_path}: {str(e)}")
        
        return results
    
    def resolve_disputes(self, excel_results: List[Dict], options: Dict) -> Dict:
        """Resolve disputes using Excel transaction data."""
        resolver = DisputeResolver(dry_run=options['dry_run'])
        
        self.stdout.write("Matching transactions with disputes...")
        
        resolution_results = resolver.process_excel_data(
            excel_results=excel_results,
            date_filter=options['date'],
            days_back=options['days_back'],
            validate_amounts=options['validate_amounts']
        )
        
        stats = resolution_results['stats']
        self.stdout.write(f"Disputes queried: {stats['disputes_queried']}")
        self.stdout.write(f"Transactions processed: {stats['transactions_processed']}")
        self.stdout.write(f"Matches found: {stats['matches_found']}")
        self.stdout.write(f"Disputes resolved: {stats['disputes_resolved']}")
        
        if stats['amount_mismatches'] > 0:
            self.stdout.write(
                self.style.WARNING(f"Amount mismatches: {stats['amount_mismatches']}")
            )
        
        return resolution_results
    
    def process_refunds(self, resolution_results: Dict, options: Dict) -> List[Dict]:
        """Process refunds for resolved disputes."""
        if options['skip_refunds']:
            self.stdout.write("Skipping refund processing")
            return []
        
        refund_processor = RefundProcessor(
            sender_email=getattr(settings, 'DISPUTE_REFUND_SENDER_EMAIL', '<EMAIL>'),
            dry_run=options['dry_run']
        )
        
        # Get disputes eligible for refund
        eligible_disputes = refund_processor.get_refund_eligible_disputes(
            resolution_results['resolutions']
        )
        
        if not eligible_disputes:
            self.stdout.write("No disputes eligible for refund")
            return []
        
        self.stdout.write(f"Processing refunds for {len(eligible_disputes)} disputes...")
        
        refund_results = refund_processor.process_batch_refunds(eligible_disputes)
        
        stats = refund_processor.get_stats()
        self.stdout.write(f"Refunds attempted: {stats['refunds_attempted']}")
        self.stdout.write(f"Refunds successful: {stats['refunds_successful']}")
        self.stdout.write(f"Refunds failed: {stats['refunds_failed']}")
        self.stdout.write(f"Total amount refunded: ₦{stats['total_amount_refunded']:,.2f}")
        
        return refund_results
    
    def send_notifications(self, eligible_disputes: List, refund_results: List[Dict], options: Dict):
        """Send email notifications to customers."""
        if options['skip_emails']:
            self.stdout.write("Skipping email notifications")
            return
        
        if not eligible_disputes:
            self.stdout.write("No disputes to send notifications for")
            return
        
        notifier = DisputeEmailNotifier(dry_run=options['dry_run'])
        
        self.stdout.write(f"Sending email notifications for {len(eligible_disputes)} disputes...")
        
        email_results = notifier.send_batch_notifications(eligible_disputes, refund_results)
        
        stats = notifier.get_stats()
        self.stdout.write(f"Emails attempted: {stats['emails_attempted']}")
        self.stdout.write(f"Emails sent: {stats['emails_sent']}")
        self.stdout.write(f"Emails failed: {stats['emails_failed']}")
    
    def handle(self, *args, **options):
        """Main command handler."""
        try:
            # Set up logging
            self.setup_logging(options['verbose'])
            
            # Validate arguments
            self.validate_arguments(options)
            
            # Initialize cache
            cache = DisputeTransactionCache()
            
            if options['clear_cache']:
                self.stdout.write("Clearing processing cache...")
                cache.clear_cache()
            
            # Show cache stats
            cache_stats = cache.get_cache_stats()
            self.stdout.write(f"Cache stats: {cache_stats['total_cached_transactions']} transactions cached")
            
            # Download Excel files
            file_paths = self.download_excel_files(options)
            
            if not file_paths:
                self.stdout.write(self.style.WARNING("No files to process"))
                return
            
            # Process Excel files
            excel_results = self.process_excel_files(file_paths, cache)
            
            if not excel_results:
                self.stdout.write(self.style.WARNING("No transaction data extracted"))
                return
            
            # Resolve disputes
            resolution_results = self.resolve_disputes(excel_results, options)
            
            # Process refunds
            refund_results = self.process_refunds(resolution_results, options)
            
            # Get eligible disputes for notifications
            from admin_dashboard.models import Dispute
            eligible_dispute_ids = [r['dispute_id'] for r in refund_results if r['success']]
            eligible_disputes = list(Dispute.objects.filter(id__in=eligible_dispute_ids))
            
            # Send email notifications
            self.send_notifications(eligible_disputes, refund_results, options)
            
            # Final summary
            self.stdout.write(self.style.SUCCESS("Dispute resolution process completed successfully"))
            
            if options['dry_run']:
                self.stdout.write(
                    self.style.WARNING("This was a dry run - no actual changes were made")
                )
            
        except CommandError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error in dispute resolution command: {str(e)}")
            raise CommandError(f"Command failed: {str(e)}")
