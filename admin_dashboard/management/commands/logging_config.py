"""
Logging configuration for dispute resolution system.
This module provides centralized logging setup with audit trails and error tracking.
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any, Optional

from django.conf import settings


class DisputeResolutionLogger:
    """
    Centralized logger for the dispute resolution system.
    
    Provides structured logging with audit trails, error tracking,
    and performance monitoring for the entire dispute resolution workflow.
    """
    
    def __init__(self, name: str = 'dispute_resolution'):
        """
        Initialize the logger.
        
        Args:
            name: Logger name
        """
        self.logger = logging.getLogger(name)
        self.audit_logger = logging.getLogger(f'{name}.audit')
        self.error_logger = logging.getLogger(f'{name}.errors')
        
        self._setup_loggers()
    
    def _setup_loggers(self):
        """Set up logger configurations."""
        # Create logs directory
        log_dir = os.path.join(settings.BASE_DIR, 'logs', 'dispute_resolution')
        os.makedirs(log_dir, exist_ok=True)
        
        # Main logger setup
        if not self.logger.handlers:
            self.logger.setLevel(logging.DEBUG)
            
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
            
            # File handler with rotation
            file_handler = logging.handlers.RotatingFileHandler(
                os.path.join(log_dir, 'dispute_resolution.log'),
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
            file_handler.setLevel(logging.DEBUG)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
        
        # Audit logger setup
        if not self.audit_logger.handlers:
            self.audit_logger.setLevel(logging.INFO)
            
            audit_handler = logging.handlers.RotatingFileHandler(
                os.path.join(log_dir, 'audit.log'),
                maxBytes=50*1024*1024,  # 50MB
                backupCount=10
            )
            audit_formatter = logging.Formatter(
                '%(asctime)s - AUDIT - %(message)s'
            )
            audit_handler.setFormatter(audit_formatter)
            self.audit_logger.addHandler(audit_handler)
            self.audit_logger.propagate = False
        
        # Error logger setup
        if not self.error_logger.handlers:
            self.error_logger.setLevel(logging.ERROR)
            
            error_handler = logging.handlers.RotatingFileHandler(
                os.path.join(log_dir, 'errors.log'),
                maxBytes=20*1024*1024,  # 20MB
                backupCount=5
            )
            error_formatter = logging.Formatter(
                '%(asctime)s - ERROR - %(name)s - %(funcName)s:%(lineno)d - %(message)s\n%(exc_info)s'
            )
            error_handler.setFormatter(error_formatter)
            self.error_logger.addHandler(error_handler)
            self.error_logger.propagate = False
    
    def info(self, message: str, extra: Dict[str, Any] = None):
        """Log info message."""
        self.logger.info(message, extra=extra or {})
    
    def debug(self, message: str, extra: Dict[str, Any] = None):
        """Log debug message."""
        self.logger.debug(message, extra=extra or {})
    
    def warning(self, message: str, extra: Dict[str, Any] = None):
        """Log warning message."""
        self.logger.warning(message, extra=extra or {})
    
    def error(self, message: str, exc_info: bool = True, extra: Dict[str, Any] = None):
        """Log error message."""
        self.logger.error(message, exc_info=exc_info, extra=extra or {})
        self.error_logger.error(message, exc_info=exc_info, extra=extra or {})
    
    def critical(self, message: str, exc_info: bool = True, extra: Dict[str, Any] = None):
        """Log critical message."""
        self.logger.critical(message, exc_info=exc_info, extra=extra or {})
        self.error_logger.critical(message, exc_info=exc_info, extra=extra or {})
    
    def audit(self, action: str, details: Dict[str, Any] = None, user: str = None):
        """
        Log audit trail entry.
        
        Args:
            action: Action being performed
            details: Additional details about the action
            user: User performing the action
        """
        audit_data = {
            'action': action,
            'timestamp': datetime.now().isoformat(),
            'user': user or 'SYSTEM',
            'details': details or {}
        }
        
        audit_message = f"ACTION={action} USER={audit_data['user']}"
        if details:
            detail_str = ' '.join([f"{k}={v}" for k, v in details.items()])
            audit_message += f" {detail_str}"
        
        self.audit_logger.info(audit_message)
    
    def log_operation_start(self, operation: str, params: Dict[str, Any] = None):
        """Log the start of an operation."""
        self.info(f"Starting operation: {operation}")
        self.audit('OPERATION_START', {'operation': operation, 'params': params})
    
    def log_operation_end(self, operation: str, success: bool, stats: Dict[str, Any] = None):
        """Log the end of an operation."""
        status = "SUCCESS" if success else "FAILURE"
        self.info(f"Operation {operation} completed with status: {status}")
        self.audit('OPERATION_END', {
            'operation': operation, 
            'status': status, 
            'stats': stats or {}
        })
    
    def log_file_processing(self, file_path: str, action: str, result: Dict[str, Any] = None):
        """Log file processing activities."""
        self.audit('FILE_PROCESSING', {
            'file_path': file_path,
            'action': action,
            'result': result or {}
        })
    
    def log_dispute_action(self, dispute_id: int, action: str, details: Dict[str, Any] = None):
        """Log dispute-related actions."""
        self.audit('DISPUTE_ACTION', {
            'dispute_id': dispute_id,
            'action': action,
            'details': details or {}
        })
    
    def log_refund_action(self, dispute_id: int, amount: float, action: str, 
                         reference: str = None, success: bool = None):
        """Log refund-related actions."""
        self.audit('REFUND_ACTION', {
            'dispute_id': dispute_id,
            'amount': amount,
            'action': action,
            'reference': reference,
            'success': success
        })
    
    def log_email_action(self, recipient: str, action: str, success: bool = None, 
                        error: str = None):
        """Log email-related actions."""
        self.audit('EMAIL_ACTION', {
            'recipient': recipient,
            'action': action,
            'success': success,
            'error': error
        })


class ErrorHandler:
    """
    Centralized error handling for the dispute resolution system.
    
    Provides consistent error handling, recovery mechanisms,
    and error reporting across all components.
    """
    
    def __init__(self, logger: DisputeResolutionLogger):
        """
        Initialize error handler.
        
        Args:
            logger: Logger instance for error reporting
        """
        self.logger = logger
        self.error_counts = {}
    
    def handle_error(self, error: Exception, context: str, 
                    recoverable: bool = True, **kwargs) -> bool:
        """
        Handle an error with appropriate logging and recovery.
        
        Args:
            error: The exception that occurred
            context: Context where the error occurred
            recoverable: Whether the error is recoverable
            **kwargs: Additional context information
            
        Returns:
            True if error was handled and operation can continue, False otherwise
        """
        error_type = type(error).__name__
        error_message = str(error)
        
        # Track error frequency
        error_key = f"{context}:{error_type}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Log the error
        self.logger.error(
            f"Error in {context}: {error_message}",
            extra={
                'context': context,
                'error_type': error_type,
                'error_count': self.error_counts[error_key],
                'recoverable': recoverable,
                **kwargs
            }
        )
        
        # Check if we should continue based on error frequency
        if self.error_counts[error_key] > 10:  # Too many similar errors
            self.logger.critical(
                f"Too many errors of type {error_type} in {context}. Stopping operation."
            )
            return False
        
        return recoverable
    
    def handle_validation_error(self, field: str, value: Any, reason: str, 
                              context: str = None) -> None:
        """Handle validation errors."""
        self.logger.warning(
            f"Validation error - {field}: {reason}",
            extra={
                'field': field,
                'value': str(value),
                'reason': reason,
                'context': context or 'unknown'
            }
        )
    
    def handle_api_error(self, api_name: str, error: Exception, 
                        request_data: Dict = None) -> bool:
        """Handle API-related errors."""
        return self.handle_error(
            error, 
            f"API:{api_name}",
            recoverable=True,
            request_data=request_data
        )
    
    def handle_database_error(self, operation: str, error: Exception, 
                            model: str = None) -> bool:
        """Handle database-related errors."""
        return self.handle_error(
            error,
            f"DATABASE:{operation}",
            recoverable=False,  # Database errors are usually not recoverable
            model=model
        )
    
    def handle_file_error(self, file_path: str, operation: str, 
                         error: Exception) -> bool:
        """Handle file-related errors."""
        return self.handle_error(
            error,
            f"FILE:{operation}",
            recoverable=True,
            file_path=file_path
        )
    
    def get_error_summary(self) -> Dict[str, int]:
        """Get summary of all errors encountered."""
        return self.error_counts.copy()


class PerformanceMonitor:
    """
    Performance monitoring for the dispute resolution system.
    
    Tracks timing, resource usage, and performance metrics
    for optimization and monitoring purposes.
    """
    
    def __init__(self, logger: DisputeResolutionLogger):
        """
        Initialize performance monitor.
        
        Args:
            logger: Logger instance for performance reporting
        """
        self.logger = logger
        self.timings = {}
        self.counters = {}
    
    def start_timer(self, operation: str) -> None:
        """Start timing an operation."""
        self.timings[operation] = datetime.now()
    
    def end_timer(self, operation: str) -> float:
        """
        End timing an operation and log the duration.
        
        Args:
            operation: Operation name
            
        Returns:
            Duration in seconds
        """
        if operation not in self.timings:
            self.logger.warning(f"Timer for operation '{operation}' was not started")
            return 0.0
        
        start_time = self.timings[operation]
        duration = (datetime.now() - start_time).total_seconds()
        
        self.logger.info(f"Operation '{operation}' completed in {duration:.2f} seconds")
        self.logger.audit('PERFORMANCE', {
            'operation': operation,
            'duration_seconds': duration
        })
        
        del self.timings[operation]
        return duration
    
    def increment_counter(self, counter_name: str, value: int = 1) -> None:
        """Increment a performance counter."""
        self.counters[counter_name] = self.counters.get(counter_name, 0) + value
    
    def get_counters(self) -> Dict[str, int]:
        """Get all performance counters."""
        return self.counters.copy()
    
    def log_memory_usage(self, context: str = None) -> None:
        """Log current memory usage."""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            self.logger.debug(
                f"Memory usage{' for ' + context if context else ''}: "
                f"RSS={memory_info.rss / 1024 / 1024:.1f}MB, "
                f"VMS={memory_info.vms / 1024 / 1024:.1f}MB"
            )
        except ImportError:
            # psutil not available
            pass
        except Exception as e:
            self.logger.warning(f"Could not get memory usage: {str(e)}")


# Global instances for easy access
dispute_logger = DisputeResolutionLogger()
error_handler = ErrorHandler(dispute_logger)
performance_monitor = PerformanceMonitor(dispute_logger)
