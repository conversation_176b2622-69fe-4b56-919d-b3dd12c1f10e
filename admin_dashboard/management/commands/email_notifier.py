"""
Email notification service for dispute resolution.
This module handles sending email notifications to customers when their disputes are resolved.
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Optional
from string import Template

import requests
from django.conf import settings
from django.template.loader import render_to_string

from accounts.models import TransactionDispute
from main.helper.send_emails import new_send_email

logger = logging.getLogger(__name__)


class DisputeEmailNotifier:
    """
    Service for sending email notifications about dispute resolutions.
    
    This service handles email composition and delivery for customers
    whose disputes have been resolved and refunds processed.
    """
    
    def __init__(self, dry_run: bool = False):
        """
        Initialize the email notifier.
        
        Args:
            dry_run: If True, don't actually send emails, just log what would be sent
        """
        self.dry_run = dry_run
        self.stats = {
            'emails_attempted': 0,
            'emails_sent': 0,
            'emails_failed': 0,
            'validation_errors': 0
        }
    
    def validate_email_data(self, dispute: TransactionDispute, refund_result: Dict) -> tuple[bool, str]:
        """
        Validate that we have all necessary data to send an email.
        
        Args:
            dispute: TransactionDispute object
            refund_result: Result from refund processing
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Validate email format (basic check)
            email = dispute.user_email.strip()
            if '@' not in email or '.' not in email:
                return False, f"Invalid email format: {email}"
            
            # Check if refund was successful
            if not refund_result.get('success', False):
                return False, "Refund was not successful"
            
            # Check required dispute fields
            if not dispute.customer_name:
                return False, "Customer name is missing"
            
            if not dispute.rrn:
                return False, "Transaction RRN is missing"

            return True, "Email data is valid"
            
        except Exception as e:
            return False, f"Error validating email data: {str(e)}"
    
    def format_amount(self, amount: float) -> str:
        """
        Format amount for display in email.
        
        Args:
            amount: Amount to format
            
        Returns:
            Formatted amount string
        """
        try:
            return f"{amount:,.2f}"
        except (ValueError, TypeError):
            return str(amount)
    
    def get_account_last_digits(self, account_number: str) -> str:
        """
        Get last 4 digits of account number for display.
        
        Args:
            account_number: Full account number
            
        Returns:
            Last 4 digits with masking
        """
        try:
            account_str = str(account_number).strip()
            if len(account_str) >= 4:
                return account_str[-4:]
            return account_str
        except Exception:
            return "****"
    
    def prepare_email_context(self, dispute: TransactionDispute, refund_result: Dict) -> Dict:
        """
        Prepare context data for email template.
        
        Args:
            dispute: TransactionDispute object
            refund_result: Result from refund processing
            
        Returns:
            Dictionary with template context
        """
        try:
            # Format resolution date
            resolution_date = datetime.now().strftime("%B %d, %Y at %I:%M %p")
            if dispute.resolved_at:
                resolution_date = dispute.resolved_at.strftime("%B %d, %Y at %I:%M %p")
            
            context = {
                'customer_name': 'Valued Customer',
                'transaction_rrn': dispute.rrn,
                'refund_amount': self.format_amount(dispute.amount),
                'account_number': dispute.customer_account_no,
                'account_last_digits': self.get_account_last_digits(dispute.customer_account_no or ''),
                'resolution_date': resolution_date,
                'refund_reference': refund_result.get('reference', 'N/A'),
                'customer_email': dispute.user_email
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Error preparing email context for dispute {dispute.id}: {str(e)}")
            return {}
    
    def render_email_template(self, context: Dict) -> str:
        """
        Render the email template with context data.
        
        Args:
            context: Template context dictionary
            
        Returns:
            Rendered HTML email content
        """
        try:
            # Use Django template rendering if available
            try:
                return render_to_string('dispute_resolution_notification.html', context)
            except Exception:
                # Fallback to manual template substitution
                template_path = os.path.join(
                    settings.BASE_DIR, 
                    'templates', 
                    'dispute_resolution_notification.html'
                )
                
                if os.path.exists(template_path):
                    with open(template_path, 'r', encoding='utf-8') as f:
                        template_content = f.read()
                    
                    # Use string Template for substitution
                    template = Template(template_content)
                    return template.safe_substitute(**context)
                else:
                    # Return a simple fallback email
                    return self._get_fallback_email_content(context)
                    
        except Exception as e:
            logger.error(f"Error rendering email template: {str(e)}")
            return self._get_fallback_email_content(context)
    
    def _get_fallback_email_content(self, context: Dict) -> str:
        """
        Generate a simple fallback email when template rendering fails.
        
        Args:
            context: Template context dictionary
            
        Returns:
            Simple HTML email content
        """
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c5aa0;">Dispute Resolution Notification</h2>
                
                <p>Dear {context.get('customer_name', 'Valued Customer')},</p>
                
                <p>We're pleased to inform you that your dispute has been successfully resolved.</p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3>Dispute Details:</h3>
                    <p><strong>Reference Number:</strong> {context.get('transaction_rrn', 'N/A')}</p>
                    <p><strong>Refund Amount:</strong> ₦{context.get('refund_amount', '0.00')}</p>
                    <p><strong>Resolution Date:</strong> {context.get('resolution_date', 'N/A')}</p>
                </div>
                
                <p>Your refund has been processed and should reflect in your account within 24-48 hours.</p>
                
                <p>If you have any questions, please contact our support <NAME_EMAIL></p>
                
                <p>Thank you for your patience.</p>
                
                <p>Best regards,<br>The Liberty Pay Support Team</p>
            </div>
        </body>
        </html>
        """
    
    def send_single_notification(self, dispute: TransactionDispute, refund_result: Dict) -> Dict:
        """
        Send email notification for a single dispute resolution.
        
        Args:
            dispute: TransactionDispute object
            refund_result: Result from refund processing
            
        Returns:
            Dictionary with sending result
        """
        result = {
            'dispute_id': dispute.id,
            'email': dispute.user_email,
            'success': False,
            'error': None
        }
        
        try:
            self.stats['emails_attempted'] += 1
            
            # Validate email data
            is_valid, error_msg = self.validate_email_data(dispute, refund_result)
            if not is_valid:
                result['error'] = error_msg
                self.stats['validation_errors'] += 1
                return result
            
            # Prepare email context
            context = self.prepare_email_context(dispute, refund_result)
            if not context:
                result['error'] = 'Failed to prepare email context'
                self.stats['validation_errors'] += 1
                return result
            
            # Render email template
            email_content = self.render_email_template(context)
            if not email_content:
                result['error'] = 'Failed to render email template'
                self.stats['emails_failed'] += 1
                return result
            
            if self.dry_run:
                # In dry run mode, just log what would be sent
                logger.info(f"[DRY RUN] Would send email to {dispute.user_email} for dispute {dispute.id}")
                result['success'] = True
                self.stats['emails_sent'] += 1
                return result
            
            # Send email using existing email infrastructure
            subject = "Your Dispute Has Been Resolved - Refund Processed"
            
            try:
                # Use the existing email sending function
                new_send_email(
                    email=dispute.user_email,
                    template=email_content,
                    subject=subject,
                    meta_data="[DISPUTE RESOLUTION]"
                )
                
                result['success'] = True
                self.stats['emails_sent'] += 1
                logger.info(f"Successfully sent dispute resolution email to {dispute.user_email}")
                
            except Exception as e:
                error_msg = f"Error sending email: {str(e)}"
                result['error'] = error_msg
                self.stats['emails_failed'] += 1
                logger.error(f"Failed to send email to {dispute.user_email}: {error_msg}")
            
        except Exception as e:
            error_msg = f"Unexpected error sending notification for dispute {dispute.id}: {str(e)}"
            logger.error(error_msg)
            result['error'] = error_msg
            self.stats['emails_failed'] += 1
        
        return result
    
    def send_batch_notifications(self, disputes: List[TransactionDispute],
                               refund_results: List[Dict]) -> List[Dict]:
        """
        Send email notifications for multiple dispute resolutions.
        
        Args:
            disputes: List of TransactionDispute objects
            refund_results: List of refund processing results
            
        Returns:
            List of email sending result dictionaries
        """
        results = []
        
        # Create a mapping of dispute_id to refund_result
        refund_lookup = {r['dispute_id']: r for r in refund_results}
        
        logger.info(f"Sending {len(disputes)} dispute resolution emails (dry_run={self.dry_run})")
        
        for dispute in disputes:
            try:
                refund_result = refund_lookup.get(dispute.id)
                if not refund_result:
                    results.append({
                        'dispute_id': dispute.id,
                        'email': dispute.user_email,
                        'success': False,
                        'error': 'No refund result found'
                    })
                    continue
                
                result = self.send_single_notification(dispute, refund_result)
                results.append(result)
                
            except Exception as e:
                logger.error(f"Unexpected error processing dispute {dispute.id}: {str(e)}")
                results.append({
                    'dispute_id': dispute.id,
                    'email': dispute.user_email,
                    'success': False,
                    'error': str(e)
                })
                self.stats['emails_failed'] += 1
        
        return results
    
    def get_stats(self) -> Dict:
        """Get email notification statistics."""
        return self.stats.copy()
