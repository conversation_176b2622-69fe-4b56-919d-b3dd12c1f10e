"""
Google Drive API service for downloading dispute resolution Excel files.
This module handles authentication and file operations with Google Drive.
"""

import os
import io
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging

from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseDownload

logger = logging.getLogger(__name__)


class GoogleDriveService:
    """
    Service class for interacting with Google Drive API to download Excel files.
    
    This service handles authentication, file listing, and downloading of
    dispute resolution Excel files from a specified Google Drive folder.
    """
    
    # Expected file patterns for dispute resolution files
    FILE_PATTERNS = {
        'terminal_owner_cr': r'LIBERTYPAY_Terminal_Owner_CR_(\d{4}_\d{2}_\d{2}_\d{6})\.xlsx',
        'terminal_owner_dr': r'LIBERTYPAY_Terminal_Owner_DR_(\d{4}_\d{2}_\d{2}_\d{6})\.xlsx',
        'pos_acquired_cr': r'LIBERTYPAY_Pos_Acquired_Detail_Report_CR_(\d{4}_\d{2}_\d{2}_\d{6})\.xlsx',
        'pos_acquired_dr': r'LIBERTYPAY_Pos_Acquired_Detail_Report_DR_(\d{4}_\d{2}_\d{2}_\d{6})\.xlsx'
    }
    
    def __init__(self, credentials_path: str, folder_id: str):
        """
        Initialize the Google Drive service.
        
        Args:
            credentials_path: Path to the service account credentials JSON file
            folder_id: Google Drive folder ID containing the Excel files
        """
        self.credentials_path = credentials_path
        self.folder_id = folder_id
        self.service = None
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Google Drive API using service account credentials."""
        try:
            if not os.path.exists(self.credentials_path):
                raise FileNotFoundError(f"Credentials file not found: {self.credentials_path}")

            # Define the required scopes
            scopes = ['https://www.googleapis.com/auth/drive.readonly']

            # Load credentials from service account file
            credentials = Credentials.from_service_account_file(
                self.credentials_path, scopes=scopes
            )

            # Build the Drive service
            self.service = build('drive', 'v3', credentials=credentials)
            logger.info("Successfully authenticated with Google Drive API")
            
        except Exception as e:
            logger.error(f"Failed to authenticate with Google Drive API: {str(e)}")
            raise
    
    def list_files_in_folder(self, date_filter: Optional[str] = None, 
                           file_types: Optional[List[str]] = None) -> List[Dict]:
        """
        List Excel files in the specified Google Drive folder.
        
        Args:
            date_filter: Filter files by date (YYYY-MM-DD format)
            file_types: List of file types to include (e.g., ['terminal_owner_cr', 'terminal_owner_dr'])
            
        Returns:
            List of file dictionaries with metadata
        """
        try:
            if not self.service:
                raise RuntimeError("Google Drive service not authenticated")
            
            # Query to list files in the folder
            query = f"'{self.folder_id}' in parents and trashed=false"
            
            # Add Excel file filter
            query += " and (name contains '.xlsx' or name contains '.xls')"
            
            results = self.service.files().list(
                q=query,
                fields="files(id, name, size, modifiedTime, createdTime)",
                orderBy="modifiedTime desc"
            ).execute()
            
            files = results.get('files', [])
            
            # Filter files based on patterns and date
            filtered_files = []
            for file in files:
                file_info = self._parse_file_name(file['name'])
                if file_info:
                    # Add file metadata
                    file_info.update({
                        'id': file['id'],
                        'size': int(file.get('size', 0)),
                        'modified_time': file.get('modifiedTime'),
                        'created_time': file.get('createdTime')
                    })
                    
                    # Apply date filter if specified
                    if date_filter and file_info['date'] != date_filter:
                        continue
                    
                    # Apply file type filter if specified
                    if file_types and file_info['type'] not in file_types:
                        continue
                    
                    filtered_files.append(file_info)
            
            logger.info(f"Found {len(filtered_files)} matching files in Google Drive folder")
            return filtered_files
            
        except HttpError as e:
            logger.error(f"HTTP error while listing files: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Error listing files from Google Drive: {str(e)}")
            raise
    
    def _parse_file_name(self, file_name: str) -> Optional[Dict]:
        """
        Parse file name to extract metadata.
        
        Args:
            file_name: Name of the Excel file
            
        Returns:
            Dictionary with file metadata or None if pattern doesn't match
        """
        for file_type, pattern in self.FILE_PATTERNS.items():
            match = re.match(pattern, file_name)
            if match:
                date_str = match.group(1)
                # Convert date format from YYYY_MM_DD_HHMMSS to YYYY-MM-DD
                date_part = date_str.split('_')[:3]
                formatted_date = '-'.join(date_part)
                
                return {
                    'name': file_name,
                    'type': file_type,
                    'date': formatted_date,
                    'timestamp': date_str
                }
        
        return None
    
    def download_file(self, file_id: str, local_path: str) -> bool:
        """
        Download a file from Google Drive to local storage.
        
        Args:
            file_id: Google Drive file ID
            local_path: Local path where the file should be saved
            
        Returns:
            True if download successful, False otherwise
        """
        try:
            if not self.service:
                raise RuntimeError("Google Drive service not authenticated")
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            # Request file content
            request = self.service.files().get_media(fileId=file_id)
            
            # Download file
            with io.FileIO(local_path, 'wb') as file_handle:
                downloader = MediaIoBaseDownload(file_handle, request)
                done = False
                while done is False:
                    status, done = downloader.next_chunk()
                    if status:
                        logger.debug(f"Download progress: {int(status.progress() * 100)}%")
            
            logger.info(f"Successfully downloaded file to: {local_path}")
            return True
            
        except HttpError as e:
            logger.error(f"HTTP error while downloading file {file_id}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error downloading file {file_id}: {str(e)}")
            return False
    
    def get_files_for_date_range(self, start_date: str, end_date: str = None, 
                               file_types: Optional[List[str]] = None) -> List[Dict]:
        """
        Get files for a specific date range.
        
        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format (defaults to start_date)
            file_types: List of file types to include
            
        Returns:
            List of file dictionaries
        """
        if end_date is None:
            end_date = start_date
        
        all_files = self.list_files_in_folder(file_types=file_types)
        
        # Filter by date range
        filtered_files = []
        for file in all_files:
            file_date = file['date']
            if start_date <= file_date <= end_date:
                filtered_files.append(file)
        
        return filtered_files
    
    def get_latest_files(self, file_types: Optional[List[str]] = None, 
                        days_back: int = 7) -> List[Dict]:
        """
        Get the latest files within a specified number of days.
        
        Args:
            file_types: List of file types to include
            days_back: Number of days to look back from today
            
        Returns:
            List of file dictionaries
        """
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
        
        return self.get_files_for_date_range(start_date, end_date, file_types)
    
    def download_files_batch(self, files: List[Dict], download_dir: str) -> List[Tuple[Dict, str, bool]]:
        """
        Download multiple files in batch.
        
        Args:
            files: List of file dictionaries from list_files_in_folder
            download_dir: Directory to download files to
            
        Returns:
            List of tuples (file_info, local_path, success)
        """
        results = []
        
        for file_info in files:
            local_path = os.path.join(download_dir, file_info['name'])
            success = self.download_file(file_info['id'], local_path)
            results.append((file_info, local_path, success))
        
        return results
    
    def validate_folder_access(self) -> bool:
        """
        Validate that the service can access the specified folder.
        
        Returns:
            True if folder is accessible, False otherwise
        """
        try:
            if not self.service:
                return False

            # Try to get folder metadata
            folder = self.service.files().get(
                fileId=self.folder_id,
                fields="id, name, mimeType"
            ).execute()

            # Check if it's actually a folder
            if folder.get('mimeType') != 'application/vnd.google-apps.folder':
                logger.error(f"Specified ID is not a folder: {self.folder_id}")
                return False

            logger.info(f"Successfully validated access to folder: {folder.get('name')}")
            return True
            
        except HttpError as e:
            logger.error(f"HTTP error while validating folder access: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error validating folder access: {str(e)}")
            return False
