"""
Excel file processor for dispute resolution transaction data.
This module handles parsing of different Excel file formats to extract
RRN (Retrieval Reference Number) and transaction amount data.
"""

import os
import re
import hashlib
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
import logging

import openpyxl
import pandas as pd
from openpyxl.utils.exceptions import InvalidFileException

logger = logging.getLogger(__name__)


class ExcelTransactionProcessor:
    """
    Processor for extracting transaction data from Excel files.
    
    This processor handles different Excel file formats used for dispute
    resolution, extracting RRN and amount data with validation.
    """
    
    # Common column name patterns for RRN identification
    RRN_COLUMN_PATTERNS = [
        r'.*rrn.*',
        r'.*retrieval.*reference.*',
        r'.*reference.*number.*',
        r'.*ref.*no.*',
        r'.*transaction.*ref.*',
        r'.*txn.*ref.*',
        r'.*stan.*',  # Sometimes STAN is used instead of RRN
    ]
    
    # Common column name patterns for amount identification
    AMOUNT_COLUMN_PATTERNS = [
        r'.*amount.*',
        r'.*value.*',
        r'.*sum.*',
        r'.*total.*',
        r'.*debit.*',
        r'.*credit.*',
        r'.*transaction.*amount.*',
        r'.*txn.*amount.*',
    ]
    
    # Common column name patterns for date identification
    DATE_COLUMN_PATTERNS = [
        r'.*date.*',
        r'.*time.*',
        r'.*created.*',
        r'.*transaction.*date.*',
        r'.*txn.*date.*',
        r'.*processed.*',
    ]
    
    def __init__(self):
        """Initialize the Excel processor."""
        self.supported_extensions = ['.xlsx', '.xls']
    
    def validate_file(self, file_path: str) -> Tuple[bool, str]:
        """
        Validate that the file exists and is a supported Excel format.
        
        Args:
            file_path: Path to the Excel file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not os.path.exists(file_path):
            return False, f"File not found: {file_path}"
        
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_extensions:
            return False, f"Unsupported file format: {file_ext}"
        
        try:
            # Try to open the file to check if it's valid
            wb = openpyxl.load_workbook(file_path, read_only=True)
            wb.close()
            return True, ""
        except InvalidFileException as e:
            return False, f"Invalid Excel file: {str(e)}"
        except Exception as e:
            return False, f"Error opening file: {str(e)}"
    
    def get_file_hash(self, file_path: str) -> str:
        """
        Calculate MD5 hash of the file for caching purposes.
        
        Args:
            file_path: Path to the file
            
        Returns:
            MD5 hash string
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating file hash: {str(e)}")
            return ""
    
    def detect_columns(self, headers: List[str]) -> Dict[str, Optional[int]]:
        """
        Detect relevant columns based on header patterns.
        
        Args:
            headers: List of column headers
            
        Returns:
            Dictionary mapping column types to their indices
        """
        detected = {
            'rrn': None,
            'amount': None,
            'date': None
        }
        
        # Convert headers to lowercase for pattern matching
        lower_headers = [str(h).lower().strip() if h else '' for h in headers]
        
        # Detect RRN column
        for i, header in enumerate(lower_headers):
            for pattern in self.RRN_COLUMN_PATTERNS:
                if re.match(pattern, header, re.IGNORECASE):
                    detected['rrn'] = i
                    break
            if detected['rrn'] is not None:
                break
        
        # Detect amount column
        for i, header in enumerate(lower_headers):
            for pattern in self.AMOUNT_COLUMN_PATTERNS:
                if re.match(pattern, header, re.IGNORECASE):
                    detected['amount'] = i
                    break
            if detected['amount'] is not None:
                break
        
        # Detect date column
        for i, header in enumerate(lower_headers):
            for pattern in self.DATE_COLUMN_PATTERNS:
                if re.match(pattern, header, re.IGNORECASE):
                    detected['date'] = i
                    break
            if detected['date'] is not None:
                break
        
        return detected
    
    def clean_rrn_value(self, value: Any) -> Optional[str]:
        """
        Clean and validate RRN value.
        
        Args:
            value: Raw RRN value from Excel
            
        Returns:
            Cleaned RRN string or None if invalid
        """
        if value is None:
            return None
        
        # Convert to string and strip whitespace
        rrn = str(value).strip()
        
        # Remove common prefixes/suffixes
        rrn = re.sub(r'^(rrn|ref|reference)[:=\s]*', '', rrn, flags=re.IGNORECASE)
        rrn = re.sub(r'[:=\s]*$', '', rrn)
        
        # RRN should be alphanumeric and of reasonable length
        if len(rrn) < 6 or len(rrn) > 50:
            return None
        
        # Check if it contains only valid characters
        if not re.match(r'^[A-Za-z0-9\-_]+$', rrn):
            return None
        
        return rrn
    
    def clean_amount_value(self, value: Any) -> Optional[float]:
        """
        Clean and validate amount value.
        
        Args:
            value: Raw amount value from Excel
            
        Returns:
            Cleaned amount as float or None if invalid
        """
        if value is None:
            return None
        
        try:
            # Handle different number formats
            if isinstance(value, (int, float)):
                amount = float(value)
            else:
                # Convert string to float, removing common formatting
                amount_str = str(value).strip()
                # Remove currency symbols and commas
                amount_str = re.sub(r'[₦$£€,\s]', '', amount_str)
                # Handle negative amounts in parentheses
                if amount_str.startswith('(') and amount_str.endswith(')'):
                    amount_str = '-' + amount_str[1:-1]
                
                amount = float(amount_str)
            
            # Validate amount is reasonable (positive and not too large)
            if amount < 0 or amount > 1000000000:  # 1 billion max
                return None
            
            return round(amount, 2)
            
        except (ValueError, TypeError):
            return None
    
    def process_sheet(self, file_path: str, sheet_name: str = None) -> List[Dict]:
        """
        Process a single sheet from an Excel file.
        
        Args:
            file_path: Path to the Excel file
            sheet_name: Name of the sheet to process (None for first sheet)
            
        Returns:
            List of transaction dictionaries
        """
        transactions = []
        
        try:
            # Load workbook
            wb = openpyxl.load_workbook(file_path, read_only=True)
            
            # Get the sheet
            if sheet_name:
                if sheet_name not in wb.sheetnames:
                    logger.warning(f"Sheet '{sheet_name}' not found in {file_path}")
                    return transactions
                ws = wb[sheet_name]
            else:
                ws = wb.active
            
            # Read headers from first row
            headers = []
            for col in range(1, ws.max_column + 1):
                cell_value = ws.cell(row=1, column=col).value
                headers.append(cell_value)
            
            # Detect relevant columns
            columns = self.detect_columns(headers)
            
            if columns['rrn'] is None:
                logger.warning(f"RRN column not detected in {file_path}, sheet: {sheet_name or 'default'}")
                wb.close()
                return transactions
            
            if columns['amount'] is None:
                logger.warning(f"Amount column not detected in {file_path}, sheet: {sheet_name or 'default'}")
                wb.close()
                return transactions
            
            logger.info(f"Detected columns - RRN: {columns['rrn']}, Amount: {columns['amount']}, Date: {columns['date']}")
            
            # Process data rows
            for row_num in range(2, ws.max_row + 1):
                try:
                    # Extract RRN
                    rrn_cell = ws.cell(row=row_num, column=columns['rrn'] + 1)
                    rrn = self.clean_rrn_value(rrn_cell.value)
                    
                    if not rrn:
                        continue  # Skip rows without valid RRN
                    
                    # Extract amount
                    amount_cell = ws.cell(row=row_num, column=columns['amount'] + 1)
                    amount = self.clean_amount_value(amount_cell.value)
                    
                    if amount is None:
                        continue  # Skip rows without valid amount
                    
                    # Extract date if available
                    transaction_date = None
                    if columns['date'] is not None:
                        date_cell = ws.cell(row=row_num, column=columns['date'] + 1)
                        if date_cell.value:
                            try:
                                if isinstance(date_cell.value, datetime):
                                    transaction_date = date_cell.value.strftime('%Y-%m-%d')
                                else:
                                    # Try to parse string date
                                    date_str = str(date_cell.value).strip()
                                    parsed_date = pd.to_datetime(date_str, errors='coerce')
                                    if not pd.isna(parsed_date):
                                        transaction_date = parsed_date.strftime('%Y-%m-%d')
                            except Exception:
                                pass  # Ignore date parsing errors
                    
                    # Create transaction record
                    transaction = {
                        'rrn': rrn,
                        'amount': amount,
                        'transaction_date': transaction_date,
                        'row_number': row_num,
                        'sheet_name': sheet_name or ws.title
                    }
                    
                    transactions.append(transaction)
                    
                except Exception as e:
                    logger.warning(f"Error processing row {row_num} in {file_path}: {str(e)}")
                    continue
            
            wb.close()
            logger.info(f"Processed {len(transactions)} transactions from {file_path}, sheet: {sheet_name or 'default'}")
            
        except Exception as e:
            logger.error(f"Error processing sheet in {file_path}: {str(e)}")
        
        return transactions
    
    def process_file(self, file_path: str, sheet_names: List[str] = None) -> Dict:
        """
        Process an entire Excel file, potentially multiple sheets.
        
        Args:
            file_path: Path to the Excel file
            sheet_names: List of sheet names to process (None for all sheets)
            
        Returns:
            Dictionary with processing results
        """
        result = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
            'file_hash': self.get_file_hash(file_path),
            'processed_timestamp': datetime.now().isoformat(),
            'sheets_processed': [],
            'total_transactions': 0,
            'transactions': [],
            'errors': []
        }
        
        # Validate file
        is_valid, error_msg = self.validate_file(file_path)
        if not is_valid:
            result['errors'].append(error_msg)
            return result
        
        try:
            # Get sheet names if not specified
            if sheet_names is None:
                wb = openpyxl.load_workbook(file_path, read_only=True)
                sheet_names = wb.sheetnames
                wb.close()
            
            # Process each sheet
            for sheet_name in sheet_names:
                try:
                    sheet_transactions = self.process_sheet(file_path, sheet_name)
                    result['sheets_processed'].append({
                        'name': sheet_name,
                        'transaction_count': len(sheet_transactions)
                    })
                    result['transactions'].extend(sheet_transactions)
                    result['total_transactions'] += len(sheet_transactions)
                    
                except Exception as e:
                    error_msg = f"Error processing sheet '{sheet_name}': {str(e)}"
                    result['errors'].append(error_msg)
                    logger.error(error_msg)
            
        except Exception as e:
            error_msg = f"Error processing file {file_path}: {str(e)}"
            result['errors'].append(error_msg)
            logger.error(error_msg)
        
        return result
