"""
Test script for the dispute resolution system.
This script validates all components and provides a comprehensive test of the workflow.
"""

import os
import tempfile
from datetime import datetime, timedelta
from typing import Dict, List

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.test import TestCase

from admin_dashboard.models import Dispute
from main.models import User
from .dispute_cache import DisputeTransactionCache
from .excel_processor import ExcelTransactionProcessor
from .dispute_resolver import DisputeResolver
from .refund_processor import RefundProcessor
from .email_notifier import DisputeEmailNotifier
from .logging_config import dispute_logger, error_handler, performance_monitor
from .config import config


class Command(BaseCommand):
    """
    Test command for the dispute resolution system.
    
    This command runs comprehensive tests to validate all components
    and ensure the system works correctly end-to-end.
    """
    
    help = 'Test the dispute resolution system components'
    
    def add_arguments(self, parser):
        """Add command line arguments."""
        parser.add_argument(
            '--component',
            type=str,
            choices=['cache', 'excel', 'resolver', 'refund', 'email', 'all'],
            default='all',
            help='Test specific component or all components',
        )
        
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create test disputes and users for testing',
        )
        
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test data after testing',
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )
    
    def handle(self, *args, **options):
        """Main test handler."""
        self.verbose = options['verbose']
        
        self.stdout.write(self.style.SUCCESS("Starting dispute resolution system tests"))
        
        # Test configuration
        self.test_configuration()
        
        # Create test data if requested
        if options['create_test_data']:
            self.create_test_data()
        
        # Run component tests
        component = options['component']
        if component == 'all':
            self.run_all_tests()
        else:
            self.run_component_test(component)
        
        # Cleanup if requested
        if options['cleanup']:
            self.cleanup_test_data()
        
        self.stdout.write(self.style.SUCCESS("All tests completed"))
    
    def test_configuration(self):
        """Test configuration system."""
        self.stdout.write("Testing configuration system...")
        
        try:
            # Test configuration loading
            all_config = config.get_all()
            self.stdout.write(f"Loaded {len(all_config)} configuration values")
            
            # Test specific config sections
            google_config = config.get_google_drive_config()
            refund_config = config.get_refund_config()
            email_config = config.get_email_config()
            
            # Test production readiness
            is_ready, issues = config.is_production_ready()
            if not is_ready:
                self.stdout.write(
                    self.style.WARNING(f"Production readiness issues: {', '.join(issues)}")
                )
            
            # Test directory creation
            config.create_directories()
            
            self.stdout.write(self.style.SUCCESS("✓ Configuration system test passed"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Configuration test failed: {str(e)}"))
    
    def test_cache_system(self):
        """Test the cache system."""
        self.stdout.write("Testing cache system...")
        
        try:
            # Create temporary cache
            with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
                cache = DisputeTransactionCache(tmp_file.name)
                
                # Test file processing tracking
                test_file = "test_file.xlsx"
                self.assertFalse(cache.is_file_processed(test_file))
                
                cache.mark_file_processed(test_file, 1000, "test_hash", 50, 25)
                self.assertTrue(cache.is_file_processed(test_file))
                
                # Test transaction tracking
                test_rrn = "TEST123456"
                self.assertFalse(cache.is_transaction_processed(test_rrn, test_file))
                
                cache.add_processed_transaction(
                    test_rrn, "2025-09-09", 1000.0, test_file, 1, True, "REF123"
                )
                self.assertTrue(cache.is_transaction_processed(test_rrn, test_file))
                
                # Test statistics
                stats = cache.get_cache_stats()
                self.assertEqual(stats['total_files_processed'], 1)
                self.assertEqual(stats['total_cached_transactions'], 1)
                
                # Cleanup
                os.unlink(tmp_file.name)
            
            self.stdout.write(self.style.SUCCESS("✓ Cache system test passed"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Cache system test failed: {str(e)}"))
    
    def test_excel_processor(self):
        """Test the Excel processor."""
        self.stdout.write("Testing Excel processor...")
        
        try:
            processor = ExcelTransactionProcessor()
            
            # Test with sample files if they exist
            sample_files = [
                'LIBERTYPAY_Terminal_Owner_CR_2025_09_09_033659.xlsx',
                'LIBERTYPAY_Terminal_Owner_DR_2025_09_09_033659.xlsx'
            ]
            
            for file_path in sample_files:
                if os.path.exists(file_path):
                    self.stdout.write(f"Testing with file: {file_path}")
                    
                    # Validate file
                    is_valid, error_msg = processor.validate_file(file_path)
                    if not is_valid:
                        self.stdout.write(f"File validation failed: {error_msg}")
                        continue
                    
                    # Process file
                    result = processor.process_file(file_path)
                    
                    self.stdout.write(f"Processed {result['total_transactions']} transactions")
                    self.stdout.write(f"Processed {len(result['sheets_processed'])} sheets")
                    
                    if result['errors']:
                        for error in result['errors']:
                            self.stdout.write(f"Processing error: {error}")
                    
                    # Test individual components
                    if result['transactions']:
                        sample_transaction = result['transactions'][0]
                        self.assertIn('rrn', sample_transaction)
                        self.assertIn('amount', sample_transaction)
                else:
                    self.stdout.write(f"Sample file not found: {file_path}")
            
            self.stdout.write(self.style.SUCCESS("✓ Excel processor test passed"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Excel processor test failed: {str(e)}"))
    
    def test_dispute_resolver(self):
        """Test the dispute resolver."""
        self.stdout.write("Testing dispute resolver...")
        
        try:
            resolver = DisputeResolver(dry_run=True)
            
            # Test with existing disputes
            disputes = resolver.get_unresolved_disputes(days_back=30)
            self.stdout.write(f"Found {len(disputes)} unresolved disputes")
            
            # Test RRN normalization
            test_rrns = ["RRN:123456", "  ref:789012  ", "ABC123DEF"]
            for rrn in test_rrns:
                normalized = resolver.normalize_rrn(rrn)
                self.stdout.write(f"Normalized '{rrn}' to '{normalized}'")
            
            # Test amount validation
            test_cases = [
                (1000.0, 1000.0, True),
                (1000.0, 1005.0, True),  # Within 1% tolerance
                (1000.0, 1020.0, False),  # Outside tolerance
            ]
            
            for dispute_amount, excel_amount, expected in test_cases:
                is_match, reason = resolver.validate_amount_match(dispute_amount, excel_amount)
                if is_match == expected:
                    self.stdout.write(f"✓ Amount validation: {dispute_amount} vs {excel_amount}")
                else:
                    self.stdout.write(f"✗ Amount validation failed: {reason}")
            
            self.stdout.write(self.style.SUCCESS("✓ Dispute resolver test passed"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Dispute resolver test failed: {str(e)}"))
    
    def test_refund_processor(self):
        """Test the refund processor."""
        self.stdout.write("Testing refund processor...")
        
        try:
            # Use a test sender email
            test_sender = config.get('DISPUTE_REFUND_SENDER_EMAIL')
            processor = RefundProcessor(sender_email=test_sender, dry_run=True)
            
            # Test with a sample dispute if available
            sample_dispute = Dispute.objects.filter(
                is_resolved=True,
                customer_account_number__isnull=False
            ).first()
            
            if sample_dispute:
                # Test validation
                is_valid, error_msg = processor.validate_dispute_for_refund(sample_dispute)
                self.stdout.write(f"Dispute validation: {is_valid} - {error_msg}")
                
                # Test refund data preparation
                refund_data = processor.prepare_refund_data(sample_dispute)
                if refund_data:
                    self.stdout.write("✓ Refund data preparation successful")
                else:
                    self.stdout.write("✗ Refund data preparation failed")
            else:
                self.stdout.write("No suitable dispute found for refund testing")
            
            self.stdout.write(self.style.SUCCESS("✓ Refund processor test passed"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Refund processor test failed: {str(e)}"))
    
    def test_email_notifier(self):
        """Test the email notifier."""
        self.stdout.write("Testing email notifier...")
        
        try:
            notifier = DisputeEmailNotifier(dry_run=True)
            
            # Test with a sample dispute
            sample_dispute = Dispute.objects.filter(
                user__isnull=False,
                user__email__isnull=False
            ).first()
            
            if sample_dispute:
                # Test email data validation
                mock_refund_result = {
                    'success': True,
                    'reference': 'TEST_REF_123',
                    'amount': 1000.0
                }
                
                is_valid, error_msg = notifier.validate_email_data(sample_dispute, mock_refund_result)
                self.stdout.write(f"Email validation: {is_valid} - {error_msg}")
                
                # Test email context preparation
                context = notifier.prepare_email_context(sample_dispute, mock_refund_result)
                if context:
                    self.stdout.write("✓ Email context preparation successful")
                    self.stdout.write(f"Context keys: {list(context.keys())}")
                
                # Test template rendering
                email_content = notifier.render_email_template(context)
                if email_content and len(email_content) > 100:
                    self.stdout.write("✓ Email template rendering successful")
                else:
                    self.stdout.write("✗ Email template rendering failed")
            else:
                self.stdout.write("No suitable dispute found for email testing")
            
            self.stdout.write(self.style.SUCCESS("✓ Email notifier test passed"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Email notifier test failed: {str(e)}"))
    
    def run_component_test(self, component: str):
        """Run test for a specific component."""
        test_methods = {
            'cache': self.test_cache_system,
            'excel': self.test_excel_processor,
            'resolver': self.test_dispute_resolver,
            'refund': self.test_refund_processor,
            'email': self.test_email_notifier,
        }
        
        if component in test_methods:
            test_methods[component]()
        else:
            self.stdout.write(self.style.ERROR(f"Unknown component: {component}"))
    
    def run_all_tests(self):
        """Run all component tests."""
        self.test_cache_system()
        self.test_excel_processor()
        self.test_dispute_resolver()
        self.test_refund_processor()
        self.test_email_notifier()
    
    def create_test_data(self):
        """Create test data for testing."""
        self.stdout.write("Creating test data...")
        
        # This would create test disputes and users
        # Implementation depends on your specific test requirements
        self.stdout.write("Test data creation not implemented yet")
    
    def cleanup_test_data(self):
        """Clean up test data."""
        self.stdout.write("Cleaning up test data...")
        
        # This would clean up any test data created
        # Implementation depends on your specific test requirements
        self.stdout.write("Test data cleanup not implemented yet")
    
    def assertTrue(self, condition, msg=None):
        """Assert that condition is true."""
        if not condition:
            raise AssertionError(msg or "Assertion failed")
    
    def assertFalse(self, condition, msg=None):
        """Assert that condition is false."""
        if condition:
            raise AssertionError(msg or "Assertion failed")
    
    def assertEqual(self, first, second, msg=None):
        """Assert that first equals second."""
        if first != second:
            raise AssertionError(msg or f"{first} != {second}")
    
    def assertIn(self, member, container, msg=None):
        """Assert that member is in container."""
        if member not in container:
            raise AssertionError(msg or f"{member} not in {container}")
