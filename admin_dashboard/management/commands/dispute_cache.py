"""
SQLite cache system for tracking processed dispute resolution transactions.
This module provides a lightweight caching mechanism to avoid reprocessing
the same Excel files and transactions.
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from django.conf import settings


class DisputeTransactionCache:
    """
    SQLite-based cache for tracking processed dispute resolution transactions.
    
    This cache helps avoid reprocessing the same Excel files and ensures
    idempotency in the dispute resolution process.
    """
    
    def __init__(self, db_path: Optional[str] = None):
        """
        Initialize the cache with a SQLite database.
        
        Args:
            db_path: Path to SQLite database file. If None, uses default location.
        """
        if db_path is None:
            data_dir = os.path.join(settings.BASE_DIR, 'data')
            os.makedirs(data_dir, exist_ok=True)
            db_path = os.path.join(data_dir, 'dispute_cache.db')
        
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Initialize the SQLite database with required tables."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Table for tracking processed files
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processed_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_name TEXT UNIQUE NOT NULL,
                    file_size INTEGER,
                    file_hash TEXT,
                    processed_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    total_transactions INTEGER DEFAULT 0,
                    successful_matches INTEGER DEFAULT 0
                )
            ''')
            
            # Table for tracking individual processed transactions
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processed_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rrn TEXT NOT NULL,
                    transaction_date DATE,
                    amount DECIMAL(15, 2),
                    source_file TEXT NOT NULL,
                    processed_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    dispute_id INTEGER,
                    refund_processed BOOLEAN DEFAULT FALSE,
                    refund_reference TEXT,
                    UNIQUE(rrn, source_file)
                )
            ''')
            
            # Index for faster lookups
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_rrn 
                ON processed_transactions(rrn)
            ''')
            
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_transaction_date 
                ON processed_transactions(transaction_date)
            ''')
            
            conn.commit()
    
    def is_file_processed(self, file_name: str, file_size: int = None, file_hash: str = None) -> bool:
        """
        Check if a file has already been processed.
        
        Args:
            file_name: Name of the Excel file
            file_size: Size of the file in bytes (optional)
            file_hash: Hash of the file content (optional)
            
        Returns:
            True if file has been processed, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if file_hash:
                cursor.execute(
                    'SELECT COUNT(*) FROM processed_files WHERE file_name = ? AND file_hash = ?',
                    (file_name, file_hash)
                )
            elif file_size:
                cursor.execute(
                    'SELECT COUNT(*) FROM processed_files WHERE file_name = ? AND file_size = ?',
                    (file_name, file_size)
                )
            else:
                cursor.execute(
                    'SELECT COUNT(*) FROM processed_files WHERE file_name = ?',
                    (file_name,)
                )
            
            return cursor.fetchone()[0] > 0
    
    def mark_file_processed(self, file_name: str, file_size: int = None, 
                          file_hash: str = None, total_transactions: int = 0,
                          successful_matches: int = 0):
        """
        Mark a file as processed.
        
        Args:
            file_name: Name of the Excel file
            file_size: Size of the file in bytes
            file_hash: Hash of the file content
            total_transactions: Total number of transactions in the file
            successful_matches: Number of successful dispute matches
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO processed_files 
                (file_name, file_size, file_hash, total_transactions, successful_matches)
                VALUES (?, ?, ?, ?, ?)
            ''', (file_name, file_size, file_hash, total_transactions, successful_matches))
            conn.commit()
    
    def is_transaction_processed(self, rrn: str, source_file: str) -> bool:
        """
        Check if a specific transaction has been processed.
        
        Args:
            rrn: Retrieval Reference Number
            source_file: Source Excel file name
            
        Returns:
            True if transaction has been processed, False otherwise
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                'SELECT COUNT(*) FROM processed_transactions WHERE rrn = ? AND source_file = ?',
                (rrn, source_file)
            )
            return cursor.fetchone()[0] > 0
    
    def add_processed_transaction(self, rrn: str, transaction_date: str, 
                                amount: float, source_file: str, 
                                dispute_id: int = None, refund_processed: bool = False,
                                refund_reference: str = None):
        """
        Add a processed transaction to the cache.
        
        Args:
            rrn: Retrieval Reference Number
            transaction_date: Date of the transaction
            amount: Transaction amount
            source_file: Source Excel file name
            dispute_id: ID of the resolved dispute (optional)
            refund_processed: Whether refund was processed
            refund_reference: Reference for the refund transaction
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO processed_transactions 
                (rrn, transaction_date, amount, source_file, dispute_id, 
                 refund_processed, refund_reference)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (rrn, transaction_date, amount, source_file, dispute_id, 
                  refund_processed, refund_reference))
            conn.commit()
    
    def get_processed_transactions(self, source_file: str = None, 
                                 start_date: str = None, end_date: str = None) -> List[Dict]:
        """
        Get processed transactions with optional filtering.
        
        Args:
            source_file: Filter by source file (optional)
            start_date: Filter by start date (optional)
            end_date: Filter by end date (optional)
            
        Returns:
            List of processed transaction dictionaries
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = 'SELECT * FROM processed_transactions WHERE 1=1'
            params = []
            
            if source_file:
                query += ' AND source_file = ?'
                params.append(source_file)
            
            if start_date:
                query += ' AND transaction_date >= ?'
                params.append(start_date)
            
            if end_date:
                query += ' AND transaction_date <= ?'
                params.append(end_date)
            
            query += ' ORDER BY processed_timestamp DESC'
            
            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]
    
    def get_cache_stats(self) -> Dict:
        """
        Get statistics about the cache.
        
        Returns:
            Dictionary with cache statistics
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # File statistics
            cursor.execute('SELECT COUNT(*) FROM processed_files')
            total_files = cursor.fetchone()[0]
            
            cursor.execute('SELECT SUM(total_transactions) FROM processed_files')
            total_file_transactions = cursor.fetchone()[0] or 0
            
            cursor.execute('SELECT SUM(successful_matches) FROM processed_files')
            total_successful_matches = cursor.fetchone()[0] or 0
            
            # Transaction statistics
            cursor.execute('SELECT COUNT(*) FROM processed_transactions')
            total_transactions = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM processed_transactions WHERE refund_processed = 1')
            refunds_processed = cursor.fetchone()[0]
            
            return {
                'total_files_processed': total_files,
                'total_file_transactions': total_file_transactions,
                'total_successful_matches': total_successful_matches,
                'total_cached_transactions': total_transactions,
                'refunds_processed': refunds_processed,
                'cache_db_path': self.db_path
            }
    
    def clear_cache(self, older_than_days: int = None):
        """
        Clear cache entries, optionally only those older than specified days.
        
        Args:
            older_than_days: Only clear entries older than this many days (optional)
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            if older_than_days:
                cutoff_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute('''
                    DELETE FROM processed_transactions 
                    WHERE processed_timestamp < datetime(?, '-{} days')
                '''.format(older_than_days), (cutoff_date,))
                
                cursor.execute('''
                    DELETE FROM processed_files 
                    WHERE processed_timestamp < datetime(?, '-{} days')
                '''.format(older_than_days), (cutoff_date,))
            else:
                cursor.execute('DELETE FROM processed_transactions')
                cursor.execute('DELETE FROM processed_files')
            
            conn.commit()
