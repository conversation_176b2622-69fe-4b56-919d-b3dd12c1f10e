from django.conf import settings
from django.http import JsonResponse
from rest_framework.response import Response
from rest_framework import status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from singlelogin.models import BlackListedJWT
import jwt
import json
from main.helper.logging_utils import log_info



class CheckBlackListJWTMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # One-time configuration and initialization.

    def __call__(self, request):

        
        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')


        auth_header = request.headers.get('Authorization', '')

        token_type, _, credentials = auth_header.partition(' ')

        hash_token = BlackListedJWT.hash_jwt(
            init_token=credentials
        )

        
        if BlackListedJWT.objects.filter(token=hash_token, blacklisted=True).exists():
            response = JsonResponse(
                {"error": "651", "message": "authentication failed"}, status=status.HTTP_407_PROXY_AUTHENTICATION_REQUIRED
            )
        else:
            response = self.get_response(request)

        # if BlackListedJWT.objects.filter(token=hash_token, blacklisted=True).exists():
        #     response = JsonResponse(
        #         {"error": "651", "message": "authentication failed"}, status=status.HTTP_401_UNAUTHORIZED
        #     )

        # else:
        #     response = self.get_response(request)

        # Code to be executed for each request/response after
        # the view is called.

        return response
    


class SingleLoginCaseMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.login_url = settings.APP_LOGIN_URL

        # One-time configuration and initialization.

    def format_response(self, response: Response) -> Response:
        response.accepted_renderer = JSONRenderer()
        response.accepted_media_type = "application/json"
        response.renderer_context = {}
        response.render()
        return response


    def __call__(self, request):


        if self.login_url not in request.build_absolute_uri():
            response = self.get_response(request)

        else:
            response = self.get_response(request)


            # Get the serial no if attached
            request_body = json.loads(request.body)
            request_email = request_body.get("email")
            request_serial_no = request_body.get("serial_no")


           

        # Code to be executed for each request before
        # the view (and later middleware) are called.


            if isinstance(response, Response):
                # you need to change private attribute `_is_render` 
                # to call render second time
                response._is_rendered = False 
                response.render()


                new_response = response.data
                get_token = new_response.get("access")

                if not get_token:
                    return self.get_response(request)

                try:
                    decoded_token = jwt.decode(get_token, settings.SECRET_KEY, algorithms=["HS256"])
                    user_id = int(decoded_token.get("user_id"))
                    
                    # Get IP ADDRESS
                    address = request.META.get('HTTP_X_FORWARDED_FOR')
                    if address:
                        ip_addr = address.split(',')[-1].strip()
                    else:
                        ip_addr = request.META.get('REMOTE_ADDR')

                    # Get User Agent
                    user_agent = request.META.get('HTTP_USER_AGENT')
                    # user_agent = get_user_agent(request)


                    create_blacklist = BlackListedJWT.create_hashed_jwt(
                        user_id=user_id,
                        init_token=get_token,
                        ip_addr=ip_addr,
                        user_agent=user_agent,
                        email=request_email,
                        serial_no=request_serial_no,
                    )

                    if create_blacklist is False:
                        bad_resp = Response(
                            {
                                "error": "596",
                                "message": "Error Occured. Try Again"
                            },
                            status=status.HTTP_400_BAD_REQUEST
                        )
                        return self.format_response(bad_resp)

                    
                except jwt.exceptions.PyJWTError as e:
                    log_info(str(e))

                    bad_resp = Response(
                        {
                            "error": "596",
                            "message": "Error Occured. Try Again"
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )
                    return self.format_response(bad_resp)


        # Code to be executed for each request/response after
        # the view is called.

        return response
    