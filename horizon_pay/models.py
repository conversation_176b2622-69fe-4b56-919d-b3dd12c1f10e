import requests
import uuid
import jwt
import ast
import json

from django.db import models, transaction
from django.db.models import Q
from django.conf import settings
from django.core.exceptions import ValidationError
from accounts.models import CashOutChargeBand, Escrow, OtherCommissionsRecord, Transaction, WalletSystem, AccountSystem, \
    InAppTransactionNotification, \
    DebitCreditRecordOnAccount, PromoCodeData, SendCommissionScheduler, MerchantIncentive
from main.models import ConstantTable, OtherServiceDetail, User, list_of_allowed_terminal_users
from horizon_pay.helpers.helper_function import create_cashout_rrn, format_hapticks_data, get_card_bank, refire_cash_out_callback
from liberty_pay.settings import cloud_messaging
from datetime import datetime
from email.policy import default
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


class CardTransaactionRawPayload(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    payload = models.TextField()
    fail_reason = models.TextField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

class CardTransaction(models.Model):

    SUCCESSFUL = "SUCCESSFUL"
    FAILED = "FAILED"

    TRANSACTION_STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (FAILED, "FAILED"),
    ]

    date_created = models.DateTimeField(auto_now_add=True)
    transaction = models.ForeignKey(
        Transaction,
        related_name="card_transactions",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    user_email = models.EmailField(null=True, blank=True)
    transaction_object_id = models.UUIDField(editable=False, null=True, blank=True)
    card_transaction_id = models.UUIDField(default=uuid.uuid4, editable=False)
    reference_number = models.CharField(max_length=200, null=True, blank=True)
    resultCode = models.CharField(max_length=200, null=True, blank=True)
    stan = models.CharField(max_length=200, null=True, blank=True)
    amount = models.FloatField(null=True, blank=True)
    liberty_commission = models.FloatField(null=True, blank=True)
    sales_rep = models.BooleanField(default=False)
    final_liberty_rev = models.FloatField(null=True, blank=True)
    liberty_profit = models.FloatField(null=True, blank=True)
    ro_profit = models.FloatField(null=True, blank=True)
    agent_profit = models.FloatField(null=True, blank=True)
    merchant_id = models.CharField(max_length=100, null=True, blank=True)
    terminal_id = models.CharField(max_length=100, null=True, blank=True)
    type_of_user = models.CharField(max_length=500, null=True, blank=True)
    host_resp_code = models.CharField(max_length=100, null=True, blank=True)
    host_resp_msg = models.CharField(max_length=100, null=True, blank=True)
    auth_code = models.CharField(max_length=100, null=True, blank=True)
    merchant_name = models.CharField(max_length=100, null=True, blank=True)
    merchant_address = models.CharField(max_length=100, null=True, blank=True)
    acquirer_name = models.CharField(max_length=100, null=True, blank=True)
    timestamp = models.CharField(max_length=100, null=True, blank=True)
    app_label = models.CharField(max_length=100, null=True, blank=True)
    pan_number = models.CharField(max_length=100, null=True, blank=True)
    expire_date =models.CharField(max_length=100, null=True, blank=True)
    holder_name =models.CharField(max_length=100, null=True, blank=True)
    ptsp_name = models.CharField(max_length=100, null=True, blank=True)
    ptsp_contact = models.CharField(max_length=100, null=True, blank=True)
    device_serial_number = models.CharField(max_length=100, null=True, blank=True)
    base_app_ver = models.CharField(max_length=100, null=True, blank=True)
    trace_num = models.CharField(max_length=100, null=True, blank=True)
    ip_addr = models.CharField(max_length=200, null=True, blank=True)
    status = models.CharField(
        max_length=150, choices=TRANSACTION_STATUS_CHOICES, default="PENDING"
    )
    send_money_by_card_resp = models.TextField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    bank_performance_checked = models.BooleanField(default=False)
    payload = models.TextField(null=True, blank=True)

    def __str__(self):
        return str(self.card_transaction_id)

    @classmethod
    def settle_for_other_services(cls, receiver_reference, unique_reference, transaction_type, service_comm, service_name, narration, escrow_id):

        get_service_object = OtherServiceDetail.objects.filter(service_name=service_name, is_active=True).last()
        if get_service_object:
            get_wallet = get_service_object.user.wallets.filter(wallet_type="COLLECTION").last()
            if get_wallet:

                service_user_instance = get_service_object.user

                receiver_transaction = Transaction.objects.create(
                    user=service_user_instance,
                    wallet_id=get_wallet.wallet_id,
                    wallet_type=get_wallet.wallet_type,
                    transaction_type=transaction_type,
                    amount=service_comm,
                    liberty_reference=receiver_reference,
                    unique_reference=unique_reference,
                    liberty_commission=0.00,
                    total_amount_received=service_comm,
                    escrow_id=escrow_id,
                    source_account_name = "LIBERTYPAY LIMITED",
                    narration=narration,
                    status="PENDING",
                    transaction_leg="EXTERNAL",
                )

                fund_service_user_balance = WalletSystem.fund_balance(
                    user=service_user_instance,
                    wallet=get_wallet,
                    amount=service_comm,
                    trans_type="OTH_SERVICES_COMM",
                    transaction_instance_id = receiver_transaction.transaction_id
                )

                service_user_balance_before = fund_service_user_balance["balance_before"]

                service_user_balance_after = WalletSystem.get_balance_after(
                    user = service_user_instance,
                    balance_before=service_user_balance_before,
                    total_amount=service_comm,
                    is_credit=True
                )

                receiver_transaction.status = "SUCCESSFUL"
                receiver_transaction.balance_before = service_user_balance_before
                receiver_transaction.balance_after = service_user_balance_after
                receiver_transaction.save()

                receiver_not_token=service_user_instance.firebase_key
                receiver_not_title="Payment Received"
                receiver_not_body=f"You have recieved a CREDIT of N{service_comm} from LIBERTYPAY LIMITED"
                receiver_not_data={"amount_sent": f"{service_comm}", "available_balance": f"{get_wallet.available_balance}"}

                send_out_notification = cloud_messaging.send_broadcast(
                    token=receiver_not_token,
                    title=receiver_not_title,
                    body=receiver_not_body,
                    data=receiver_not_data
                )

                InAppTransactionNotification.create_in_app_transaction_notification(
                    user=service_user_instance,
                    title=receiver_not_title,
                    message_body=receiver_not_body
                )

                WalletSystem.transaction_alert_notfication_manager(
                    user=service_user_instance,
                    amount=float(service_comm),
                    cr_dr="CR",
                    narration=f"{narration}-FRM LIBERTYPAY LIMITED//CASH OUT COMM",
                    from_wallet_type=get_wallet.wallet_type,
                    transaction_instance_id=receiver_transaction.transaction_id
                )

            else:
                pass
        else:
            pass

    @classmethod
    def settle_card_fund_to_wallet(cls, amount, user, wallet_id, wallet_type, from_provider_type, transaction_instance_id, commission_obj, rrn, escrow_id, initialized_trans_data: 'StartCashOutTran'):


        tid = initialized_trans_data.tID


        get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=user)

        liberty_profit = commission_obj["liberty_profit"]
        ro_profit = commission_obj["ro_profit"]
        agent_profit = commission_obj["agent_profit"]

        # Handle Liberty, Agent Profit On Cash Out

        if liberty_profit > 0:
            handle_liberty_commisson = OtherCommissionsRecord.create_and_top_up_other_commissions_for_liberty(
                agent = user,
                sales_rep = get_sales_rep.sales_rep if get_sales_rep else None,
                amount = amount,
                transaction_id = transaction_instance_id,
                transaction_type = "CASH_OUT",
                transaction_reason = "Liberty Commission",
                total_profit = commission_obj["total_charge"],
                liberty_profit = liberty_profit,
                ro_cash_profit = ro_profit,
                agent_cash_profit = agent_profit
            )


        if agent_profit > 0:
            handle_agent_commisson = OtherCommissionsRecord.create_and_top_up_other_commissions_for_agent(
                agent = user,
                sales_rep = get_sales_rep.sales_rep if get_sales_rep else None,
                amount = amount,
                transaction_id = transaction_instance_id,
                transaction_type = "CASH_OUT",
                transaction_reason = "Cash Back On Cash Out",
                total_profit = commission_obj["total_charge"],
                liberty_profit = liberty_profit,
                ro_cash_profit = ro_profit,
                agent_cash_profit = agent_profit
            )

        if get_sales_rep is None:
            final_profit = liberty_profit + ro_profit

        else:
            final_profit = liberty_profit

            # Handle RO Profit On Cash Out

            if ro_profit > 0:
                handle_ro_commission = OtherCommissionsRecord.create_and_top_up_sales_rep_commissions(
                    agent = user,
                    sales_rep = get_sales_rep.sales_rep,
                    amount = amount,
                    transaction_id = transaction_instance_id,
                    transaction_type = "CASH_OUT",
                    transaction_reason = f"Commission on Cash Out By {user.bvn_first_name}" if user.bvn_first_name else f"Commission on Cash Out By {user.email}",
                    total_profit = commission_obj["total_charge"],
                    liberty_profit = liberty_profit,
                    ro_cash_profit = ro_profit,
                    agent_cash_profit = agent_profit
                )
            else:
                pass

        log_info(f"PRINTING SENDCOMMISSION DATA: user: {user} wallet_id: {wallet_id} amount:"
                 f" {float(amount)}\nprovider: {from_provider_type}\ntrans_comm_id: {str(transaction_instance_id)}\nescro_id: {escrow_id}")

        # Create SendCommissionScheduler
        SendCommissionScheduler.objects.create(
            user=user, wallet_id=wallet_id, wallet_type=wallet_type, amount=float(final_profit), provider=from_provider_type,
            transaction_commission_id=str(transaction_instance_id), transfer_leg="TRNS_CB_COMM_CASH",
            escrow_id=str(escrow_id), transaction_sub_type="CASHOUTCOMM"
        )
        # send_commission = WalletSystem.pay_commission_to_liberty(
        #     user_id=user.id,
        #     wallet_id=wallet_id,
        #     wallet_type=wallet_type,
        #     liberty_commission = final_profit,
        #     from_provider_type=from_provider_type,
        #     transaction_commission_id = transaction_instance_id,
        #     transfer_leg="TRNS_CB_COMM_CASH",
        #     get_escrow_id=escrow_id,
        #     transaction_sub_type="CASHOUTCOMM"
        # )


        if tid.startswith("2215") and amount > 8000:

            # NIBSS
            nibbs_revenue = ConstantTable.get_constant_table_instance().mbbs_cash_out_fee_value
            if nibbs_revenue > 0:
                nibss_service_receiver_reference = Transaction.create_liberty_reference(suffix="LP-CSH-OUT-CMM")
                nibss_settlement = cls.settle_for_other_services(
                    receiver_reference = nibss_service_receiver_reference,
                    unique_reference = f"{rrn}-{transaction_instance_id}",
                    transaction_type = "FUND_BUDDY",
                    service_comm = nibbs_revenue,
                    service_name = "MBBS",
                    narration = f"LP-MBBS-CASHOUT-{rrn}",
                    escrow_id = escrow_id
                )


            # HAPTICKS
            hapticks_revenue = ConstantTable.get_constant_table_instance().aptics_cash_out_fee_value
            if hapticks_revenue > 0:
                hapticks_service_receiver_reference = Transaction.create_liberty_reference(suffix="LP-CSH-OUT-CMM")
                hapticks_settlement = cls.settle_for_other_services(
                    receiver_reference = hapticks_service_receiver_reference,
                    unique_reference = f"{rrn}-{transaction_instance_id}",
                    transaction_type = "FUND_BUDDY",
                    service_comm = hapticks_revenue,
                    service_name = "HAPTICS",
                    narration = f"LP-CASHOUT-{rrn}",
                    escrow_id = escrow_id
                )


        # print(send_commission)
        # return send_commission

    @staticmethod
    def settle_money_function(user, amount, wallet_id, wallet_type, liberty_commission, transaction_instance_id, from_provider_type, commission_obj, rrn, escrow_id, initialized_trans_data: 'StartCashOutTran', ledger_commission_type="CASH", ledger_commission_amount=0):

        send_by_card = initialized_trans_data.send_money_by_card

        account_type = AccountSystem.get_account_type(user, wallet_type, from_provider_type)

        settle_money_to_wallet = CardTransaction.settle_card_fund_to_wallet(
            amount = amount,
            user = user,
            wallet_id = wallet_id,
            wallet_type = wallet_type,
            from_provider_type = from_provider_type,
            transaction_instance_id = transaction_instance_id,
            commission_obj=commission_obj,
            rrn=rrn,
            escrow_id=escrow_id,
            initialized_trans_data = initialized_trans_data
        )

#####################################################################################################

        wallet_instance = WalletSystem.objects.filter(wallet_id=wallet_id).last()

        # SEND OUT APP NOTIFICATION
        receiver_not_token=user.firebase_key
        receiver_not_title="Payment Received"
        receiver_not_body=f"You have recieved a CREDIT of N{amount} from CARD WITHDRAWAL. COMM - {liberty_commission}"
        receiver_not_data={"amount_sent": f"{amount}", "available_balance": f"{wallet_instance.available_balance}"}

        send_out_notification = cloud_messaging.send_broadcast(
            token=receiver_not_token,
            title=receiver_not_title,
            body=receiver_not_body,
            data=receiver_not_data
        )

        InAppTransactionNotification.create_in_app_transaction_notification(
            user=user,
            title=receiver_not_title,
            message_body=receiver_not_body
        )


        if send_by_card == True:
            pass
        else:
            send_credit_alert = WalletSystem.transaction_alert_notfication_manager(
                user=user,
                amount = amount,
                liberty_commission = liberty_commission,
                cr_dr = "CR",
                narration = "LP-CARD-POS-",
                from_wallet_type = wallet_type,
                transaction_instance_id = transaction_instance_id
            )


class HorizonPayTable(models.Model):
    payload = models.TextField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


    ip_addr = models.CharField(max_length=150, blank=True, null=True)
    ip_correct = models.BooleanField(default=False)
    retry_resolve = models.BooleanField(default=False)
    is_resolved = models.BooleanField(default=False)
    as_chargeback = models.BooleanField(default=False)
    wrong_tid = models.BooleanField(default=False)
    retry_response = models.TextField(blank=True, null=True)
    mti = models.CharField(max_length=255, blank=True, null=True)
    amount = models.FloatField(default=0.00, blank=True, null=True)
    terminal_id = models.CharField(max_length=255, blank=True, null=True)
    response_code = models.CharField(max_length=255, blank=True, null=True)
    response_description = models.CharField(max_length=255, blank=True, null=True)
    pan = models.CharField(max_length=255, blank=True, null=True)
    stan = models.CharField(max_length=255, blank=True, null=True)
    auth_code = models.CharField(max_length=255, blank=True, null=True)
    transaction_time = models.DateTimeField(blank=True, null=True)
    reversal = models.CharField(max_length=255, blank=True, null=True)
    merchant_id = models.CharField(max_length=255, blank=True, null=True)
    merchant_name = models.CharField(max_length=255, blank=True, null=True)
    merchant_address = models.CharField(max_length=255, blank=True, null=True)
    rrn = models.CharField(max_length=255, blank=True, null=True)
    is_rejected = models.BooleanField(default=False)
    force_resolve = models.BooleanField(default=False)


    def clean(self):
        if self.retry_response == True and self.wrong_tid and self.as_chargeback == True:
            raise ValidationError(f'Cannot have as_chargeback and is_resolved and retry_response checked at the same time')
        elif self.as_chargeback and self.wrong_tid:
            raise ValidationError(f'Cannot have wrong_tid and as_chargedback checked at the same time')


    def save(self, *args, **kwargs):
        if self.is_resolved == False:

            if self.retry_resolve:
                try:
                    data = json.loads(self.payload)
                except:
                    data = ast.literal_eval(self.payload)

                if not User.objects.filter(terminal_id=data["terminalId"]).exists():
                    self.retry_response = "ERROR!!! Terminal ID DOES NOT EXIST"
                    self.wrong_tid = True

                else:

                    resp_data = format_hapticks_data(data=data)
                    send_data = refire_cash_out_callback(resp_data)
                    self.retry_response = send_data


                    self.is_resolved = True
            pass

        super(HorizonPayTable, self).save(*args, **kwargs)


    @classmethod
    def resolve_and_settle_funds(cls, user, data, initialized_trans_data):

        amount = initialized_trans_data.amount_started
        rrn = initialized_trans_data.rrn
        ledger_commission_amount = initialized_trans_data.ledger_commission
        ledger_commission_type = initialized_trans_data.commission_type
        type_of_user = user.type_of_user

        if type_of_user in ["AGENT", "LOTTO_AGENT", "LIBERTY_RETAIL"]:
            commission_obj = CashOutChargeBand.calculate_cash_out_agent_fees(amount, user=user)
            liberty_commission = commission_obj["total_charge"]

        elif type_of_user == "MERCHANT" or user.is_paybox_merchant:
            commission_obj = CashOutChargeBand.calculate_cash_out_merchant_fees(amount)
            liberty_commission = commission_obj["total_charge"]

        elif initialized_trans_data.trx_owner == "PAYBOX":
            commission_obj = CashOutChargeBand.calculate_cash_out_merchant_fees(amount)
            liberty_commission = commission_obj["total_charge"]

        else:
            commission_obj = CashOutChargeBand.calculate_cash_out_agent_fees(amount, user=user)
            liberty_commission = commission_obj["total_charge"]

        log_info("IN HERE 1")

        if initialized_trans_data.send_money_by_card:
            transaction_type = "CARD_TRANSACTION_FUND_TRANSFER"
        else:
            transaction_type = "CARD_TRANSACTION_FUND"

        # Check if user_type is merchant and has incentive for free transaction
        is_incentive, liberty_commission = MerchantIncentive.get_liberty_commission_from_incentive(
            user, liberty_commission, transaction_type
        )

        resolvable_amount = amount - liberty_commission
        liberty_reference = Transaction.create_liberty_reference(suffix="LP-CARD_INW")
        get_current_provider = ConstantTable.default_account_provider()

        check_wallet = WalletSystem.objects.filter(Q(user=user) & Q(wallet_type="COLLECTION")).last()

        if check_wallet:
            wallet_id = check_wallet.wallet_id
            wallet_type = check_wallet.wallet_type

            user_balance_before = check_wallet.available_balance
            user_balance_after = WalletSystem.get_balance_after(
                user = user,
                balance_before=user_balance_before,
                total_amount=resolvable_amount,
                is_credit=True
            )

        else:
            wallet_id = None
            wallet_type = None

            user_balance_before = 0.00
            user_balance_after = 0.00
        log_info("IN HERE 2")

        transaction_instance = Transaction.objects.filter(unique_reference=rrn).last()

        sms_charge = WalletSystem.get_sms_charge(user, transaction_type, resolvable_amount)

        host_resp_msg = data.get("responseDescription")
        host_resp_code = data.get("responseCode")
        get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=user)
        if get_sales_rep is None:
            sales_rep = True
            final_liberty_rev = commission_obj["liberty_profit"] + commission_obj["ro_profit"]
        else:
            sales_rep = False
            final_liberty_rev = commission_obj["liberty_profit"]

        if initialized_trans_data.trx_owner == "PAYBOX":
            # Truncating this if transaction owner is PAYBOX, to avoid instant settlement on LibertyPay
            log_info("THIS IS A PAYBOX CARD TRANSACTION")

            card_trans = CardTransaction.objects.create(
                user_email=user.email, reference_number=rrn, resultCode=host_resp_code, stan=data.get("STAN"), amount=amount,
                liberty_commission=liberty_commission, sales_rep=sales_rep, final_liberty_rev=final_liberty_rev,
                liberty_profit=commission_obj["liberty_profit"], ro_profit=commission_obj["ro_profit"], agent_profit=commission_obj["agent_profit"],
                merchant_id=data.get("merchantId"), terminal_id=initialized_trans_data.tID, type_of_user=type_of_user, host_resp_code=host_resp_code,
                host_resp_msg=host_resp_msg, auth_code=data.get("authCode"), merchant_name=data.get("merchantName"),
                merchant_address=data.get("merchantAddress"), acquirer_name="N/A", timestamp=data.get("transactionTime"), app_label="N/A",
                pan_number=data.get("PAN"), expire_date="N/A", holder_name="N/A", ptsp_name="N/A", ptsp_contact="N/A", device_serial_number="N/A",
                base_app_ver="N/A", trace_num="N/A", ip_addr=initialized_trans_data.ip_addr, payload=str(data)
            )

            log_info("CARD TRANSACTION CREATED: ", card_trans.id)

            if host_resp_code == "00":
                initialized_trans_data.transaction_status = "S"
                card_trans.status = "SUCCESSFUL"
                # Send fund to Paybox Float account
                paybox_user_email = ConstantTable.get_constant_table_instance().paybox_float_email
                paybox_user_filter = User.objects.filter(email__iexact=paybox_user_email)
                paybox_float_user = paybox_user_filter.last() if paybox_user_filter else None
                if paybox_float_user is not None:
                    paybox_float_wallet = WalletSystem.objects.filter(user=paybox_float_user, wallet_type="COLLECTION").last()
                    WalletSystem.fund_balance(
                        user=paybox_float_user,
                        wallet=paybox_float_wallet,
                        amount=resolvable_amount,
                        trans_type="PAYBOX_SALES",
                        transaction_instance_id=transaction_instance.transaction_id if transaction_instance else rrn,
                        unique_reference=rrn
                    )

                    # Send commission to Liberty
                    SendCommissionScheduler.objects.create(
                        user=user, wallet_id=wallet_id, wallet_type=wallet_type, amount=float(liberty_commission), provider="VFD", transaction_commission_id=str(card_trans.id),
                        transfer_leg="PAYBOX_SALES"
                    )

                    # WalletSystem.pay_commission_to_liberty(
                    #     user.id, wallet_id, wallet_type, liberty_commission, "VFD", transaction_commission_id=card_trans.id,
                    #     transfer_leg="PAYBOX_SALES"
                    # )
            else:
                card_trans.status = "FAILED"
                initialized_trans_data.transaction_status = "F"

            card_trans.save()
            initialized_trans_data.amount_resolved = resolvable_amount
            initialized_trans_data.trans_complete = True
            initialized_trans_data.save()
            log_info("SAVE STARTCASHOUT")

        else:

            # check_if_transaction_exist = Transaction.objects.filter(unique_reference=rrn).exists()
            if not transaction_instance:
                escrow_instance = Escrow.objects.create(
                    user=user,
                    transfer_type="SEND_COMMISSION",
                    send_money_by_card = True if initialized_trans_data.send_money_by_card else False,
                    send_by_card_rrn = rrn if initialized_trans_data.send_money_by_card else None,
                )

                host_resp_msg = data.get("responseDescription")
                host_resp_code = data.get("responseCode")

                with transaction.atomic():
                    transaction_instance = Transaction.objects.create(
                        user = user,
                        wallet_id = wallet_id,
                        wallet_type = wallet_type,
                        account_provider = get_current_provider,
                        transaction_type = transaction_type,
                        amount = amount,
                        balance_before = user_balance_before,
                        balance_after = user_balance_after,
                        liberty_commission = liberty_commission,
                        total_amount_received = amount,
                        escrow_id = escrow_instance.escrow_id,
                        status = "PENDING",
                        user_trans_band = user.trans_band,
                        provider_status = host_resp_msg,
                        liberty_reference = liberty_reference,
                        ip_addr=initialized_trans_data.ip_addr,
                        unique_reference = rrn,
                        terminal_id = initialized_trans_data.tID,
                        type_of_user = type_of_user,
                        transaction_mode = transaction_type,
                        payload = str(data),
                        is_incentive=is_incentive
                    )
                    log_info("IN HERE 3")
                    get_sales_rep = OtherCommissionsRecord.check_if_sales_rep_exists(downline_user=user)
                    if get_sales_rep is None:
                        sales_rep = True
                        final_liberty_rev = commission_obj["liberty_profit"] + commission_obj["ro_profit"]
                    else:
                        sales_rep = False
                        final_liberty_rev = commission_obj["liberty_profit"]

                    card_transaction_instance = CardTransaction.objects.create(
                        user_email = user.email,
                        transaction = transaction_instance,
                        transaction_object_id = transaction_instance.transaction_id,
                        reference_number = rrn,
                        resultCode = host_resp_code,
                        stan = data.get("STAN"),
                        amount = amount,
                        liberty_commission = liberty_commission,
                        sales_rep = sales_rep,
                        final_liberty_rev = final_liberty_rev,
                        liberty_profit = commission_obj["liberty_profit"],
                        ro_profit = commission_obj["ro_profit"],
                        agent_profit = commission_obj["agent_profit"],
                        merchant_id = data.get("merchantId"),
                        terminal_id = initialized_trans_data.tID,
                        type_of_user = type_of_user,
                        host_resp_code = host_resp_code,
                        host_resp_msg = host_resp_msg,
                        auth_code = data.get("authCode"),
                        merchant_name = data.get("merchantName"),
                        merchant_address = data.get("merchantAddress"),
                        acquirer_name = "N/A",
                        timestamp = data.get("transactionTime"),
                        app_label = "N/A",
                        pan_number = data.get("PAN"),
                        expire_date = "N/A",
                        holder_name = "N/A",
                        ptsp_name = "N/A",
                        ptsp_contact = "N/A",
                        device_serial_number = "N/A",
                        base_app_ver = "N/A",
                        trace_num = "N/A",
                        ip_addr = initialized_trans_data.ip_addr,
                        payload = str(data)
                    )

            else:
                card_transaction_instance = CardTransaction.objects.filter(Q(transaction=transaction_instance) | Q(reference_number=rrn)).last()
                if not card_transaction_instance:
                    card_transaction_instance = CardTransaction.objects.create(
                        user_email = user.email,
                        transaction = transaction_instance,
                        transaction_object_id = transaction_instance.transaction_id,
                        reference_number = rrn,
                        resultCode = host_resp_code,
                        stan = data.get("STAN"),
                        amount = amount,
                        liberty_commission = liberty_commission,
                        sales_rep = sales_rep,
                        final_liberty_rev = final_liberty_rev,
                        liberty_profit = commission_obj["liberty_profit"],
                        ro_profit = commission_obj["ro_profit"],
                        agent_profit = commission_obj["agent_profit"],
                        merchant_id = data.get("merchantId"),
                        terminal_id = initialized_trans_data.tID,
                        type_of_user = type_of_user,
                        host_resp_code = host_resp_code,
                        host_resp_msg = host_resp_msg,
                        auth_code = data.get("authCode"),
                        merchant_name = data.get("merchantName"),
                        merchant_address = data.get("merchantAddress"),
                        acquirer_name = "N/A",
                        timestamp = data.get("transactionTime"),
                        app_label = "N/A",
                        pan_number = data.get("PAN"),
                        expire_date = "N/A",
                        holder_name = "N/A",
                        ptsp_name = "N/A",
                        ptsp_contact = "N/A",
                        device_serial_number = "N/A",
                        base_app_ver = "N/A",
                        trace_num = "N/A",
                        ip_addr = initialized_trans_data.ip_addr,
                        payload = str(data)
                    )
                log_info("IN HERE 4")
                host_resp_code = card_transaction_instance.host_resp_code
                host_resp_msg = card_transaction_instance.host_resp_msg
                escrow_instance = Escrow.objects.filter(escrow_id=transaction_instance.escrow_id).last()
                # If Transaction exist and is not successful yet

            log_info("IN HERE 5")
            if transaction_instance and transaction_instance.status not in ["SUCCESSFUL", "FAILED"]:
                log_info("Out here for cashout", user.email)
                        # update and change to success
                if not transaction_instance.promo_code:
                    promo_code = PromoCodeData.generate_promo_code(
                        user=user,
                        transaction_id=transaction_instance.transaction_id,
                        trans_type=transaction_type
                    )
                    transaction_instance.promo_code = promo_code

                log_info("IN HERE 6")
                if host_resp_code == "00" and "approved" in host_resp_msg.casefold():

                    try:
                        # Fund User Wallet
                        fund_user_wallet = WalletSystem.fund_balance(
                            user = user,
                            wallet = check_wallet,
                            amount = amount - liberty_commission,
                            trans_type = "CASH_OUT",
                            transaction_instance_id = transaction_instance.transaction_id,
                            unique_reference = rrn
                        )

                    except Exception as e:
                        log_error("error", e)

                        if isinstance(e, dict) and e.get("status") == "505":
                            unique_reference = e.get("unique_reference")
                            transaction_instance_id = e.get("transaction_instance_id")

                            if transaction_instance.transaction_id == transaction_instance_id and transaction_instance.unique_reference == unique_reference:
                                pass
                            else:
                                raise Exception("Debit Credit Unique field Error")

                    log_info("IN HERE 7")
                    # update and change to success
                    transaction_instance.status = "SUCCESSFUL"
                    transaction_instance.sms_charge = sms_charge
                    card_transaction_instance.status = "SUCCESSFUL"
                    transaction_instance.save()
                    card_transaction_instance.save()
                    initialized_trans_data.transaction_status = "S"

                    log_info("IN HERE 7")

                    # initialized_trans_data.amount_resolved = amount
                    # initialized_trans_data.trans_complete = True
                    # initialized_trans_data.save()


                    # # check_debit_credit
                    # if DebitCreditRecordOnAccount.objects.filter(user=user, entry="CREDIT", transaction_instance_id=transaction_instance.transaction_id).exists():
                    #     response = {
                    #         "status": "error",
                    #         "message": "Credit Exists"
                    #     }

                    # else:

                    # SETTLE MONEY
                    if check_wallet:
                        log_info("SETTLING MMONEY FOR CARD TRANSFER.!!")
                        CardTransaction.settle_money_function(
                            user=user,
                            amount=amount,
                            wallet_id=wallet_id,
                            wallet_type=wallet_type,
                            liberty_commission=liberty_commission,
                            transaction_instance_id=transaction_instance.transaction_id,
                            from_provider_type=get_current_provider,
                            commission_obj = commission_obj,
                            rrn=rrn,
                            escrow_id = escrow_instance.escrow_id,
                            initialized_trans_data = initialized_trans_data,
                            ledger_commission_type = ledger_commission_type,
                            ledger_commission_amount = ledger_commission_amount,
                        )
                        log_info("IN SEND MONEY BY TRANSFER.!!!")
                        if initialized_trans_data.send_money_by_card:
                            from horizon_pay.tasks import send_money_by_card_transfer_out

                            send_money_by_card_transfer_out(
                                data = initialized_trans_data.send_money_data,
                                user_access_token = initialized_trans_data.access_token,
                                card_trans_id = card_transaction_instance.id
                            )
                            log_info("COMPLETED SEND MONEY BY TRANSFER.!!!")
                        else:
                            pass

                else:
                    # update its details
                    log_error("IN HERE 8 FAILED")
                    transaction_instance.status = "FAILED"
                    transaction_instance.balance_after = user_balance_before
                    card_transaction_instance.status = "FAILED"
                    initialized_trans_data.transaction_status = "F"

                log_info("IN HERE AT 8")
                transaction_instance.save()
                card_transaction_instance.save()

            log_info("IN HERE 9")
            initialized_trans_data.amount_resolved = resolvable_amount
            initialized_trans_data.trans_complete = True
            initialized_trans_data.save()

        return {
                "status": True,
                "message": "response received"
            }


    @classmethod
    def new_function_to_resolve_and_settle_cashout(cls, rrn, terminal_id, amount, response_json, force_resolve=False, resolved_by=None):
        log_info(f"{rrn, amount}")
        try:
            initialized_trans = StartCashOutTran.objects.get(rrn=rrn, trans_complete=False, amount_started=amount)
            log_info("HERE -1")
        except:
            return None

        # if initialized_trans_qs.count() > 1:
        #     return False

        # initialized_trans = initialized_trans_qs.first()
        # tID_user = User.objects.filter(terminal_id=terminal_id).last()
        # if initialized_trans:
        tID_user = initialized_trans.user

        manual_refund_exist = ManualRefund.objects.filter(rrn=rrn).first()
        log_info("HERE 0")
        log_info(f"THIS IS THE TID :: {terminal_id}")

        response = False

        if not initialized_trans.trans_complete and not manual_refund_exist:
            log_info("HERE 1")
            if True: #initialized_trans.tID == terminal_id or force_resolve == True:
                log_info("HERE 1")
                HorizonPayTable.resolve_and_settle_funds(
                    user = tID_user,
                    data = response_json,
                    initialized_trans_data = initialized_trans
                )
                log_info("HERE 2")

                response = True

            if initialized_trans.tID != terminal_id:
                initialized_trans.tIDMismatch = True
                initialized_trans.returned_tid = terminal_id
                initialized_trans.resolved_by = resolved_by if resolved_by is not None else "Auto Allowed"
                initialized_trans.save()

            log_info("HERE 3")

        return response
        # else:


        #     else:
        # # else:
        # #     get_data = cls.objects.filter(rrn=rrn).last()
        # #     if get_data:
        # #         get_data.is_resolved = True
        # #         get_data.is_rejected = True
        # #         get_data.save()

        # return "Done"


class StartCashOutTran(models.Model):

    TRX_OWNER_CHOICES = (
        ('PAYBOX', 'PAYBOX'),
        ('LIBPAY', 'LIBPAY'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    tID = models.CharField(max_length=500)
    rrn = models.CharField(max_length=150)
    ip_addr = models.CharField(max_length=150, null=True, blank=True)
    trx_owner = models.CharField(max_length=50, null=True, blank=True, choices=TRX_OWNER_CHOICES)
    trx_owner_alerted = models.BooleanField(default=False)
    amount_started = models.FloatField(null=True, blank=True)
    trans_complete = models.BooleanField(default=False)
    transaction_status = models.CharField(max_length=5, default="P", blank=True, null=True)
    amount_resolved = models.FloatField(null=True, blank=True)
    send_money_data = models.JSONField(null=True, blank=True)
    send_money_by_card = models.BooleanField(default=False)
    ledger_commission = models.FloatField(default=0.00)
    commission_type = models.CharField(max_length=100, null=True, blank=True)
    access_token = models.CharField(max_length=500, null=True, blank=True)
    tIDMismatch = models.BooleanField(default=False)
    returned_tid = models.CharField(max_length=100, null=True, blank=True)
    resolved_by = models.CharField(max_length=100, null=True, blank=True)
    trx_owner_response_payload = models.CharField(max_length=300, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    pan = models.TextField(blank=True, null=True)
    terminal_serial = models.ForeignKey("horizon_pay.TerminalSerialTable", on_delete=models.SET_NULL, blank=True, null=True)
    limit_amount = models.FloatField(null=True, blank=True, help_text="Sum of transaction amount and previous successful transaction")

    @classmethod
    def create_trans_rrn(
            cls, user, tID, amount_started, send_money_data, ip_addr, ledger_commission, commission_type, access_token, terminal_serial,
            trx_owner="LIBPAY"
    ):

        with transaction.atomic():

            create_rrn = create_cashout_rrn(tID)

            if cls.objects.filter(rrn=create_rrn).exists():
                return cls.create_trans_rrn(
                    user=user,
                    tID=tID,
                    amount_started=amount_started,
                    send_money_data=send_money_data,
                    ip_addr = ip_addr,
                    ledger_commission = ledger_commission,
                    commission_type = commission_type,
                    access_token = access_token,
                    trx_owner = trx_owner,
                    terminal_serial=terminal_serial
                )

            if send_money_data:
                send_money_data["send_by_card_rrn"] = create_rrn

            cls.objects.create(
                user = user,
                tID = tID,
                rrn = create_rrn,
                ip_addr = ip_addr,
                amount_started = amount_started,
                send_money_data = send_money_data,
                send_money_by_card = True if send_money_data else False,
                ledger_commission = ledger_commission,
                commission_type = commission_type,
                access_token = access_token,
                trx_owner = trx_owner,
                terminal_serial=terminal_serial
            )

            return create_rrn


    @classmethod
    def dynamic_start_tid(cls, amount: float, get_terminal_fk: 'TerminalSerialTable'):
        band_amount = 9100
        if amount >= band_amount and (get_terminal_fk.terminal_id.startswith("210") or get_terminal_fk.arca_clear) and get_terminal_fk.arca_terminal_id is not None:
            return get_terminal_fk.arca_terminal_id

        return get_terminal_fk.terminal_id


class TransactionOwners(models.Model):
    postback_url = models.CharField(max_length=500, null=True, blank=True)
    owner_code = models.CharField(max_length=500, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Transaction Owners"


class CardSignatureVerification(models.Model):
    user_email = models.EmailField(max_length=500, null=True, blank=True)
    token = models.CharField(max_length=500)
    tID = models.CharField(max_length=500, null=True, blank=True)
    amount_started = models.FloatField(null=True, blank=True)
    amount_settled = models.FloatField(null=True, blank=True)
    is_checked = models.BooleanField(default=True)
    is_correct = models.BooleanField(default=False)
    twofa_started = models.BooleanField(default=False)
    twofa_complete = models.BooleanField(default=False)
    verification_reason = models.TextField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


    @classmethod
    def create_new_token(cls, token, user_email=None):
        custom_signature = f"{settings.CARD_LOAD_TOKONE}"

        find_token = cls.objects.filter(token=token).exists()
        if find_token:
            response = {
                "status": "FAILED"
            }

        else:
            try:
                verification = jwt.decode(token, custom_signature, algorithms=["HS256"])

                # {'exp': 1665260219, 'iat': 1665259799, 'pld': {'amount': '100.00'}}


                token_instance = cls.objects.create(
                    user_email = user_email,
                    twofa_started = True,
                    token = token,
                    verification_reason = verification,
                    is_correct = True,
                    is_checked = True,
                    amount_started = float(verification["pld"]["amount"])
                )


                response = {
                    "status": "SUCCESSFUL"
                }

            except jwt.exceptions.PyJWTError as e:
                token_instance = cls.objects.create(
                    user_email = user_email,
                    token = token,
                    verification_reason = e,
                    amount_started = float(verification["pld"]["amount"])
                )

                response = {
                    "status": "FAILED"
                }

        return response


    @classmethod
    def verify_signature_token(cls, token, tID, amount, user_email=None):
        custom_signature = f"{settings.CARD_LOAD_TOKONE}"

        find_token = cls.objects.filter(token=token).last()
        if find_token:
            if find_token.twofa_started and not find_token.twofa_complete:
                find_token.twofa_complete = True
                find_token.tID = tID
                find_token.amount_settled = amount
                find_token.save()

                response = {
                    "status": "SUCCESSFUL"
                }

            else:
                response = {
                    "status": "FAILED"
                }

        else:
            try:
                verification = jwt.decode(token, custom_signature, algorithms=["HS256"])

                token_instance = cls.objects.create(
                    user_email = user_email,
                    token = token,
                    verification_reason = verification,
                    is_correct = True,
                    tID = tID,
                    amount_settled = amount
                )

                response = {
                    "status": "SUCCESSFUL"
                }

            except jwt.exceptions.PyJWTError as e:
                token_instance = cls.objects.create(
                    user_email = user_email,
                    token = token,
                    verification_reason = e,
                    tID = tID,
                    amount_settled = amount
                )

                response = {
                    "status": "FAILED"
                }

        return response


class CardIssuerTable(models.Model):
    performance_type = models.CharField(max_length=200, blank=True, null=True)
    bank = models.CharField(max_length=200, blank=True, null=True)
    brand = models.CharField(max_length=200, blank=True, null=True)
    card_bin = models.CharField(max_length=100, blank=True, null=True)

    performance = models.IntegerField(default=100)
    overall_performance = models.IntegerField(default=100)
    front_view_performance = models.IntegerField(default=100)

    previous_performance = models.IntegerField(default=100)
    overall_previous_performance = models.IntegerField(default=100)
    front_view_previous_performance = models.IntegerField(default=100)

    daily_count_pass = models.PositiveIntegerField(default=0)
    daily_count_fail = models.PositiveIntegerField(default=0)
    daily_count_total = models.PositiveIntegerField(default=0)

    overall_count_pass = models.PositiveIntegerField(default=0)
    overall_count_fail = models.PositiveIntegerField(default=0)
    overall_count_total = models.PositiveIntegerField(default=0)

    front_view_count_pass = models.PositiveIntegerField(default=0)
    front_view_count_fail = models.PositiveIntegerField(default=0)
    front_view_count_total = models.PositiveIntegerField(default=0)

    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    # brand = models.CharField(max_length=200, blank=True, null=True)

    @classmethod
    def create_issuer_logs(cls, pan, **kwargs):
        bank = get_card_bank(pan)
        bank_data, created = cls.objects.get_or_create(bank=bank["bank"], brand=bank["brand"])
        # if created:
        bank_data.daily_count += 1
        bank_data.overall_count += 1
        bank_data.save()


    def save(self, *args, **kwargs):
        self.daily_count_total = self.daily_count_pass + self.daily_count_fail
        self.overall_count_total = self.overall_count_pass + self.overall_count_fail
        self.front_view_count_total = self.front_view_count_pass + self.front_view_count_fail

        if self.front_view_count_total > 0:
            if self.front_view_count_pass < 1:
                self.front_view_performance = 0
            else:
                self.front_view_performance = (self.front_view_count_pass/self.front_view_count_total) * 100

        super(CardIssuerTable, self).save(*args, **kwargs)

# def generate_isw_tid(num_to_add=0):
#     """
#     Function to generate ISW TIDS
#     """

#     try:
#         last_id = ISWTerminalTable.objects.latest('id').isw_terminal_id
#         prefix = last_id[:4]  # Get the prefix "2LBP"
#         last_number = int(last_id[4:])  # Get the last numeric part
#         new_number = last_number + num_to_add + 1  # Increment the numeric part
#         new_id = f"{prefix}{new_number:04}"

#         if ISWTerminalTable.objects.filter(isw_terminal_id=new_id).exists():
#             return generate_isw_tid(num_to_add=num_to_add+1)

#     except ISWTerminalTable.DoesNotExist:
#         # If no previous terminal ID exists, start with "2LBP0001"
#         new_id = "2LBP0001"


#     ISWTerminalTable.objects.create(
#         isw_terminal_id = new_id
#     )

#     return new_id


class TerminalSerialTable(models.Model):

    TERMINAL_USER_CHOICE = [(x, x) for x in list_of_allowed_terminal_users]

    ACTIVE_PROVIDER_CHOICE = [
        ("LIBERTY_PAY", "LIBERTY_PAY"),
        ("HERITAGE", "HERITAGE"),
        ("ZENITH", "ZENITH"),
    ]

    terminal_id = models.CharField(max_length=200)
    isw_terminal_id = models.CharField(max_length=200, null=True, blank=True)
    arca_terminal_id = models.CharField(max_length=200, null=True, blank=True)
    terminal_serial = models.CharField(max_length=200)
    terminal_provider = models.CharField(max_length=200)
    user = models.ForeignKey(User, related_name="terminal_serial_set", on_delete=models.PROTECT, null=True, blank=True)
    type_of_user = models.CharField(max_length=200, choices=TERMINAL_USER_CHOICE, blank=True, null=True)
    is_registered = models.BooleanField(default=False)
    # register_tid_payload = models.TextField(blank=True, null=True)
    liberty_draw_response = models.TextField(blank=True, null=True)
    arca_clear = models.BooleanField(default=False)
    is_suspended = models.BooleanField(default=False)
    reason = models.TextField(blank=True, null=True)
    unsuspend_token = models.CharField(max_length=300, null=True, blank=True)
    date_suspended = models.DateTimeField(null=True, blank=True)
    date_unsuspended = models.DateTimeField(null=True, blank=True)
    date_assigned = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        user_is = self.user.email if self.user else "No-User"

        return f"{self.terminal_id}-{user_is}"

    def clean(self):
        if self.user:
            get_other_tied_users = User.objects.filter(Q(terminal_id=self.terminal_id) | Q(terminal_serial=self.terminal_serial))
            if len(get_other_tied_users) > 1:
                raise ValidationError(f'This TID or SN is tied to the following users {get_other_tied_users}')
            elif len(TerminalSerialTable.objects.filter(user=self.user)) > 0:
                raise ValidationError(f'Sorry, you cannot have more than one terminal tied to your account')
            elif self.user and not self.type_of_user:
                raise ValidationError(f'Sorry, please enter a type of user')
            elif self.user in get_other_tied_users:
                pass

    def save(self, *args, **kwargs):
        if self.pk:
            # Notify liberty_draw of un-assigned terminal
            prev = TerminalSerialTable.objects.get(id=self.pk)
            if prev.user and self.user is None:
                prev_user = prev.user
                payload = json.dumps({
                    "agent_name": prev_user.full_name,
                    "agent_email": prev_user.email,
                    "agent_phone_number": prev_user.phone_number
                })
                headers = {"Content-Type": "application/json"}
                url = str(settings.LIBERTY_DRAW_BASE_URL) + "agent/api/terminal_re_assignment_request/"

                try:
                    response = requests.post(url, headers=headers, data=payload, timeout=30)
                    self.liberty_draw_response = response.text
                except requests.exceptions.RequestException as e:
                    self.liberty_draw_response = e
                except ValueError as e:
                    self.liberty_draw_response = e

        if self.user:
            self.user.terminal_id = self.terminal_id
            self.user.terminal_serial = self.terminal_serial
            self.user.terminal_provider = self.terminal_provider
            self.user.type_of_user = self.type_of_user
            if not self.date_assigned:
                self.date_assigned = datetime.now()

            self.user.date_assigned = self.date_assigned
            self.user.save()
        else:
            self.date_assigned = None

            get_user_with_tid = User.objects.filter(Q(terminal_id=self.terminal_id) | Q(terminal_serial=self.terminal_serial))

            if get_user_with_tid:
                for user in get_user_with_tid:
                    user.terminal_id = None
                    user.terminal_serial = None
                    user.terminal_provider = None
                    user.date_assigned = None
                    user.save()

        super(TerminalSerialTable, self).save(*args, **kwargs)

    @staticmethod
    def reregister_tid(queryset: None):
        from .tasks import batch_insert_terminal_ids_task

        data_list = []
        # get_data = queryset
        # if queryset:
        # else:
        #     get_data = TerminalSerialTable.objects.filter(is_registered=False).filter(Q(isw_terminal_id__isnull=False) | Q(arca_terminal_id__isnull=False))

        for data in queryset:
            data: TerminalSerialTable

            if not data.arca_terminal_id:
                data.arca_terminal_id = ArcaPayTerminalTable.generate_arca_tids()
            if not data.isw_terminal_id:
                data.isw_terminal_id = ISWTerminalTable.generate_isw_tids()

            data.save()

            data_list.append(
                {
                    "deviceSerialNumber": data.terminal_serial,
                    "nibssTerminalId": data.terminal_id,
                    "iswTerminalId": data.isw_terminal_id,
                    "arcaPayTerminalId": data.arca_terminal_id
                }
            )

        # batch_insert_terminal_ids_task(data_list=data_list)
        batch_insert_terminal_ids_task.apply_async(
            queue="horizon_queue",
            kwargs={
                "data_list": data_list
            }
        )

        return True


class ISWTerminalTable(models.Model):
    isw_terminal_id = models.CharField(max_length=200, unique=True)
    assigned = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)



    @classmethod
    def generate_isw_tids(cls, num_add=0):
        existing_ids = cls.objects.values_list('isw_terminal_id', flat=True)
        model_b_ids = TerminalSerialTable.objects.values_list('isw_terminal_id', flat=True)

        unused_ids = [id for id in existing_ids if id not in model_b_ids]
        unused_instances = cls.objects.filter(isw_terminal_id__in=unused_ids, assigned=False)

        if unused_instances:
            return unused_instances.last().isw_terminal_id


        last_id_qs = cls.objects.last()

        if not last_id_qs:
            next_id = "2LBPA001"
        else:

            last_id = last_id_qs.isw_terminal_id

            last_numeric_part = int(last_id[-3:])

            # Extract the alphabetic part of the last_id (e.g., "2LBPA999" => "2LBPA")
            alphabetic_part = last_id[:-3]

            # Determine the next numeric part and alphabetic part
            if last_numeric_part == 999:
                # If the numeric part reaches 999, reset to 1 and move to the next alphabet
                next_numeric_part = 1
                last_alphabetic_char = alphabetic_part[-1]
                next_alphabetic_char = chr(ord(last_alphabetic_char) + 1)

                # If we have reached 'Z999', we need to reset to 'A001'
                if next_alphabetic_char > 'Z':
                    next_alphabetic_char = 'A'
                    raise IndexError
            else:
                # If the numeric part is less than 999, increment it by 1 and keep the same alphabet
                next_numeric_part = last_numeric_part + 1 + num_add
                next_alphabetic_char = alphabetic_part[-1]

            # Generate the next ID
            next_id = alphabetic_part[:-1] + next_alphabetic_char + str(next_numeric_part).zfill(3)

        try:
            cls.objects.get(isw_terminal_id=next_id)
            TerminalSerialTable.objects.get(isw_terminal_id=next_id)

            return cls.generate_isw_tids(num_add=num_add+1)
        except:
            pass


        cls.objects.create(
            isw_terminal_id = next_id
        )

        return next_id

class NewCardSaveDataTable(models.Model):
    register_tid_initial_payload = models.TextField(blank=True, null=True)
    register_tid_response = models.TextField(blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class ArcaPayTerminalTable(models.Model):
    arca_terminal_id = models.CharField(max_length=200, unique=True)
    assigned = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def generate_arca_tids(cls, num_add=0):
        existing_ids = cls.objects.values_list('arca_terminal_id', flat=True)
        model_b_ids = TerminalSerialTable.objects.values_list('arca_terminal_id', flat=True)

        unused_ids = [id for id in existing_ids if id not in model_b_ids]
        unused_instances = cls.objects.filter(arca_terminal_id__in=unused_ids, assigned=False)

        if unused_instances:
            return unused_instances.last().arca_terminal_id

        last_id_qs = cls.objects.last()

        if not last_id_qs:
            next_id = "20LP0002"
        else:

            last_id = last_id_qs.arca_terminal_id
            last_numeric_part = int(last_id[-3:])
            alphabetic_part = last_id[:-3]

            # Determine the next numeric part and alphabetic part
            if last_numeric_part == 999:
                # If the numeric part reaches 999, reset to 1 and move to the next alphabet
                next_numeric_part = 1
                last_alphabetic_char = alphabetic_part[-1]
                next_alphabetic_char = chr(ord(last_alphabetic_char) + 1)

                # If we have reached 'Z999', we need to reset to 'A001'
                if next_alphabetic_char > 'Z':
                    next_alphabetic_char = 'A'
                    raise IndexError
            else:
                next_numeric_part = last_numeric_part + 1 + num_add
                next_alphabetic_char = alphabetic_part[-1]

            # Generate the next ID
            next_id = alphabetic_part[:-1] + next_alphabetic_char + str(next_numeric_part).zfill(3)

        try:
            cls.objects.get(arca_terminal_id=next_id)
            TerminalSerialTable.objects.get(arca_terminal_id=next_id)

            return cls.generate_arca_tids(num_add=num_add+1)
        except:
            pass

        cls.objects.create(
            arca_terminal_id = next_id
        )

        return next_id


class RecordTerminalSerial(models.Model):

    # assignor_user = models.ForeignKey(User, on_delete=models.CASCADE)
    terminal = models.ForeignKey(TerminalSerialTable, on_delete=models.CASCADE)
    collector_name = models.CharField(max_length=200)
    date_assigned = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


    def clean(self):

        if len(RecordTerminalSerial.objects.filter(terminal=self.terminal)) > 0:
            raise ValidationError(f'Sorry, you cannot have more than one terminal on this dashboard')


    def save(self, *args, **kwargs):
        if self.terminal.user:
            # self.terminal.user.initial_assignor = self.assignor_user.email
            self.terminal.user.initial_handler = self.collector_name
            if self.date_assigned:
                pass
            else:
                self.date_assigned = datetime.now()

            self.terminal.user.save()
        else:
            self.date_assigned = None

            get_user_with_tid = User.objects.filter(Q(terminal_id=self.terminal.terminal_id) | Q(terminal_serial=self.terminal.terminal_serial))

            if get_user_with_tid:
                for user in get_user_with_tid:
                    user.initial_assignor = None
                    user.initial_handler = None
                    user.save()
            pass

        super(RecordTerminalSerial, self).save(*args, **kwargs)


class ManualRefund(models.Model):
    rrn = models.CharField(max_length=200, unique=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class DebugTerminal(models.Model):
    serial_no = models.CharField(max_length=300, unique=True)
    is_active = models.BooleanField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.serial_no


class TerminalRetrieval(models.Model):
    TERMINAL_RETRIEVAL_STATUS = (
        ("retrieved", "Terminal with Supervisor"), ("returned", "Terminal with Company"), ("reassigned", "Terminal with new agent")
    )
    terminal = models.ForeignKey(TerminalSerialTable, on_delete=models.CASCADE)
    retrieved_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, blank=True, null=True, related_name="terminal_retrieved_by",
        help_text="supervisor who has retrieved this terminal"
    )
    retrieved_from = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="terminal_retrieved_from")
    reassigned = models.BooleanField(default=False)
    reassigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True, related_name="terminal_reassigned_to")
    status = models.CharField(max_length=100, choices=TERMINAL_RETRIEVAL_STATUS, default="retrieved")
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.terminal.terminal_id

