import json
from datetime import datetime

from django.contrib.auth import get_user_model, authenticate
from django.db.models import Sum
from django.utils import timezone
from djoser.conf import settings
from rest_framework import serializers

from accounts.models import Transaction
from horizon_pay.models import CardIssuerTable, CardT<PERSON>saction, HorizonPayTable, CardTransaactionRawPayload, StartCashOutTran, TerminalSerialTable
from accounts.serializers import TransactionSerializerCardTransactionReport
from liberty_pay.exceptions import InvalidRequestException
from main.helper.helper_function import mask_pan, convert_number_to_datetime
from main.models import ConstantTable, UserOtherAccount
from send_money.serializers import SendMoneyBankTransferSerializer
from horizon_pay.helpers.helper_function import generate_receipt_base64
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical

User = get_user_model()

COMMISSION_TYPE_CHOICES = [
    ("BANK", "BANK"),
    ("CASH", "CASH")
]


class Start2FASerializer(serializers.Serializer):
    signature_token = serializers.CharField(required=True)


class BeginTransactionSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=True)
    tID = serializers.CharField(required=True)
    trx_owner = serializers.CharField(required=False)
    ledger_commission = serializers.FloatField(required=False, allow_null=True)
    commission_type = serializers.ChoiceField(choices= COMMISSION_TYPE_CHOICES, required=False, allow_null=True, allow_blank=True)
    send_money_data = SendMoneyBankTransferSerializer(required=True, allow_null=True)
    pan = serializers.CharField(required=False)


class CardIssuerSerializer(serializers.ModelSerializer):
    class Meta:
        model = CardIssuerTable
        fields = ["performance_type", "bank", "brand", "front_view_performance"]


class CardWithdrawSerializer(serializers.Serializer):
    admin_password = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    user_by_admin = serializers.EmailField(required=False, allow_null=True, allow_blank=True)
    resultCode = serializers.IntegerField(required=False, allow_null=True)
    refNum = serializers.CharField(required=True, allow_null=True)
    hostRespCode = serializers.CharField(required=True, allow_null=True)
    hostRespMsg = serializers.CharField(required=True, allow_null=True)
    mID = serializers.CharField(required=True, allow_null=True)
    tID = serializers.CharField(required=True, allow_null=True)
    merchantName = serializers.CharField(required=True, allow_null=True)
    merchantAddress = serializers.CharField(required=True, allow_null=True)
    acquirerName = serializers.CharField(required=True, allow_null=True)
    appLabel = serializers.CharField(required=True, allow_null=True)
    pAN = serializers.CharField(required=True, allow_null=True)
    authCode = serializers.CharField(required=True, allow_null=True)
    expireDate = serializers.CharField(required=True, allow_null=True)
    holderName = serializers.CharField(required=True, allow_null=True)
    ptspName = serializers.CharField(required=True, allow_null=True)
    ptspContact = serializers.CharField(required=True, allow_null=True)
    deviceSN = serializers.CharField(required=True, allow_null=True)
    baseAppVer = serializers.CharField(required=True, allow_null=True)
    traceNum = serializers.CharField(required=True, allow_null=True)
    stan = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    amount = serializers.CharField(required=True, allow_null=True)
    timestamp = serializers.CharField(required=True, allow_null=True)
    ledger_commission = serializers.FloatField(required=False, allow_null=True)
    commission_type = serializers.ChoiceField(choices= COMMISSION_TYPE_CHOICES, required=False, allow_null=True, allow_blank=True)
    signature_token = serializers.CharField(required=True, allow_null=False, allow_blank=False)


class CardTransactionReportSerializer(serializers.ModelSerializer):
    transaction = TransactionSerializerCardTransactionReport(read_only=True)
    Status = serializers.CharField(source="resultCode")
    Pan = serializers.CharField(source="pan_number")
    ExpiryDate = serializers.CharField(source="expire_date")
    Stan = serializers.CharField(source="stan")
    RRN = serializers.CharField(source="reference_number")
    Timestamp = serializers.CharField(source="timestamp")

    class Meta:
        model = CardTransaction
        fields = ["Pan", "Status", "ExpiryDate", "Stan",
        "RRN", "Timestamp", "transaction"]

# class BankPerformanceSerializer(serializers.ModelSerializer):
#     # transaction = TransactionSerializerCardTransactionReport(read_only=True)

#     # Status = serializers.CharField(source="resultCode")
#     # Pan = serializers.CharField(source="pan_number")
#     # ExpiryDate = serializers.CharField(source="expire_date")
#     # Stan = serializers.CharField(source="stan")
#     # RRN = serializers.CharField(source="reference_number")
#     # Timestamp = serializers.CharField(source="timestamp")

#     class Meta:
#         model = CardIssuerSerializer
#         fields = ["Pan", "Status", "ExpiryDate", "Stan",
#         "RRN", "Timestamp", "transaction"]


class HorizonPayTableSerializer(serializers.ModelSerializer):
    class Meta:
        model = HorizonPayTable
        fields = "__all__"


class CardInputSerializer(serializers.Serializer):
    # MTI = serializers.CharField(required=True, allow_blank=True)
    amount = serializers.FloatField(required=True)
    tID = serializers.CharField(required=True)
    hostRespCode = serializers.CharField(required=True)
    hostRespMsg = serializers.CharField(required=True)
    pAN = serializers.CharField(required=True, allow_blank=True)
    stan = serializers.CharField(required=True, allow_blank=True)
    authCode = serializers.CharField(required=True, allow_blank=True)
    timestamp = serializers.CharField(required=True, allow_blank=True)
    # reversal = serializers.BooleanField(required=True)
    mID = serializers.CharField(required=True, allow_blank=True)
    merchantName = serializers.CharField(required=True, allow_blank=True)
    merchantAddress = serializers.CharField(required=True, allow_blank=True)
    refNum = serializers.CharField(required=True, allow_blank=True)


class CashOutFeeSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=True, allow_null=False)
    user_email = serializers.EmailField(required=True, allow_null=False)


class CheckActualDepositSerializer(serializers.Serializer):
    amount = serializers.FloatField(required=True)


class CardRawPayloadSerializer(serializers.ModelSerializer):
    class Meta:
        model = CardTransaactionRawPayload
        fields = ["user", "date_created", "last_updated", "payload"]


class GetDataHorizonPayTableSerializer(serializers.ModelSerializer):
    class Meta:
        model = HorizonPayTable
        fields = ["date_created", "last_updated", "payload"]


class LimitCheckSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    pan = serializers.CharField()
    rrn = serializers.CharField()

    def create(self, validated_data):
        user = validated_data.get("user")
        encrypted_pan = validated_data.get("pan")
        rrn = validated_data.get("rrn")

        try:
            cash_out_trans = StartCashOutTran.objects.get(rrn=rrn)
        except StartCashOutTran.DoesNotExist:
            raise InvalidRequestException({"status": "error", "message": "Transaction with RRN not found"})

        constant_table = ConstantTable.get_constant_table_instance()
        terminal_fk = cash_out_trans.terminal_serial
        amount = cash_out_trans.amount_started
        today = datetime.today()

        current_customer_daily_limit = StartCashOutTran.objects.filter(
            pan__iexact=encrypted_pan, terminal_serial=terminal_fk, trans_complete=True, date_created__day=today.day
        ).aggregate(Sum("amount_started"))["amount_started__sum"] or 0

        current_limit = float(current_customer_daily_limit + amount)

        # Update Transaction
        cash_out_trans.pan = encrypted_pan
        cash_out_trans.limit_amount = current_limit
        cash_out_trans.save()

        if not (user.is_paybox_merchant or user.type_of_user == "MERCHANT"):

            # Check Daily Transaction Limit per Customer
            if current_limit > constant_table.customer_max_daily_limit:
                raise InvalidRequestException({"status": "error", "message": "Daily card limit exceeded"})

            # Check Daily Cumulative Transaction Limit per Agent
            current_agent_daily_limit = StartCashOutTran.objects.filter(
                user=user, trans_complete=True, trx_owner="LIBPAY", date_created__day=today.day
            ).aggregate(Sum("amount_started"))["amount_started__sum"] or 0

            if float(current_agent_daily_limit + amount) > constant_table.agent_max_daily_limit:
                raise InvalidRequestException({"status": "error", "message": "Daily agent limit exceeded"})

        return {"status": "success", "message": "Within Limit"}


class LimitCheckNewSerializerIn(serializers.Serializer):
    # user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    pan = serializers.CharField()
    rrn = serializers.CharField()

    def create(self, validated_data):
        # user = validated_data.get("user")
        encrypted_pan = validated_data.get("pan")
        rrn = validated_data.get("rrn")

        try:
            cash_out_trans = StartCashOutTran.objects.get(rrn=rrn)
        except StartCashOutTran.DoesNotExist:
            raise InvalidRequestException({"status": "error", "message": "Transaction with RRN not found"})

        user = cash_out_trans.user
        constant_table = ConstantTable.get_constant_table_instance()
        terminal_fk = cash_out_trans.terminal_serial
        amount = cash_out_trans.amount_started
        today = datetime.today()

        current_customer_daily_limit = StartCashOutTran.objects.filter(
            pan__iexact=encrypted_pan, terminal_serial=terminal_fk, transaction_status="S", date_created__day=today.day
        ).aggregate(Sum("amount_started"))["amount_started__sum"] or 0

        current_limit = float(current_customer_daily_limit + amount)

        # Update Transaction
        cash_out_trans.pan = encrypted_pan
        cash_out_trans.limit_amount = current_limit
        cash_out_trans.save()

        if not (user.is_paybox_merchant or user.type_of_user == "MERCHANT" or cash_out_trans.trx_owner == "PAYBOX") or user.is_pos_agent:
            # Check Daily Transaction Limit per Customer
            if current_limit > constant_table.customer_max_daily_limit:
                raise InvalidRequestException({"status": "error", "message": "Daily card limit exceeded"})

            # Check Daily Cumulative Transaction Limit per Agent
            current_agent_daily_limit = StartCashOutTran.objects.filter(
                user=user, transaction_status="S", date_created__day=today.day).aggregate(Sum("amount_started"))["amount_started__sum"] or 0

            if float(current_agent_daily_limit + amount) > constant_table.agent_max_daily_limit:
                raise InvalidRequestException({"status": "error", "message": "Daily agent limit exceeded"})

        return {"status": "success", "message": "Within Limit"}


class TerminalSerialTableSerializer(serializers.ModelSerializer):
    user_email = serializers.EmailField(source="user.email", read_only=True)

    class Meta:
        model = TerminalSerialTable
        fields = [
            "terminal_id",
            "isw_terminal_id",
            "arca_terminal_id",
            "terminal_serial",
            "terminal_provider",
            "type_of_user",
            "is_registered",
            "arca_clear",
            "date_assigned",
            "date_created",
            "last_updated",
            "user_email", 
        ]


class ReversalCallbackSerializerIn(serializers.Serializer):
    rrn = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    amount = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    primaryAccountNumber = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    processCode = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    transactionDateAndTime = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    transactionTime = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    transactionDate = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    cardExpiryDate = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    posEntryMode = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    cardSequenceNumber = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    posConditionCode = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    posPinCaptureCode = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    acquiringInstitutionIdCode = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    serviceRestrictionCode = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    stan = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    terminalId = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    pinBlock = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    iccCardSystemData = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    track2Data = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    """
    {
        "amount":"************",
        "primaryAccountNumber":"****************",
        "processCode":"500000",
        "transactionDateAndTime":"**********",
        "transactionTime":"131330",
        "transactionDate":"0404",
        "cardExpiryDate":"2605",
        "posEntryMode":"051",
        "cardSequenceNumber":"001",
        "posConditionCode":"00",
        "posPinCaptureCode":"04",
        "acquiringInstitutionIdCode":"********",
        "serviceRestrictionCode":"221",
        "stan":"131330",
        "rrn":"************",
        "terminalId":"2LBP8205",
        "pinBlock":"F5743F137E7C9344",
        "iccCardSystemData":"",
        "track2Data":"****************D2605221012055244"
    }
    """

    def create(self, validated_data):
        rrn = validated_data.get("rrn")
        amount = validated_data.get("amount")
        trans_datetime = validated_data.get("transactionDateAndTime")
        card_sequence = validated_data.get("cardSequenceNumber")
        stan = validated_data.get("stan")
        term_id = validated_data.get("terminalId")
        pan = validated_data.get("primaryAccountNumber")

        try:
            start_trans = StartCashOutTran.objects.get(rrn=rrn)
            start_trans.transaction_status = "F"
            start_trans.save()
            trans_user = start_trans.user
        except StartCashOutTran.DoesNotExist:
            return {"status": False, "message": "rrn not found"}

        amount = int(amount) / 100

        # Mark transaction and card transaction as FAILED
        payment_transaction = Transaction.objects.filter(unique_reference=rrn)
        if not payment_transaction.exists():
            Transaction.objects.create(
                user=trans_user, unique_reference=rrn, transaction_type="CARD_TRANSACTION_FUND", status="FAILED", amount=float(amount)
            )
        payment_transaction.update(status="FAILED")
        CardTransaction.objects.filter(reference_number=rrn).update(status="FAILED")

        user_name = f"@{start_trans.user.username}"
        user_address = start_trans.user.get_full_address
        maxed_pan = mask_pan(pan) if pan and len(pan) >= 10 else ""
        new_date = convert_number_to_datetime(trans_datetime)

        data = {
            "Amount": amount,
            "RRN": rrn,
            "Terminal": term_id,
            "Stan": stan,
            "Date": new_date,
            "PrimaryAccountNumber": maxed_pan,
            "ResponseCode": "00",
            "Authcode": "N/A",
            "CardSequence": card_sequence
        }

        image_base64 = generate_receipt_base64(data, user_name, user_address)
        validated_data["image"] = image_base64
        # Create CardTransaction Payload
        CardTransaactionRawPayload.objects.create(user=trans_user, payload=json.dumps(validated_data))

        return {"status": True, "message": "response received"}


class ValidateCardTransactionSerializerIn(serializers.Serializer):
    """
    amount, date, rrn, email
    """
    amount = serializers.FloatField()
    transaction_date = serializers.DateField()
    transaction_time = serializers.TimeField()
    email = serializers.EmailField()

    def create(self, validated_data):
        amount = validated_data.get("amount")
        transaction_date = validated_data.get("transaction_date")
        transaction_time = validated_data.get("transaction_time")
        email = validated_data.get("email")

        # Combine date and time for filtering
        transaction_datetime = datetime.combine(transaction_date, transaction_time)
        # Add 1 hour to transaction time to account for timezone difference
        transaction_datetime += timezone.timedelta(hours=1)

        # Get transactions within a small time window (e.g., 5 minutes)
        time_window = timezone.timedelta(minutes=5)
        start_datetime = transaction_datetime - time_window
        end_datetime = transaction_datetime + time_window

        start_cash_outs = StartCashOutTran.objects.filter(
            user__email=email, amount_started=amount, date_created__gte=start_datetime, date_created__lte=end_datetime
        )
        
        result = list()
        if start_cash_outs:
            # Confirm cashout transaction record on HorizonPay table
            for cash in start_cash_outs:
                rrn = cash.rrn
                amount = cash.amount_started
                transaction_period = cash.date_created
                data = dict()
                data['transaction_date'] = transaction_period
                data['amount'] = amount
                status_code = "100"
                try:
                    horizon_record = HorizonPayTable.objects.get(rrn=rrn)
                    payload_data = json.loads(horizon_record.payload)
                    log_info(str(payload_data))
                    description = str(payload_data.get("responseDescription")).upper()
                    if description == "SYSTEM MALFUNCTION":
                        status_code = "96"
                    if description == "INVALID MERCHANT":
                        status_code = "03"
                    if description == "PIN TRIES EXCEEDED PICK UP":
                        status_code = "38"
                    if description == "APPROVED":
                        status_code = "00"
                    if description == "INVALID TRANSACTION":
                        status_code = "12"
                    if description == "INCORRECT PIN":
                        status_code = "55"
                    if description == "ISSUER OR SWITCH INOPERATIVE":
                        status_code = "91"
                    if description == "ROUTING ERROR":
                        status_code = "92"
                    if description == "EXCEEDS WITHDRAWAL LIMIT":
                        status_code = "61"
                    if description == "NOT SUFFICIENT FUND":
                        status_code = "51"
                    data['detail'] = description
                    data['status_code'] = status_code
                except HorizonPayTable.DoesNotExist:
                    data['status_code'] = "15"
                    data['detail'] = "Transaction not completed"
                except json.JSONDecodeError:
                    data['status_code'] = status_code
                    data['detail'] = "Error fetching provider response"
                result.append(data)
        else:
            data = dict()
            data['transaction_date'] = transaction_date
            data['amount'] = amount
            data['status_code'] = "10"
            data['detail'] = "TRANSACTION NOT FOUND"
            result.append(data)

        return result[0]

class SuspendUnsuspendTerminalSerializerOut(serializers.Serializer):
    success = serializers.BooleanField(read_only=True, default=True)
    message = serializers.CharField(read_only=True, default="Terminal suspended successfully")
    recovery_token = serializers.CharField(read_only=True)

class SuspendUnsuspendTerminalSerializerIn(serializers.Serializer):
    terminal_serial_no = serializers.CharField()
    reason = serializers.CharField()
    action = serializers.ChoiceField(choices=[("suspend", "suspend"), ("unsuspend", "unsuspend")])
    recovery_token = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    def create(self, validated_data):
        terminal_serial_no = validated_data.get("terminal_serial_no")
        reason = validated_data.get("reason")
        action = validated_data.get("action")

        try:
            suspension_token = UserOtherAccount.generate_random_password()
            terminal = TerminalSerialTable.objects.get(terminal_serial=terminal_serial_no)
            if action == "suspend":
                # Generate a unique suspension token
                from django.contrib.auth.hashers import make_password
                terminal.is_suspended = terminal.user.terminal_suspended = True
                terminal.reason = reason
                terminal.unsuspend_token = make_password(suspension_token)
                terminal.date_suspended = timezone.now()
                terminal.user.save()

            else:
                from django.contrib.auth.hashers import check_password
                if not check_password(validated_data.get("recovery_token", ""), terminal.unsuspend_token):
                    raise InvalidRequestException({"success": False, "message": "Invalid recovery token."})

                terminal.is_suspended = terminal.user.terminal_suspended = False
                terminal.date_unsuspended = timezone.now()
                terminal.unsuspend_token = None
                terminal.reason = None
                terminal.user.save()

            terminal.save()
            return {
                "success": True, "message": f"Terminal {action}d successfully.",
                "recovery_token": suspension_token if action == "suspend" else ""
            }
        except TerminalSerialTable.DoesNotExist:
            raise InvalidRequestException({"success": False, "error": "Terminal not found."})





