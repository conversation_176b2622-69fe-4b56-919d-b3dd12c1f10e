from accounts.models import Transaction, WalletSystem, AccountSystem
from django.db.models import Count
from django.utils import timezone
from django.db.models import Sum, Count, Q, Prefetch, F, Value, Case, When, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ger<PERSON>ield, <PERSON>loat<PERSON>ield, \
    DateTime<PERSON>ield, Subquery, OuterRef
from django.db.models.functions import Coalesce, Greatest
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from datetime import datetime
import django_filters
from main.models import LoginStatus, User


class AnalyticsService:
    """
    Service class for analytics operations
    Separates business logic from the API layer
    """

    @staticmethod
    def get_transaction_analytics_by_date(date):
        """
        Calculate transaction analytics for a specific date
        Returns structured data for serialization
        """
        # Get transactions for the specified date
        transactions = Transaction.objects.filter(
            date_created__date=date,
            status=Transaction.SUCCESSFUL
        )

        # Prepare the response data
        result = {
            "filter_type": "specific_date",
            "date_range": {
                "start_date": str(date),
                "end_date": str(date)
            },
            "total_transactions": transactions.count(),
        }

        return AnalyticsService._process_transaction_data(transactions, result)

    @staticmethod
    def get_transaction_analytics_by_filter(filter_type='all_time'):
        """
        Calculate transaction analytics based on filter type
        Defaults to 'all_time' if no filter is provided
        Returns structured data for serialization
        """
        now = timezone.now()
        today = now.date()

        # Calculate date ranges based on filter type
        try:
            if filter_type == 'today':
                date_range_info = {
                    'start': today,
                    'end': today,
                    'filter_query': Q(date_created__date=today)
                }
            elif filter_type == 'this_week':
                week_start = today - timedelta(days=today.weekday())
                date_range_info = {
                    'start': week_start,
                    'end': today,
                    'filter_query': Q(date_created__date__gte=week_start)
                }
            elif filter_type == 'this_month':
                month_start = today.replace(day=1)
                date_range_info = {
                    'start': month_start,
                    'end': today,
                    'filter_query': Q(date_created__date__gte=month_start)
                }
            elif filter_type == 'six_months':
                six_months_ago = today - timedelta(days=180)
                date_range_info = {
                    'start': six_months_ago,
                    'end': today,
                    'filter_query': Q(date_created__date__gte=six_months_ago)
                }
            elif filter_type == 'this_year':
                year_start = today.replace(month=1, day=1)
                date_range_info = {
                    'start': year_start,
                    'end': today,
                    'filter_query': Q(date_created__date__gte=year_start)
                }
            elif filter_type == 'all_time':
                date_range_info = {
                    'start': None,
                    'end': today,
                    'filter_query': Q()  # No date filter for all time
                }
            else:
                # Default to all_time for invalid filter types
                logger.warning(f"Invalid filter type: {filter_type}, defaulting to all_time")
                filter_type = 'all_time'
                date_range_info = {
                    'start': None,
                    'end': today,
                    'filter_query': Q()
                }

        except Exception as e:
            logger.error(f"Error calculating date range for filter {filter_type}: {str(e)}")
            # Fallback to all_time if there's an error
            filter_type = 'all_time'
            date_range_info = {
                'start': None,
                'end': today,
                'filter_query': Q()
            }

        # Get transactions based on the filter
        base_query = Transaction.objects.filter(status=Transaction.SUCCESSFUL)
        transactions = base_query.filter(date_range_info['filter_query'])

        # Prepare the response data
        result = {
            "filter_type": filter_type,
            "date_range": {
                "start_date": str(date_range_info['start']) if date_range_info['start'] else None,
                "end_date": str(date_range_info['end'])
            },
            "total_transactions": transactions.count(),
        }

        return AnalyticsService._process_transaction_data(transactions, result)

    @staticmethod
    def _process_transaction_data(transactions, result):
        """
        Process transaction data and calculate analytics
        Helper method to avoid code duplication
        """
        total_transactions = result["total_transactions"]

        if total_transactions == 0:
            result.update({
                "message": f"No transactions found for the selected {result['filter_type'].replace('_', ' ')}",
                "transaction_types": [],
                "most_used_transaction": None
            })
        else:
            # Group transactions by transaction_type and count
            transaction_counts = transactions.values('transaction_type').annotate(
                count=Count('transaction_type')
            ).order_by('-count')

            # Calculate percentages and format response
            transaction_analytics = []
            most_used_transaction = transaction_counts[0]['transaction_type'] if transaction_counts else None

            for item in transaction_counts:
                transaction_type = item['transaction_type']
                count = item['count']
                percentage = (count / total_transactions) * 100

                transaction_analytics.append({
                    'transaction_type': transaction_type,
                    'count': count,
                    'percentage': round(percentage, 2)
                })

            result.update({
                "transaction_types": transaction_analytics,
                "most_used_transaction": most_used_transaction
            })

        return result

    @staticmethod
    def get_transaction_analytics(date):
        """
        Backward compatibility method
        Maintains existing functionality for specific date queries
        """
        return AnalyticsService.get_transaction_analytics_by_date(date)


class OptimizedUserQuerySetMixin:
    """Mixin to provide optimized user queryset with all annotations"""

    def get_optimized_user_queryset(self, user_id=None):
        """Get user with all optimized annotations"""
        # Get credit and debit transaction types
        credit_types = [t[0] for t in Transaction.CREDIT_TRANS + Transaction.OTHER_CREDIT_TRANS]
        debit_types = [t[0] for t in Transaction.DEBIT_TRANS + Transaction.OTHER_DEBIT_TRANS]

        # Prefetch the latest transaction
        latest_transaction_subquery = Transaction.objects.filter(
            user_id=OuterRef('id')
        ).order_by('-date_created').values(
            'date_created', 'amount', 'transaction_type',
            'status', 'liberty_reference'
        )[:1]

        # Prefetch the latest login status
        latest_login_subquery = LoginStatus.objects.filter(
            user_id=OuterRef('id')
        ).order_by('-last_login_date')[:1]

        # Main user queryset with annotations
        user_queryset = User.objects.annotate(
            # Last login timestamp
            last_login_timestamp=Greatest(
                F('terminal_last_login'),
                F('mobile_last_login'),
                F('web_last_login')
            ),

            # Custom methods as annotations
            get_kyc_status=Case(
                When(kyc_level__gte=1, then=Value('ACTIVE')),
                default=Value('INACTIVE'),
                output_field=CharField()
            ),

            get_account_status=Case(
                When(is_suspended=True, then=Value('SUSPENDED')),
                When(is_fraud=True, then=Value('FLAGGED')),
                default=Value('ACTIVE'),
                output_field=CharField()
            ),

            # Transaction stats
            total_transactions_count=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id')
                ).values('user_id').annotate(
                    count=Count('id')
                ).values('count'),
                output_field=IntegerField()
            ),

            total_transactions_value=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id')
                ).values('user_id').annotate(
                    sum=Coalesce(Sum('amount'), 0.0)
                ).values('sum'),
                output_field=FloatField()
            ),

            # Last transaction data
            last_transaction_date=Subquery(
                latest_transaction_subquery.values('date_created'),
                output_field=DateTimeField()
            ),

            last_transaction_amount=Subquery(
                latest_transaction_subquery.values('amount'),
                output_field=FloatField()
            ),

            last_transaction_type=Subquery(
                latest_transaction_subquery.values('transaction_type'),
                output_field=CharField()
            ),

            last_transaction_status=Subquery(
                latest_transaction_subquery.values('status'),
                output_field=CharField()
            ),

            last_transaction_reference=Subquery(
                latest_transaction_subquery.values('liberty_reference'),
                output_field=CharField()
            ),

            # Wallet balance
            calculated_wallet_balance=Subquery(
                WalletSystem.objects.filter(
                    user_id=OuterRef('id')
                ).values('user_id').annotate(
                    sum=Coalesce(Sum('available_balance'), 0.0)
                ).values('sum'),
                output_field=FloatField()
            ),

            # Pending transactions
            pending_transactions_count=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id'),
                    status='PENDING'
                ).values('user_id').annotate(
                    count=Count('id')
                ).values('count'),
                output_field=IntegerField()
            ),

            pending_transactions_value=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id'),
                    status='PENDING'
                ).values('user_id').annotate(
                    sum=Coalesce(Sum('amount'), 0.0)
                ).values('sum'),
                output_field=FloatField()
            ),

            # Failed transactions
            failed_transactions_count=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id'),
                    status='FAILED'
                ).values('user_id').annotate(
                    count=Count('id')
                ).values('count'),
                output_field=IntegerField()
            ),

            failed_transactions_value=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id'),
                    status='FAILED'
                ).values('user_id').annotate(
                    sum=Coalesce(Sum('amount'), 0.0)
                ).values('sum'),
                output_field=FloatField()
            ),

            # Successful credits
            successful_credits_count=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id'),
                    status='SUCCESSFUL',
                    transaction_type__in=credit_types
                ).values('user_id').annotate(
                    count=Count('id')
                ).values('count'),
                output_field=IntegerField()
            ),

            successful_credits_value=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id'),
                    status='SUCCESSFUL',
                    transaction_type__in=credit_types
                ).values('user_id').annotate(
                    sum=Coalesce(Sum('amount'), 0.0)
                ).values('sum'),
                output_field=FloatField()
            ),

            # Successful debits
            successful_debits_count=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id'),
                    status='SUCCESSFUL',
                    transaction_type__in=debit_types
                ).values('user_id').annotate(
                    count=Count('id')
                ).values('count'),
                output_field=IntegerField()
            ),

            successful_debits_value=Subquery(
                Transaction.objects.filter(
                    user_id=OuterRef('id'),
                    status='SUCCESSFUL',
                    transaction_type__in=debit_types
                ).values('user_id').annotate(
                    sum=Coalesce(Sum('amount'), 0.0)
                ).values('sum'),
                output_field=FloatField()
            ),
        )

        # Apply user_id filter if provided
        if user_id:
            user_queryset = user_queryset.filter(id=user_id)

        # Add prefetches
        user_queryset = user_queryset.select_related(
            'user_branch'
        ).prefetch_related(
            # Prefetch wallets
            Prefetch(
                'wallets',
                queryset=WalletSystem.objects.all(),
                to_attr='wallets_prefetched'
            ),
            # Prefetch accounts
            Prefetch(
                'accounts',
                queryset=AccountSystem.objects.all(),
                to_attr='accounts_prefetched'
            ),
            # Prefetch latest login
            # Prefetch(
            #     'login_statuses',
            #     queryset=latest_login_subquery,
            #     to_attr='latest_login'
            # )
        )

        return user_queryset


class TransactionFilter(django_filters.FilterSet):
    """
    Custom filter for Transaction model with date range and email filtering
    """
    email = django_filters.CharFilter(field_name='user__email', lookup_expr='icontains')
    transaction_date = django_filters.DateFilter(field_name='date_created__date')
    transaction_date_from = django_filters.DateFilter(field_name='date_created__date', lookup_expr='gte')
    transaction_date_to = django_filters.DateFilter(field_name='date_created__date', lookup_expr='lte')
    transaction_status = django_filters.CharFilter(field_name='status', lookup_expr='iexact')
    transaction_type = django_filters.CharFilter(field_name='transaction_type', lookup_expr='iexact')

    class Meta:
        model = Transaction
        fields = ['email', 'transaction_date', 'transaction_date_from', 'transaction_date_to',
                  'transaction_status', 'transaction_type']