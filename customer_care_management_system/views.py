from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, viewsets
from django.utils import timezone
from rest_framework import generics, permissions, filters
from accounts.models import Transaction, WalletSystem, AccountSystem, TransferVerificationObject
from customer_care_management_system.reuseable import calculate_wallet_summary
from customer_care_management_system.serializers import DashboardMetricsSerializer, \
    TransactionSerializer, StandardResultsSetPagination, ProductAnalyticsSerializer, PendingTransactionSerializer, \
    UserListSerializer, BillPaymentSerializer, TransactionVolumeSerializer, \
    UserBasicInfoSerializer, UserAccountSerializer, UserAccountsListSerializer, WalletSerializer, \
    PendingBankTransferSerializer
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.views.decorators.vary import vary_on_cookie
from django_filters.rest_framework import DjangoFilterBackend
import logging
from datetime import datetime, timedelta, date
from main.models import User
from customer_care_management_system.services import AnalyticsService, OptimizedUserQuerySetMixin, TransactionFilter
from django.db.models import Sum, Count, Q, Prefetch, F, Value, Case, When, CharField, IntegerField, FloatField
from django.db.models.functions import Coalesce, Greatest
from rest_framework import views
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from rest_framework.filters import OrderingFilter, SearchFilter
from main.permissions import CustomIsAuthenticated
from django.db.models import Q, Count, Sum, Case, When, DecimalField
from rest_framework import status, filters
from rest_framework.decorators import action
from datetime import datetime

logger = logging.getLogger(__name__)


class DashboardStatsView(APIView):
    """
    API view for retrieving dashboard statistics with time period filtering.
    """

    # permission_classes = [CustomIsAuthenticated]

    def get(self, request, *args, **kwargs):
        """Handle GET requests for dashboard statistics with time period filtering."""
        try:
            # Get period from query parameters, default to 'this_month'
            period = request.query_params.get('period', 'this_month').lower()

            # Validate period parameter
            valid_periods = ['today', 'this_week', 'this_month', 'all_time']
            if period not in valid_periods:
                return Response(
                    {"error": f"Invalid period parameter. Must be one of: {', '.join(valid_periods)}"},
                    status=400
                )

            # Get dashboard data with period filter
            data = DashboardMetricsSerializer.get_dashboard_data(period)
            serializer = DashboardMetricsSerializer(data)

            return Response({
                'period': period,
                'data': serializer.data
            })
        except Exception as e:
            logger.error(f"Error in dashboard view: {str(e)}")
            return Response(
                {"error": "Unable to retrieve dashboard metrics"},
                status=500
            )


class ProductAnalyticsAPIView(APIView):
    """
    API View for transaction analytics
    Provides insights on transaction usage patterns with flexible time filtering

    Query Parameters:
    - filter: today, this_week, this_month, six_months, this_year, all_time (default: all_time)
    - date: YYYY-MM-DD (for specific date, overrides filter parameter)
    """
    # permission_classes = [CustomIsAuthenticated]

    VALID_FILTERS = [
        'today', 'this_week', 'this_month',
        'six_months', 'this_year', 'all_time'
    ]

    def get(self, request, *args, **kwargs):
        try:
            date_param = request.query_params.get('date')
            # Default filter is 'all_time' if not provided
            filter_param = request.query_params.get('filter', 'all_time')

            # Validate filter parameter
            if filter_param not in self.VALID_FILTERS:
                return Response({
                    "error": f"Invalid filter parameter. Valid options: {', '.join(self.VALID_FILTERS)}"
                }, status=status.HTTP_400_BAD_REQUEST)

            # If specific date is provided, use it instead of filter
            if date_param:
                try:
                    date_to_use = timezone.datetime.strptime(date_param, '%Y-%m-%d').date()
                    # Use the service with specific date
                    analytics_data = AnalyticsService.get_transaction_analytics_by_date(date_to_use)
                except ValueError:
                    return Response({
                        "error": "Invalid date format. Please use YYYY-MM-DD."
                    }, status=status.HTTP_400_BAD_REQUEST)
            else:
                # Use the service with filter parameter (defaults to all_time)
                analytics_data = AnalyticsService.get_transaction_analytics_by_filter(filter_param)

            # Serialize the data
            serializer = ProductAnalyticsSerializer(data=analytics_data)
            serializer.is_valid(raise_exception=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in ProductAnalyticsAPIView: {str(e)}")
            return Response({
                "error": "An error occurred while processing transaction analytics",
                "detail": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TransactionViewSet(viewsets.GenericViewSet):
    """
    A ViewSet for viewing transactions with advanced filtering capabilities.
    Provides both list and detail actions through a single endpoint.
    """
    queryset = Transaction.objects.select_related('user').order_by('-date_created')
    serializer_class = TransactionSerializer
    permission_classes = [CustomIsAuthenticated]
    pagination_class = StandardResultsSetPagination
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_class = TransactionFilter
    search_fields = ['user__email', 'liberty_reference', 'transaction_id']

    def get_queryset(self):
        """
        Return an optimized queryset with proper select_related
        """
        return Transaction.objects.select_related('user').order_by('-date_created')

    @method_decorator(cache_page(60 * 5))  # Cache for 5 minutes
    @method_decorator(vary_on_cookie)
    def list(self, request, *args, **kwargs):

        transaction_id = request.query_params.get('transaction_id', None)

        if transaction_id:

            try:
                transaction = self.get_queryset().get(transaction_id=transaction_id)
                serializer = self.get_serializer(transaction)
                return Response({
                    'status': 'success',
                    'data': serializer.data
                }, status=status.HTTP_200_OK)
            except Transaction.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': f'Transaction with ID {transaction_id} not found'
                }, status=status.HTTP_404_NOT_FOUND)
            except Exception as e:
                return Response({
                    'status': 'error',
                    'message': 'An error occurred while fetching the transaction'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            # All transactions case with pagination
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)

            if page is not None:
                serializer = self.get_serializer(page, many=True)
                response_data = self.get_paginated_response(serializer.data)
                response_data.data['filter_summary'] = self._get_filter_summary(queryset)
                return response_data

            serializer = self.get_serializer(queryset, many=True)
            return Response({
                'status': 'success',
                'count': queryset.count(),
                'filter_summary': self._get_filter_summary(queryset),
                'data': serializer.data
            }, status=status.HTTP_200_OK)

    def _get_filter_summary(self, queryset):
        """
        Provide summary of applied filters
        """
        total_amount = queryset.aggregate(total=Sum('amount'))['total'] or 0
        return {
            'total_records': queryset.count(),
            'total_amount': float(total_amount)
        }

    @action(detail=False, methods=['get'], url_path='analytics')
    @method_decorator(cache_page(60 * 10))
    def analytics(self, request):
        """
        Get transaction analytics with optional date filtering

        Query Parameters:
        - date_from: Start date (YYYY-MM-DD)
        - date_to: End date (YYYY-MM-DD)
        - date: Specific date (YYYY-MM-DD)
        """
        try:
            # Get base queryset
            queryset = self.get_queryset()

            # Apply date filters
            date_from = request.query_params.get('date_from')
            date_to = request.query_params.get('date_to')
            specific_date = request.query_params.get('date')

            if specific_date:
                try:
                    date_obj = datetime.strptime(specific_date, '%Y-%m-%d').date()
                    queryset = queryset.filter(date_created__date=date_obj)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid date format. Use YYYY-MM-DD'
                    }, status=status.HTTP_400_BAD_REQUEST)

            elif date_from or date_to:
                if date_from:
                    try:
                        from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
                        queryset = queryset.filter(date_created__date__gte=from_date)
                    except ValueError:
                        return Response({
                            'status': 'error',
                            'message': 'Invalid date_from format. Use YYYY-MM-DD'
                        }, status=status.HTTP_400_BAD_REQUEST)

                if date_to:
                    try:
                        to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
                        queryset = queryset.filter(date_created__date__lte=to_date)
                    except ValueError:
                        return Response({
                            'status': 'error',
                            'message': 'Invalid date_to format. Use YYYY-MM-DD'
                        }, status=status.HTTP_400_BAD_REQUEST)

            # Calculate analytics
            analytics_data = queryset.aggregate(
                total_transaction_count=Count('id'),
                total_transaction_value=Sum('amount'),

                # Successful transactions
                successful_transaction_count=Count(
                    Case(When(status__iexact='successful', then=1))
                ),
                successful_transaction_value=Sum(
                    Case(
                        When(status__iexact='successful', then='amount'),
                        default=0,
                        output_field=DecimalField()
                    )
                ),

                # Failed transactions
                failed_transaction_count=Count(
                    Case(When(status__iexact='failed', then=1))
                ),
                failed_transaction_value=Sum(
                    Case(
                        When(status__iexact='failed', then='amount'),
                        default=0,
                        output_field=DecimalField()
                    )
                ),

                # Pending transactions
                pending_transaction_count=Count(
                    Case(When(status__iexact='pending', then=1))
                ),
                pending_transaction_value=Sum(
                    Case(
                        When(status__iexact='pending', then='amount'),
                        default=0,
                        output_field=DecimalField()
                    )
                )
            )

            # Format response data
            response_data = {
                'status': 'success',
                'period': {
                    'date_from': date_from,
                    'date_to': date_to,
                    'specific_date': specific_date
                },
                'analytics': {
                    'total_transactions': {
                        'count': analytics_data['total_transaction_count'] or 0,
                        'value': float(analytics_data['total_transaction_value'] or 0)
                    },
                    'successful_transactions': {
                        'count': analytics_data['successful_transaction_count'] or 0,
                        'value': float(analytics_data['successful_transaction_value'] or 0)
                    },
                    'failed_transactions': {
                        'count': analytics_data['failed_transaction_count'] or 0,
                        'value': float(analytics_data['failed_transaction_value'] or 0)
                    },
                    'pending_transactions': {
                        'count': analytics_data['pending_transaction_count'] or 0,
                        'value': float(analytics_data['pending_transaction_value'] or 0)
                    }
                }
            }

            # Add success rate calculation
            total_count = analytics_data['total_transaction_count'] or 0
            successful_count = analytics_data['successful_transaction_count'] or 0

            if total_count > 0:
                response_data['analytics']['success_rate'] = round(
                    (successful_count / total_count) * 100, 2
                )
            else:
                response_data['analytics']['success_rate'] = 0

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'status': 'error',
                'message': 'An error occurred while generating analytics'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PendingTransactionView(generics.ListAPIView):
    """
    API view to handle both:
    1. Listing all pending transactions
    2. Retrieving a specific pending transaction by transaction_id passed as a query parameter

    """
    serializer_class = PendingTransactionSerializer
    # permission_classes = [CustomIsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['transaction_id', 'liberty_reference', 'user__email', 'user__phone_number', 'user_full_name']
    ordering_fields = ['date_created', 'amount']
    ordering = ['-date_created']

    def get_queryset(self):
        """
        Returns pending transactions
        CustomIsAuthenticated permission class handles authentication and permissions
        """
        queryset = Transaction.objects.filter(status=Transaction.PENDING)

        transaction_id = self.request.query_params.get('transaction_id', None)
        if transaction_id:
            queryset = queryset.filter(transaction_id=transaction_id)

        return queryset

    def list(self, request, *args, **kwargs):
        """
        Override list method to handle both list and detail requests
        """
        transaction_id = request.query_params.get('transaction_id', None)
        queryset = self.get_queryset()
        if transaction_id:
            try:
                instance = queryset.get(transaction_id=transaction_id)
                serializer = self.get_serializer(instance)
                return Response(serializer.data)
            except Transaction.DoesNotExist:
                return Response(
                    {"detail": f"Pending transaction with ID {transaction_id} not found."},
                    status=status.HTTP_404_NOT_FOUND
                )

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)


class UserProfileListAPIView(APIView, OptimizedUserQuerySetMixin):
    """
    API view to fetch all users' profiles with comprehensive filtering support.

    Supported filters:
    - kyc_status: ACTIVE, INACTIVE
    - account_status: ACTIVE, SUSPENDED, FLAGGED
    - search: Search across name, email, phone, customer_id
    - name: Filter by first_name or last_name
    - email: Filter by email
    - phone_number: Filter by phone number
    - account_type: Filter by type_of_user field
    """
    # permission_classes = [CustomIsAuthenticated ]

    @method_decorator(cache_page(60))  # Cache for 60 seconds
    def get(self, request):
        try:
            # Get pagination parameters
            page = max(1, int(request.query_params.get('page', 1)))
            page_size = min(100, max(1, int(request.query_params.get('page_size', 10))))  # Limit max page size

            # Build filtered queryset
            users_queryset = self._build_filtered_queryset(request)

            # Apply pagination
            paginator = Paginator(users_queryset, page_size)
            page_obj = paginator.get_page(page)

            # Optimize queryset with select_related and prefetch_related
            optimized_users = self._optimize_queryset(page_obj.object_list)

            # Serialize data
            serializer = UserListSerializer(optimized_users, many=True)

            return Response({
                "status": "success",
                "message": "Users retrieved successfully",
                "data": serializer.data,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_pages": paginator.num_pages,
                    "total_items": paginator.count,
                    "has_next": page_obj.has_next(),
                    "has_previous": page_obj.has_previous()
                },
                "filters_applied": self._get_applied_filters(request)
            }, status=status.HTTP_200_OK)

        except ValueError as e:
            logger.warning(f"Invalid parameter in user list request: {e}")
            return Response({
                "status": "error",
                "message": "Invalid parameter provided",
                "details": str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in UserProfileListAPIView: {e}", exc_info=True)
            return Response({
                "status": "error",
                "message": "An error occurred while retrieving users"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _build_filtered_queryset(self, request):
        """Build queryset with all applied filters"""
        queryset = User.objects.all()

        # KYC Status Filter
        kyc_status = request.query_params.get('kyc_status', '').upper()
        if kyc_status in ['ACTIVE', 'INACTIVE']:
            if kyc_status == 'ACTIVE':
                queryset = queryset.filter(kyc_level__gte=1)
            else:  # INACTIVE
                queryset = queryset.filter(kyc_level=0)

        # Account Status Filter
        account_status = request.query_params.get('account_status', '').upper()
        if account_status in ['ACTIVE',  'INACTIVE', 'SUSPENDED', 'FLAGGED']:
            if account_status == 'SUSPENDED':
                queryset = queryset.filter(is_suspended=True)
            elif account_status == 'INACTIVE':
                queryset = queryset.filter(is_active=False)

            elif account_status == 'FLAGGED':
                queryset = queryset.filter(is_fraud=True)
            elif account_status == 'ACTIVE':
                queryset = queryset.filter(is_suspended=False, is_fraud=False, is_active=True)

        # Account Type Filter
        account_type = request.query_params.get('account_type')
        if account_type:
            queryset = queryset.filter(type_of_user__icontains=account_type)

        # Name Filter (searches both first_name and last_name)
        name = request.query_params.get('name')
        if name:
            queryset = queryset.filter(
                Q(first_name__icontains=name) | Q(last_name__icontains=name)
            )

        # Email Filter
        email = request.query_params.get('email')
        if email:
            queryset = queryset.filter(email__icontains=email)

        # Phone Number Filter
        phone_number = request.query_params.get('phone_number')
        if phone_number:
            queryset = queryset.filter(phone_number__icontains=phone_number)

        # General Search Filter (searches across multiple fields)
        search_query = request.query_params.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(email__icontains=search_query) |
                Q(phone_number__icontains=search_query) |
                Q(customer_id__icontains=search_query)
            )

        # Apply ordering
        ordering = request.query_params.get('ordering', 'id')
        valid_orderings = ['id', '-id', 'first_name', '-first_name', 'email', '-email', 'date_joined', '-date_joined']
        if ordering in valid_orderings:
            queryset = queryset.order_by(ordering)
        else:
            queryset = queryset.order_by('id')

        return queryset

    def _optimize_queryset(self, queryset):
        """Apply database optimizations to the queryset"""
        return queryset.select_related('user_branch').prefetch_related(
            Prefetch('wallets', queryset=WalletSystem.objects.all()),
            Prefetch('accounts', queryset=AccountSystem.objects.all()),
        ).annotate(
            # Key annotations needed for list view
            get_kyc_status=Case(
                When(kyc_level__gte=1, then=Value('ACTIVE')),
                default=Value('INACTIVE'),
                output_field=CharField()
            ),
            get_account_status=Case(
                When(is_suspended=True, then=Value('SUSPENDED')),
                When(is_fraud=True, then=Value('FLAGGED')),
                default=Value('ACTIVE'),
                output_field=CharField()
            ),
            last_login_timestamp=Greatest(
                F('terminal_last_login'),
                F('mobile_last_login'),
                F('web_last_login')
            ),
        )

    def _get_applied_filters(self, request):
        """Return a summary of applied filters for transparency"""
        filters = {}

        filter_params = [
            'kyc_status', 'account_status', 'account_type',
            'name', 'email', 'phone_number', 'search', 'ordering'
        ]

        for param in filter_params:
            value = request.query_params.get(param)
            if value:
                filters[param] = value

        return filters


class UserProfileDetailAPIView(APIView, OptimizedUserQuerySetMixin):
    """API view to fetch a specific user's complete profile with detailed transactions (admin only)"""

    # permission_classes = [CustomIsAuthenticated]

    @method_decorator(cache_page(60))  # Cache for 60 seconds
    def get(self, request):
        """Get a specific user's detailed profile by ID from query parameter"""
        try:
            # Get user_id from query parameter
            user_id = request.query_params.get('user_id')

            if not user_id:
                return Response({
                    "status": "error",
                    "message": "user_id query parameter is required"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get optimized user queryset
            user_queryset = self.get_optimized_user_queryset(user_id)
            user = user_queryset.first()

            if not user:
                return Response({
                    "status": "error",
                    "message": "User not found"
                }, status=status.HTTP_404_NOT_FOUND)

            # Get all wallets associated with the user
            wallets = WalletSystem.objects.filter(user=user)

            # Calculate financial summary for each wallet
            wallet_data = []
            overall_summary = {
                'total_transactions_count': 0,
                'total_transactions_value': 0.00,
                'pending_transactions_count': 0,
                'pending_transactions_value': 0.00,
                'failed_transactions_count': 0,
                'failed_transactions_value': 0.00,
                'successful_credits_count': 0,
                'successful_credits_value': 0.00,
                'successful_debits_count': 0,
                'successful_debits_value': 0.00,
                'current_wallet_balance': 0.00,
            }

            for wallet in wallets:
                # Get all transactions for this wallet using wallet_id string
                wallet_transactions = Transaction.objects.filter(wallet_id=str(wallet.wallet_id))

                # Calculate financial summary for this wallet
                wallet_summary = calculate_wallet_summary(wallet, wallet_transactions)

                # Find the linked accounts for this wallet using the direct relationship
                linked_accounts = AccountSystem.objects.filter(wallet=wallet).distinct()

                # Add wallet data
                wallet_data.append({
                    'wallet_info': WalletSerializer(wallet).data,
                    'linked_accounts': UserAccountsListSerializer(linked_accounts, many=True).data,
                    'financial_summary': wallet_summary
                })

                # Update overall summary
                for key in overall_summary:
                    if key in wallet_summary:
                        if key != 'last_transaction_date' and key != 'last_transaction_amount' and \
                                key != 'last_transaction_type' and key != 'last_transaction_status' and \
                                key != 'last_transaction_reference':
                            overall_summary[key] += wallet_summary[key]


            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))

            base_transactions = Transaction.objects.filter(user=user).select_related('user').order_by('-date_created')

            all_transactions_queryset = base_transactions
            all_transactions_paginator = Paginator(all_transactions_queryset, page_size)

            try:
                all_transactions_page = all_transactions_paginator.page(page)
            except PageNotAnInteger:
                all_transactions_page = all_transactions_paginator.page(1)
            except EmptyPage:
                all_transactions_page = all_transactions_paginator.page(all_transactions_paginator.num_pages)

            pending_transactions = base_transactions.filter(
                status__in=[Transaction.PENDING, Transaction.IN_PROGRESS]
            )[:50]

            bill_payment_types = [
                Transaction.BILLS_AND_PAYMENT,
                Transaction.BILLS_AND_PAYMENT_REVERSAL,
                Transaction.AIRTIME_PIN,
                Transaction.AIRTIME_PIN_REVERSAL,
            ]
            bill_payments = base_transactions.filter(
                transaction_type__in=bill_payment_types
            )[:100]

            bank_transfer_types = [
                Transaction.SEND_BANK_TRANSFER,
                Transaction.FUND_BANK_TRANSFER,
                Transaction.REVERSAL_BANK_TRANSFER,
                Transaction.REVERSAL_BANK_TRANSFER_IN,
            ]
            pending_bank_transfers = base_transactions.filter(
                transaction_type__in=bank_transfer_types,
                status__in=[Transaction.PENDING, Transaction.IN_PROGRESS]
            )[:50]

            transaction_details = {
                'all_transactions': {
                    'count': all_transactions_paginator.count,
                    'num_pages': all_transactions_paginator.num_pages,
                    'current_page': all_transactions_page.number,
                    'has_next': all_transactions_page.has_next(),
                    'has_previous': all_transactions_page.has_previous(),
                    'results': TransactionSerializer(all_transactions_page.object_list, many=True).data
                },
                'pending_transactions': {
                    'count': pending_transactions.count(),
                    'results': TransactionSerializer(pending_transactions, many=True).data
                },
                'bill_payments': {
                    'count': bill_payments.count(),
                    'results': TransactionSerializer(bill_payments, many=True).data
                },
                'pending_bank_transfers': {
                    'count': pending_bank_transfers.count(),
                    'results': TransactionSerializer(pending_bank_transfers, many=True).data
                }
            }

            transaction_stats = {
                'total_transactions': base_transactions.count(),
                'successful_transactions': base_transactions.filter(status=Transaction.SUCCESSFUL).count(),
                'failed_transactions': base_transactions.filter(status=Transaction.FAILED).count(),
                'pending_transactions': base_transactions.filter(
                    status__in=[Transaction.PENDING, Transaction.IN_PROGRESS]).count(),
                'reversed_transactions': base_transactions.filter(status=Transaction.REVERSED).count(),
                'total_bill_payments': base_transactions.filter(transaction_type__in=bill_payment_types).count(),
                'total_bank_transfers': base_transactions.filter(transaction_type__in=bank_transfer_types).count(),
                'total_amount_transacted': base_transactions.filter(status=Transaction.SUCCESSFUL).aggregate(
                    total=Sum('amount')
                )['total'] or 0.00,
                'last_transaction_date': base_transactions.first().date_created if base_transactions.exists() else None,
            }

            serializer = UserBasicInfoSerializer(user)
            account_serializer = UserAccountSerializer(user)

            response_data = {
                'basic_info': serializer.data,
                'account_info': account_serializer.data,
                'wallets': wallet_data,
                'overall_financial_summary': overall_summary,
                'transaction_details': transaction_details,
                'transaction_statistics': transaction_stats,
            }

            return Response({
                "status": "success",
                "message": "User profile with transaction details retrieved successfully",
                "data": response_data
            }, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            return Response({
                "status": "error",
                "message": "User not found"
            }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            return Response({
                "status": "error",
                "message": f"An error occurred: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


import uuid


class BillPaymentListView(views.APIView):
    """
    API View to fetch either a single bill payment by UUID or all bill payments
    based on query parameters
    """

    # permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        bill_id = request.query_params.get('bill_id')

        if bill_id:
            try:
                # Validate UUID format
                try:
                    uuid_obj = uuid.UUID(bill_id)
                except ValueError:
                    return Response(
                        {
                            "error": f"Invalid UUID format: {bill_id}. Please provide a valid UUID (e.g., 43f8209c-bb70-4d12-9409-c1f9e8e9f111)"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Query the specific bill payment using transaction_id (UUID)
                bill_payment = Transaction.objects.filter(
                    transaction_type='BILLS_AND_PAYMENT',
                    transaction_id=str(uuid_obj)
                ).select_related('user').first()

                # If no bill payment found with the given UUID
                if not bill_payment:
                    return Response(
                        {"error": f"Bill payment with UUID {bill_id} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )

                # Serialize the data
                serializer = BillPaymentSerializer(bill_payment)

                return Response(serializer.data)

            except Exception as e:
                return Response(
                    {"error": f"An error occurred: {str(e)}"},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        # If no bill_id is provided, fetch all bill payments with filtering options
        else:
            # Query parameters for filtering
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')
            status_filter = request.query_params.get('status')
            biller = request.query_params.get('biller')
            page = request.query_params.get('page', 1)
            page_size = request.query_params.get('page_size', 20)

            # Base query with select_related to optimize DB queries
            queryset = Transaction.objects.filter(
                transaction_type='BILLS_AND_PAYMENT'
            ).select_related('user').order_by('-date_created')

            # Apply filters if provided
            if start_date and end_date:
                try:
                    start = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
                    end = datetime.strptime(end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59,
                                                                          tzinfo=timezone.utc)
                    queryset = queryset.filter(date_created__range=(start, end))
                except ValueError:
                    return Response(
                        {"error": "Invalid date format. Use YYYY-MM-DD."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            if status_filter:
                queryset = queryset.filter(status=status_filter.upper())

            if biller:
                queryset = queryset.filter(
                    Q(narration__icontains=biller) |
                    Q(transaction_sub_type__icontains=biller)
                )

            # Use DB indexing for efficient pagination
            paginator = Paginator(queryset, page_size)
            try:
                page_obj = paginator.page(page)
            except Exception:
                page_obj = paginator.page(1)

            # Serialize the data
            serializer = BillPaymentSerializer(page_obj, many=True)

            return Response({
                'count': paginator.count,
                'total_pages': paginator.num_pages,
                'current_page': int(page),
                'results': serializer.data
            })

class TransactionVolumeView(APIView):
    """
    API view for getting transaction volume data month by month,
    categorized by status (SUCCESSFUL, PENDING, FAILED)
    """
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        serializer = TransactionVolumeSerializer(data=request.query_params)
        if serializer.is_valid():
            chart_data = serializer.get_chart_data(serializer.validated_data)
            return Response(chart_data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PendingBankTransferListView(generics.ListAPIView):
    """
    API view to retrieve pending bank transfers with filtering

    Query Parameters:
    - filter: today, this_week, this_month, this_year (default: today)
    - search: Search by liberty_reference, unique_reference, user_email, escrow_id
    - ordering: Order by any field (default: -date_added)
    - user_id: Filter by specific user
    - account_provider: Filter by account provider
    - is_verified: Filter by verification status
    """
    # permission_classes = [CustomIsAuthenticated]
    serializer_class = PendingBankTransferSerializer
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['liberty_reference', 'unique_reference', 'user_email', 'escrow_id']
    ordering_fields = ['date_added', 'last_updated', 'amount', 'num_of_checks']
    ordering = ['-date_added']

    def get_queryset(self):
        """Get filtered queryset with optimizations"""
        filter_period = self.request.query_params.get('filter', 'today')

        # Base queryset with optimizations
        queryset = TransferVerificationObject.objects.filter(
            transaction_ver_status=TransferVerificationObject.PENDING,
            transaction_type__icontains='BANK_TRANSFER'
        ).select_related('transaction_instance')

        # Apply date filtering
        queryset = self._apply_date_filter(queryset, filter_period)

        # Apply optional filters
        queryset = self._apply_optional_filters(queryset)

        return queryset.distinct()

    def _apply_date_filter(self, queryset, filter_period):
        """Apply date filtering based on period"""
        now = timezone.now()

        date_filters = {
            'today': (
                now.replace(hour=0, minute=0, second=0, microsecond=0),
                now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            ),
            'this_week': (
                (now - timedelta(days=now.weekday())).replace(hour=0, minute=0, second=0, microsecond=0),
                (now - timedelta(days=now.weekday())).replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(
                    days=7)
            ),
            'this_month': (
                now.replace(day=1, hour=0, minute=0, second=0, microsecond=0),
                (now.replace(day=1, hour=0, minute=0, second=0, microsecond=0) + timedelta(days=32)).replace(day=1)
            ),
            'this_year': (
                now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0),
                now.replace(year=now.year + 1, month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            )
        }

        start_date, end_date = date_filters.get(filter_period, date_filters['today'])
        return queryset.filter(date_added__gte=start_date, date_added__lt=end_date)

    def _apply_optional_filters(self, queryset):
        """Apply optional query parameter filters"""
        params = self.request.query_params

        if user_id := params.get('user_id'):
            queryset = queryset.filter(user_id=user_id)

        if account_provider := params.get('account_provider'):
            queryset = queryset.filter(account_provider__icontains=account_provider)

        if is_verified := params.get('is_verified'):
            queryset = queryset.filter(is_verified=is_verified.lower() == 'true')

        return queryset

    def list(self, request, *args, **kwargs):
        """Custom list response with summary statistics"""
        try:
            queryset = self.filter_queryset(self.get_queryset())

            # Handle pagination
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                # Get summary from full queryset for pagination
                summary = self._get_summary_stats(queryset)
                response_data = self.get_paginated_response(serializer.data)
                response_data.data['summary'] = summary
                response_data.data['filter_applied'] = request.query_params.get('filter', 'today')
                return response_data

            serializer = self.get_serializer(queryset, many=True)
            summary = self._get_summary_stats(queryset)

            return Response({
                'status': 'success',
                'message': f'Pending bank transfers retrieved successfully',
                'filter_applied': request.query_params.get('filter', 'today'),
                'summary': summary,
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': 'Failed to retrieve pending bank transfers',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_summary_stats(self, queryset):
        """Get summary statistics efficiently using aggregation"""
        stats = queryset.aggregate(
            total_count=Count('id'),
            total_amount=Sum('amount')
        )
        return {
            'total_count': stats['total_count'] or 0,
            'total_amount': float(stats['total_amount'] or 0)
        }


class PendingBankTransferDetailView(generics.GenericAPIView):
    """
    API view to retrieve a single pending bank transfer by ID (passed as query parameter)

    Query Parameters:
    - id: The transaction ID to retrieve (required)

    URL: /pending-bank-transfer/?id=123
    """
    # permission_classes = [CustomIsAuthenticated]
    serializer_class = PendingBankTransferSerializer

    def get_queryset(self):
        """Get single pending bank transfer with optimizations"""
        return TransferVerificationObject.objects.filter(
            transaction_ver_status=TransferVerificationObject.PENDING,
            transaction_type__icontains='BANK_TRANSFER'
        ).select_related('transaction_instance')

    def get(self, request, *args, **kwargs):
        """Retrieve single pending bank transfer by ID parameter"""
        try:
            # Get ID from query parameters
            transaction_id = request.query_params.get('id')

            if not transaction_id:
                return Response({
                    'status': 'error',
                    'message': 'Transaction ID is required as a query parameter'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the specific transaction
            try:
                instance = self.get_queryset().get(id=transaction_id)
            except TransferVerificationObject.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': f'Pending bank transfer with ID {transaction_id} not found'
                }, status=status.HTTP_404_NOT_FOUND)
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid transaction ID format'
                }, status=status.HTTP_400_BAD_REQUEST)

            serializer = self.get_serializer(instance)

            return Response({
                'status': 'success',
                'message': 'Pending bank transfer retrieved successfully',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': 'Failed to retrieve pending bank transfer',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)