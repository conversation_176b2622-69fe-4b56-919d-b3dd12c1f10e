from django.urls import path, include
from customer_care_management_system import views
from rest_framework.routers import DefaultRouter
from customer_care_management_system.views import BillPaymentListView, TransactionVolumeView, \
    PendingBankTransferListView, PendingBankTransferDetailView
from .views import TransactionViewSet
router = DefaultRouter()
router.register(r'', TransactionViewSet)


dahsboard = [
    path("dashboard/", views.DashboardStatsView.as_view()),
    path('product_usage/', views.ProductAnalyticsAPIView.as_view(), name='product_usage'),
    path('transactions/', include(router.urls)),
    path('pending_transactions/', views.PendingTransactionView.as_view(), name='pending-transactions'),
    path('customers_profile/', views.UserProfileListAPIView.as_view(), name='customers_profile'),
    path('customers_profile_detail/', views.UserProfileDetailAPIView.as_view(), name='admin-user-detail'),
    path('bills/', BillPaymentListView.as_view(), name='bill-payments-list'),
    path('transaction_volume/', TransactionVolumeView.as_view(), name='transaction-volume'),
    path('pending_bank_transfers/', PendingBankTransferListView.as_view(), name='pending-bank-transfers-list'),
    path('pending_bank_transfers/', PendingBankTransferDetailView.as_view(), name='pending-bank-transfer-detail'),
]

urlpatterns = [
    *dahsboard,
]