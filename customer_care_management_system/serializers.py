from rest_framework import serializers, status, filters
from django.utils.timezone import now
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from main.models import User, LoginStatus
from accounts.models import Transaction, WalletSystem, AccountSystem, TransferVerificationObject
from rest_framework import serializers
from django.db.models import Sum, Count
from datetime import datetime
from django.db.models.functions import TruncMonth
import calendar
from rest_framework.pagination import PageNumberPagination
import logging
from datetime import datetime, timedelta, date
from main.helper.logging_utils import log_info
logger = logging.getLogger(__name__)


class DashboardMetricsSerializer(serializers.Serializer):
    """
    Serializer for dashboard metrics with comprehensive user and transaction data.
    """
    # User metrics
    total_customers = serializers.IntegerField()
    total_customers_percentage_diff = serializers.FloatField()

    verified_customers = serializers.IntegerField()
    verified_customers_percentage_diff = serializers.FloatField()

    active_customers = serializers.IntegerField()
    active_customers_percentage_diff = serializers.FloatField()

    pending_verification = serializers.IntegerField()
    pending_verification_percentage_diff = serializers.FloatField()

    # KYC level metrics
    kyc_level_one_users = serializers.IntegerField()
    kyc_level_two_users = serializers.IntegerField()
    kyc_level_three_users = serializers.IntegerField()

    # Transaction metrics
    total_transactions = serializers.IntegerField()
    total_transactions_percentage_diff = serializers.FloatField()

    pending_transactions_within_3days = serializers.IntegerField()
    pending_transactions_percentage_diff = serializers.FloatField()

    successful_transactions = serializers.IntegerField()
    successful_transactions_percentage_diff = serializers.FloatField()

    failed_transactions = serializers.IntegerField()
    failed_transactions_percentage_diff = serializers.FloatField()

    # Transaction volume
    transaction_volume = serializers.FloatField()
    transaction_volume_percentage_diff = serializers.FloatField()

    @staticmethod
    def get_date_range(period):
        """
        Return date ranges based on the specified period.

        Args:
            period (str): One of 'today', 'this_week', 'this_month', or 'all_time'

        Returns:
            tuple: (current_period_start, current_period_end,
                   previous_period_start, previous_period_end)
        """
        today = now()

        if period == 'today':
            # Today: from midnight to now
            current_start = today.replace(hour=0, minute=0, second=0, microsecond=0)
            current_end = today
            # Previous day
            previous_end = current_start
            previous_start = previous_end - timedelta(days=1)

        elif period == 'this_week':
            # This week: from Monday to now
            current_start = today - timedelta(days=today.weekday())
            current_start = current_start.replace(hour=0, minute=0, second=0, microsecond=0)
            current_end = today
            # Previous week
            previous_end = current_start
            previous_start = previous_end - timedelta(days=7)

        elif period == 'this_month':
            # This month: from 1st day to now
            current_start = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            current_end = today
            # Previous month
            previous_end = current_start
            previous_start = previous_end - relativedelta(months=1)

        else:  # 'all_time' or any other value
            # All time: compare current year to previous year
            current_start = today.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            current_end = today
            # Previous year
            previous_end = current_start
            previous_start = previous_end - relativedelta(years=1)

        return current_start, current_end, previous_start, previous_end

    @staticmethod
    def get_counts_by_period(queryset, date_field='created_at', period='this_month'):
        """Returns count for current and previous period."""
        current_start, current_end, previous_start, previous_end = DashboardMetricsSerializer.get_date_range(period)

        # Current period count
        current_period_qs = queryset.filter(**{
            f"{date_field}__gte": current_start,
            f"{date_field}__lte": current_end
        })

        # Previous period count
        previous_period_qs = queryset.filter(**{
            f"{date_field}__gte": previous_start,
            f"{date_field}__lte": previous_end
        })

        return current_period_qs.count(), previous_period_qs.count()

    @staticmethod
    def get_sum_by_period(queryset, sum_field, date_field='created_at', period='this_month'):
        """Returns sum for current and previous period for a specific field."""
        current_start, current_end, previous_start, previous_end = DashboardMetricsSerializer.get_date_range(period)

        # Current period sum
        current_period_sum = queryset.filter(**{
            f"{date_field}__gte": current_start,
            f"{date_field}__lte": current_end
        }).aggregate(total=Sum(sum_field))['total'] or 0

        # Previous period sum
        previous_period_sum = queryset.filter(**{
            f"{date_field}__gte": previous_start,
            f"{date_field}__lte": previous_end
        }).aggregate(total=Sum(sum_field))['total'] or 0

        return current_period_sum, previous_period_sum

    @staticmethod
    def calculate_percentage_change(current, previous):
        """Calculates the percentage change between periods."""
        if previous == 0:
            return 100.0 if current > 0 else 0.0
        return ((current - previous) / previous) * 100

    @classmethod
    def get_dashboard_data(cls, period='this_month'):
        """
        Gets comprehensive dashboard data with optimized queries and error handling.

        Args:
            period (str): One of 'today', 'this_week', 'this_month', or 'all_time'

        Returns:
            dict: A dictionary of dashboard metrics.
        """
        try:

            # Get date ranges for the specified period
            current_start, current_end, previous_start, previous_end = cls.get_date_range(period)

            # Calculate three days ago for pending transactions
            three_days_ago = now() - timedelta(days=3)

            # Date filters for transaction queries
            current_tx_filter = {
                'date_created__gte': current_start,
                'date_created__lte': current_end
            }

            # User metrics
            # Total users in current period
            total_cust, prev_total_cust = cls.get_counts_by_period(
                User.objects,
                'created_at',
                period
            )

            # Verified users - users with kyc_level >= 1
            verified_cust, prev_verified_cust = cls.get_counts_by_period(
                User.objects.filter(kyc_level__gte=1),
                'created_at',
                period
            )

            # Active users
            active_cust, prev_active_cust = cls.get_counts_by_period(
                User.objects.filter(terminal_status="ACTIVE"),
                'created_at',
                period
            )

            # Pending verification - users with kyc_level < 1
            pending_ver, prev_pending_ver = cls.get_counts_by_period(
                User.objects.filter(kyc_level__lt=1),
                'created_at',
                period
            )

            # KYC level users (current snapshot, not period-based)
            kyc_level_one = User.objects.filter(kyc_level=1).count()
            kyc_level_two = User.objects.filter(kyc_level=2).count()
            kyc_level_three = User.objects.filter(kyc_level=3).count()

            # Transaction metrics
            # Total transactions
            total_tx, prev_total_tx = cls.get_counts_by_period(
                Transaction.objects,
                'date_created',
                period
            )

            # Transaction volume (sum of amounts)
            tx_volume, prev_tx_volume = cls.get_sum_by_period(
                Transaction.objects.filter(status=Transaction.SUCCESSFUL),
                'amount',
                'date_created',
                period
            )

            # tx_volume, prev_tx_volume = 200000, 40000

            log_info(str(Transaction.objects.filter(status=Transaction.SUCCESSFUL)))

            # Pending transactions within 3 days (this is always a 3-day window regardless of period)
            pending_tx = Transaction.objects.filter(
                status=Transaction.PENDING,
                date_created__gte=three_days_ago
            ).count()

            # For previous period, calculate the equivalent 3-day window at the end of previous period
            prev_three_days_ago = previous_end - timedelta(days=3)
            prev_pending_tx = Transaction.objects.filter(
                status=Transaction.PENDING,
                date_created__gte=prev_three_days_ago,
                date_created__lte=previous_end
            ).count()

            # Successful and failed transactions
            successful_tx, prev_successful_tx = cls.get_counts_by_period(
                Transaction.objects.filter(status=Transaction.SUCCESSFUL),
                'date_created',
                period
            )

            failed_tx, prev_failed_tx = cls.get_counts_by_period(
                Transaction.objects.filter(status=Transaction.FAILED),
                'date_created',
                period
            )

            # Prepare result dictionary
            return {
                # User metrics
                "total_customers": total_cust,
                "total_customers_percentage_diff": cls.calculate_percentage_change(total_cust, prev_total_cust),

                "verified_customers": verified_cust,
                "verified_customers_percentage_diff": cls.calculate_percentage_change(verified_cust,
                                                                                      prev_verified_cust),

                "active_customers": active_cust,
                "active_customers_percentage_diff": cls.calculate_percentage_change(active_cust, prev_active_cust),

                "pending_verification": pending_ver,
                "pending_verification_percentage_diff": cls.calculate_percentage_change(pending_ver, prev_pending_ver),

                # KYC level metrics
                "kyc_level_one_users": kyc_level_one,
                "kyc_level_two_users": kyc_level_two,
                "kyc_level_three_users": kyc_level_three,

                # Transaction metrics
                "total_transactions": total_tx,
                "total_transactions_percentage_diff": cls.calculate_percentage_change(total_tx, prev_total_tx),

                "pending_transactions_within_3days": pending_tx,
                "pending_transactions_percentage_diff": cls.calculate_percentage_change(pending_tx, prev_pending_tx),

                "successful_transactions": successful_tx,
                "successful_transactions_percentage_diff": cls.calculate_percentage_change(successful_tx,
                                                                                           prev_successful_tx),

                "failed_transactions": failed_tx,
                "failed_transactions_percentage_diff": cls.calculate_percentage_change(failed_tx, prev_failed_tx),

                # Transaction volume
                "transaction_volume": tx_volume,
                "transaction_volume_percentage_diff": cls.calculate_percentage_change(tx_volume, prev_tx_volume),
            }

        except Exception as e:
            logger.error(f"Error generating dashboard metrics for period '{period}': {str(e)}")
            # Return empty dictionary with zero values for all fields
            return {field.name: 0 if isinstance(field, serializers.IntegerField) else 0.0
                    for field in cls._declared_fields.values()}


class TransactionTypeAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for individual transaction type analytics
    """
    transaction_type = serializers.CharField()
    count = serializers.IntegerField()
    percentage = serializers.FloatField()



class ProductAnalyticsSerializer(serializers.Serializer):
    """
    Serializer for overall transaction analytics
    Contains the business logic for calculating transaction usage statistics
    """
    filter_type = serializers.CharField()
    date_range = serializers.DictField()
    total_transactions = serializers.IntegerField()
    transaction_types = serializers.ListField(
        child=serializers.DictField(),
        allow_empty=True
    )
    most_used_transaction = serializers.CharField(allow_null=True)
    message = serializers.CharField(required=False)

    class Meta:
        fields = [
            'filter_type', 'date_range', 'total_transactions',
            'transaction_types', 'most_used_transaction', 'message'
        ]
class TransactionCategoryField(serializers.Field):
    """Custom field to determine transaction category with its type"""

    def get_attribute(self, obj):
        # Pass the entire object
        return obj

    def to_representation(self, obj):
        transaction_type = obj.transaction_type
        category_map = {
            'DEBIT': dict(Transaction.DEBIT_TRANS),
            'CREDIT': dict(Transaction.CREDIT_TRANS),
            'OTHER_DEBIT': dict(Transaction.OTHER_DEBIT_TRANS),
            'OTHER_CREDIT': dict(Transaction.OTHER_CREDIT_TRANS)
        }

        # Find which category this transaction type belongs to
        for category, transactions in category_map.items():
            if transaction_type in transactions:
                return {
                    "category": category,
                    "type": transaction_type,
                    "display": f"{category} - {transaction_type}"
                }

        return {
            "category": "UNKNOWN",
            "type": transaction_type,
            "display": f"UNKNOWN - {transaction_type}"
        }


class TransactionSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for Transaction model with all necessary fields
    """
    transaction_id = serializers.UUIDField(read_only=True)
    type_of_transaction = serializers.CharField(source='transaction_type')
    amount_of_transaction = serializers.FloatField(source='amount')
    transaction_status = serializers.CharField(source='status')
    transaction_channel = serializers.CharField(source='transaction_mode')
    user_name = serializers.SerializerMethodField()
    email = serializers.EmailField(source='user.email', read_only=True)
    transaction_date = serializers.SerializerMethodField()
    transaction_reference = serializers.CharField(source='liberty_reference')
    transaction_category = TransactionCategoryField()
    user_wallet_type = serializers.CharField()
    date_created = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S")
    date_modified = serializers.DateTimeField(source='last_updated', format="%Y-%m-%d %H:%M:%S")
    user_id = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = [
            'transaction_id',
            'type_of_transaction',
            'transaction_category',
            'amount_of_transaction',
            'transaction_status',
            'transaction_channel',
            'user_name',
            'email',
            'user_id',
            'transaction_date',
            'date_created',
            'date_modified',
            'transaction_reference',
            'user_wallet_type',
        ]

    def get_user_name(self, obj):
        """Get the full name of the user who performed the transaction"""
        if obj.user_full_name:
            return obj.user_full_name
        elif obj.user and hasattr(obj.user, 'get_full_name'):
            return obj.user.get_full_name()
        return "Unknown User"

    def get_transaction_date(self, obj):
        """Format the transaction date and time"""
        if obj.date_created:
            return obj.date_created.strftime("%Y-%m-%d %H:%M:%S")
        return None

    def get_user_id(self, obj):
        """Get the user ID if available"""
        if obj.user_id:
            return str(obj.user_id)
        return None

class StandardResultsSetPagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100


class PendingTransactionCategoryField(serializers.Field):
    """
    Custom field to determine transaction category based on transaction type
    """

    def to_representation(self, obj):
        transaction_type = obj.transaction_type

        if transaction_type in Transaction.DEBIT_TRANS:
            return "DEBIT"
        elif transaction_type in Transaction.CREDIT_TRANS:
            return "CREDIT"
        elif transaction_type in Transaction.OTHER_DEBIT_TRANS:
            return "OTHER_DEBIT"
        elif transaction_type in Transaction.OTHER_CREDIT_TRANS:
            return "OTHER_CREDIT"
        else:
            return "UNKNOWN"


class PendingTransactionSerializer(serializers.ModelSerializer):
    transaction_id = serializers.UUIDField(read_only=True)
    type_of_transaction = serializers.CharField(source='transaction_type')
    amount_of_transaction = serializers.FloatField(source='amount')
    transaction_status = serializers.CharField(source='status')
    transaction_channel = serializers.CharField(source='transaction_mode')
    user_name = serializers.SerializerMethodField()
    transaction_date = serializers.SerializerMethodField()
    transaction_reference = serializers.CharField(source='liberty_reference')
    transaction_category = serializers.SerializerMethodField()
    user_wallet_type = serializers.CharField()
    date_created = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S")
    date_modified = serializers.SerializerMethodField()
    user_id = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = [
            'transaction_id', 'type_of_transaction', 'amount_of_transaction',
            'transaction_status', 'transaction_channel', 'user_name',
            'transaction_date', 'transaction_reference', 'transaction_category',
            'user_wallet_type', 'date_created', 'date_modified', 'user_id'
        ]

    def get_user_name(self, obj):
        if obj.user_full_name:
            return obj.user_full_name
        elif obj.user and hasattr(obj.user, 'get_full_name'):
            return obj.user.get_full_name()
        return "Unknown User"

    def get_transaction_date(self, obj):
        return obj.date_created.strftime("%Y-%m-%d %H:%M:%S") if obj.date_created else None

    def get_date_modified(self, obj):
        return obj.last_updated.strftime("%Y-%m-%d %H:%M:%S") if obj.last_updated else None

    def get_user_id(self, obj):
        return str(obj.user.id) if obj.user else None

    def get_transaction_category(self, obj):
        transaction_type = obj.transaction_type

        if transaction_type in [choice[0] for choice in Transaction.DEBIT_TRANS]:
            return "DEBIT"
        elif transaction_type in [choice[0] for choice in Transaction.CREDIT_TRANS]:
            return "CREDIT"
        elif transaction_type in [choice[0] for choice in Transaction.OTHER_DEBIT_TRANS]:
            return "OTHER_DEBIT"
        elif transaction_type in [choice[0] for choice in Transaction.OTHER_CREDIT_TRANS]:
            return "OTHER_CREDIT"
        else:
            return "UNKNOWN"


class UserBasicInfoSerializer(serializers.ModelSerializer):
    """Serializer for user's basic information"""
    full_name = serializers.SerializerMethodField()
    kyc_status = serializers.CharField(source='get_kyc_status')
    address = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'full_name', 'email', 'phone_number', 'gender',
            'kyc_status', 'address', 'profile_picture'
        ]

    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}"

    def get_address(self, obj):
        address_parts = filter(None, [obj.street, obj.city, obj.state, obj.lga])
        address_str = ", ".join(address_parts)

        if obj.nearest_landmark:
            return f"{address_str}, Near {obj.nearest_landmark}" if address_str else f"Near {obj.nearest_landmark}"
        return address_str or None


class UserAccountSerializer(serializers.ModelSerializer):
    account_status = serializers.CharField(source='get_account_status')
    account_type = serializers.CharField(source='type_of_user')
    date_created = serializers.DateTimeField(source='date_joined')
    last_login_date = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'customer_id', 'account_status',
            'account_type', 'date_created', 'last_login_date'
        ]

    def get_last_login_date(self, obj):
        """
        Get the user's last login date from the AbstractUser last_login field.
        Falls back to None if no login timestamp exists.
        """
        # Access the standard last_login field that comes from AbstractBaseUser
        return obj.last_login


class UserAccountsListSerializer(serializers.ModelSerializer):
    """Serializer for user's bank accounts"""
    account_type_display = serializers.CharField(source='get_account_type_display', read_only=True)
    true_account_type_display = serializers.CharField(source='get_true_account_type_display', read_only=True)

    class Meta:
        model = AccountSystem
        fields = [
            'account_id', 'account_number', 'account_name',
            'bank_name', 'bank_code', 'account_type',
            'account_type_display', 'true_account_type',
            'true_account_type_display', 'available_balance',
            'is_active', 'date_created', 'last_updated'
        ]


class WalletSerializer(serializers.ModelSerializer):
    """Serializer for user's wallets"""
    wallet_type_display = serializers.CharField(source='get_wallet_type_display', read_only=True)

    class Meta:
        model = WalletSystem
        fields = [
            'wallet_id', 'wallet_type', 'wallet_type_display',
            'available_balance', 'hold_balance', 'is_active',
            'date_created', 'last_updated'
        ]


class FinancialSummarySerializer(serializers.Serializer):
    """Serializer for user's financial summary"""
    total_transactions_count = serializers.IntegerField()
    total_transactions_value = serializers.FloatField()
    last_transaction_date = serializers.DateTimeField(allow_null=True)
    last_transaction_amount = serializers.FloatField(allow_null=True)
    last_transaction_type = serializers.CharField(allow_null=True)
    last_transaction_status = serializers.CharField(allow_null=True)
    last_transaction_reference = serializers.CharField(allow_null=True)
    current_wallet_balance = serializers.FloatField()
    wallets = WalletSerializer(many=True)

    pending_transactions_count = serializers.IntegerField()
    pending_transactions_value = serializers.FloatField()

    failed_transactions_count = serializers.IntegerField()
    failed_transactions_value = serializers.FloatField()

    successful_credits_count = serializers.IntegerField()
    successful_credits_value = serializers.FloatField()

    successful_debits_count = serializers.IntegerField()
    successful_debits_value = serializers.FloatField()

class WalletWithAccountsSerializer(serializers.Serializer):
    """Serializer for wallet with linked accounts and financial summary"""
    wallet_info = WalletSerializer()
    linked_accounts = UserAccountsListSerializer(many=True)
    financial_summary = serializers.SerializerMethodField()

    def get_financial_summary(self, obj):
        # This will be pre-calculated in the view
        return obj.get('financial_summary', {})

class UserProfileSerializer(serializers.Serializer):
    """Main serializer that combines all user profile information"""
    basic_info = UserBasicInfoSerializer(source='*')
    account_info = UserAccountSerializer(source='*')
    wallets = serializers.SerializerMethodField()
    financial_summary = FinancialSummarySerializer()
    # overall_financial_summary = serializers.SerializerMethodField()

    def get_wallets(self, obj):
        # This will be filled in the view
        return obj.get('wallets', [])

    # def get_overall_financial_summary(self, obj):
    #     # This will be filled in the view
    #     return obj.get('overall_financial_summary', {})

class LastLoginSerializer(serializers.ModelSerializer):
    """Serializer for user's last login information"""
    login_status_display = serializers.CharField(source='get_login_status_display', read_only=True)

    class Meta:
        model = LoginStatus
        fields = ['login_status', 'login_status_display', 'last_login_date']


class UserListSerializer(serializers.Serializer):
    """Lightweight serializer for user list view"""
    basic_info = UserBasicInfoSerializer(source='*')
    account_info = UserAccountSerializer(source='*')


class BillPaymentSerializer(serializers.ModelSerializer):
    id = serializers.UUIDField(source='transaction_id', read_only=True)
    type = serializers.CharField(source='transaction_sub_type', read_only=True)
    biller = serializers.SerializerMethodField()
    date = serializers.DateTimeField(source='date_created', read_only=True, format="%Y-%m-%d %H:%M:%S")
    channel = serializers.SerializerMethodField()

    class Meta:
        model = Transaction
        fields = ['id', 'type', 'biller', 'amount', 'status', 'date', 'channel']

    def get_biller(self, obj):
        """Extract biller from narration or transaction_sub_type"""
        if obj.narration:
            # Try to extract biller from narration
            return obj.narration.split(' ')[0] if obj.narration else obj.transaction_sub_type
        return obj.transaction_sub_type

    def get_channel(self, obj):
        """Return the transaction mode (channel)"""
        return obj.transaction_mode if obj.transaction_mode else "MOBILE"


class TransactionVolumeSerializer(serializers.Serializer):
    """
    Serializer for getting transaction volume data month by month,
    categorized by status (SUCCESSFUL, PENDING, FAILED)
    """
    start_date = serializers.DateField(required=False)
    end_date = serializers.DateField(required=False)

    def get_chart_data(self, validated_data):
        """Process transaction data and return chart-ready format"""
        # Get date range from request or default to current year
        start_date = validated_data.get('start_date')
        end_date = validated_data.get('end_date')

        current_year = datetime.now().year

        if not start_date:
            start_date = datetime(current_year, 1, 1)

        if not end_date:
            end_date = datetime(current_year, 12, 31)

        # Query for transactions grouped by month and status
        transactions = (
            Transaction.objects
            .filter(date_created__range=(start_date, end_date))
            .annotate(month=TruncMonth('date_created'))
            .values('month', 'status')
            .annotate(count=Count('id'))
            .order_by('month', 'status')
        )

        # Initialize results with all months and statuses
        results = self._initialize_results(start_date, end_date)

        # Populate with actual data
        for transaction in transactions:
            month_name = calendar.month_abbr[transaction['month'].month]
            status = transaction['status']
            count = transaction['count']

            if status in results and month_name in results[status]:
                results[status][month_name] = count

        # Format for the chart display
        formatted_data = {
            'labels': list(results[Transaction.SUCCESSFUL].keys()),
            'datasets': [
                {
                    'label': 'Successful',
                    'data': list(results[Transaction.SUCCESSFUL].values())
                },
                {
                    'label': 'Pending',
                    'data': list(results[Transaction.PENDING].values())
                },
                {
                    'label': 'Failed',
                    'data': list(results[Transaction.FAILED].values())
                }
            ]
        }

        return formatted_data

    def _initialize_results(self, start_date, end_date):
        """Initialize results dictionary with all months in range"""
        results = {
            Transaction.SUCCESSFUL: {},
            Transaction.PENDING: {},
            Transaction.FAILED: {}
        }

        # Generate all months between start and end date
        current_date = start_date
        while current_date <= end_date:
            month_name = calendar.month_abbr[current_date.month]

            results[Transaction.SUCCESSFUL][month_name] = 0
            results[Transaction.PENDING][month_name] = 0
            results[Transaction.FAILED][month_name] = 0

            # Move to the next month
            if current_date.month == 12:
                current_date = datetime(current_date.year + 1, 1, 1)
            else:
                current_date = datetime(current_date.year, current_date.month + 1, 1)

        return results


class PendingBankTransferSerializer(serializers.ModelSerializer):
    """Serializer for pending bank transfers"""

    class Meta:
        model = TransferVerificationObject
        fields = [
            'id', 'transaction_instance', 'user_id', 'user_email',
            'account_provider', 'transaction_type', 'transaction_sub_type',
            'escrow_id', 'amount', 'liberty_reference', 'unique_reference',
            'transaction_ver_status', 'source_nuban', 'beneficiary_nuban',
            'bank_code', 'is_verified', 'num_of_checks', 'date_added', 'last_updated'
        ]
        read_only_fields = ['date_added', 'last_updated']