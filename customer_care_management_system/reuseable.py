from main.models import  User
from django.db.models import  Q

# def calculate_wallet_summary(wallet, wallet_transactions):
#     """
#     Calculate financial summary for a wallet based on its transactions
#
#     Args:
#         wallet: WalletSystem instance
#         wallet_transactions: QuerySet of Transaction objects
#
#     Returns:
#         dict: Financial summary statistics
#     """
#     wallet_summary = {
#         'total_transactions_count': wallet_transactions.count(),
#         'total_transactions_value': sum(t.amount or 0 for t in wallet_transactions),
#         'pending_transactions_count': wallet_transactions.filter(status="PENDING").count(),
#         'pending_transactions_value': sum(t.amount or 0 for t in wallet_transactions.filter(status="PENDING")),
#         'failed_transactions_count': wallet_transactions.filter(status="FAILED").count(),
#         'failed_transactions_value': sum(t.amount or 0 for t in wallet_transactions.filter(status="FAILED")),
#         'successful_credits_count': wallet_transactions.filter(
#             status="SUCCESSFUL",
#             transaction_type="CREDIT"
#         ).count(),
#         'successful_credits_value': sum(
#             t.amount or 0 for t in wallet_transactions.filter(
#                 status="SUCCESSFUL",
#                 transaction_type="CREDIT"
#             )
#         ),
#         'successful_debits_count': wallet_transactions.filter(
#             status="SUCCESSFUL",
#             transaction_type="DEBIT"
#         ).count(),
#         'successful_debits_value': sum(
#             t.amount or 0 for t in wallet_transactions.filter(
#                 status="SUCCESSFUL",
#                 transaction_type="DEBIT"
#             )
#         ),
#         'current_wallet_balance': wallet.available_balance,
#     }
#
#     # Get the last transaction if any exists
#     last_transaction = wallet_transactions.order_by('-date_created').first()
#     if last_transaction:
#         wallet_summary.update({
#             'last_transaction_date': last_transaction.date_created,
#             'last_transaction_amount': last_transaction.amount,
#             'last_transaction_type': last_transaction.transaction_type,
#             'last_transaction_status': last_transaction.status,
#             'last_transaction_reference': last_transaction.liberty_reference,
#         })
#
#     return wallet_summary

class OptimizedUserQuerySetMixin:
    """
    Mixin to provide optimized user queryset with all necessary related fields
    to avoid N+1 query issues.
    """

    def get_optimized_user_queryset(self, user_id):
        """
        Returns an optimized queryset for fetching user details.

        Args:
            user_id: User ID to fetch

        Returns:
            QuerySet: Optimized user queryset with related fields
        """
        # Create a queryset with all the necessary related fields
        user_queryset = User.objects.filter(
            Q(id=user_id) | Q(email=user_id) | Q(customer_id=user_id) | Q(phone_number=user_id)
        ).select_related(
            'profile'  # If you have a profile model related to User
        ).prefetch_related(
            'wallets',  # Prefetch wallet relationships
            'accounts'  # Prefetch account relationships
        )

        return user_queryset


def calculate_wallet_summary(wallet, wallet_transactions):
    """
    Calculate financial summary for a wallet based on its transactions

    Args:
        wallet: WalletSystem instance
        wallet_transactions: QuerySet of Transaction objects

    Returns:
        dict: Financial summary statistics
    """
    wallet_summary = {
        'total_transactions_count': wallet_transactions.count(),
        'total_transactions_value': sum(t.amount or 0 for t in wallet_transactions),
        'pending_transactions_count': wallet_transactions.filter(status="PENDING").count(),
        'pending_transactions_value': sum(t.amount or 0 for t in wallet_transactions.filter(status="PENDING")),
        'failed_transactions_count': wallet_transactions.filter(status="FAILED").count(),
        'failed_transactions_value': sum(t.amount or 0 for t in wallet_transactions.filter(status="FAILED")),
        'successful_credits_count': wallet_transactions.filter(
            status="SUCCESSFUL",
            transaction_type="CREDIT"
        ).count(),
        'successful_credits_value': sum(
            t.amount or 0 for t in wallet_transactions.filter(
                status="SUCCESSFUL",
                transaction_type="CREDIT"
            )
        ),
        'successful_debits_count': wallet_transactions.filter(
            status="SUCCESSFUL",
            transaction_type="DEBIT"
        ).count(),
        'successful_debits_value': sum(
            t.amount or 0 for t in wallet_transactions.filter(
                status="SUCCESSFUL",
                transaction_type="DEBIT"
            )
        ),
        'current_wallet_balance': wallet.available_balance,
    }

    # Get the last transaction if any exists
    last_transaction = wallet_transactions.order_by('-date_created').first()
    if last_transaction:
        wallet_summary.update({
            'last_transaction_date': last_transaction.date_created,
            'last_transaction_amount': last_transaction.amount,
            'last_transaction_type': last_transaction.transaction_type,
            'last_transaction_status': last_transaction.status,
            'last_transaction_reference': last_transaction.liberty_reference,
        })

    return wallet_summary