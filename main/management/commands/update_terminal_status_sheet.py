import json
import logging
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from google.oauth2 import service_account
import gspread
from horizon_pay.models import TerminalSerialTable, TerminalRetrieval

# Set up logging
logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Update terminal status in Google Sheets based on database records'
    
    # Google Sheets configuration
    SPREADSHEET_URL = "https://docs.google.com/spreadsheets/d/164RnnjcIKuZ9qz776TRpPOl4Ktvd206iq25r8VwIfSg/edit?gid=*********#gid=*********"
    WORKSHEET_NAME = "Real Terminal Records"
    TERMINAL_SN_COLUMN = "TERMINAL SN"
    TERMINAL_STATUS_COLUMN = "TERMINAL STATUS"
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without making actual changes to the sheet',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )
        parser.add_argument(
            '--limit',
            type=int,
            help='Limit the number of rows to process (for testing)',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.verbose = options['verbose']
        self.limit = options['limit']
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('Running in DRY-RUN mode - no changes will be made'))
        
        try:
            # Initialize Google Sheets client
            client = self._get_google_sheets_client()
            
            # Open the spreadsheet and worksheet
            spreadsheet = client.open_by_url(self.SPREADSHEET_URL)
            worksheet = spreadsheet.worksheet(self.WORKSHEET_NAME)
            
            # Process the worksheet
            self._process_worksheet(worksheet)
            
            self.stdout.write(self.style.SUCCESS('Terminal status update completed successfully'))
            
        except Exception as e:
            logger.error(f"Error updating terminal status: {str(e)}")
            raise CommandError(f'Error updating terminal status: {str(e)}')

    def _get_google_sheets_client(self):
        """Initialize and return Google Sheets client with authentication"""
        try:
            google_json = settings.GOOGLE_SHEET_CREDENTIALS
            service_account_info = json.loads(google_json)
            credentials = service_account.Credentials.from_service_account_info(service_account_info)
            scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
            creds_with_scope = credentials.with_scopes(scope)
            client = gspread.authorize(creds_with_scope)
            
            if self.verbose:
                self.stdout.write('Successfully authenticated with Google Sheets API')
            
            return client
            
        except Exception as e:
            raise CommandError(f'Failed to authenticate with Google Sheets API: {str(e)}')

    def _process_worksheet(self, worksheet):
        """Process the worksheet and update terminal statuses"""
        try:
            # Get all records from the worksheet
            records = worksheet.get_all_records()
            
            if not records:
                self.stdout.write(self.style.WARNING('No records found in the worksheet'))
                return
            
            # Find column indices
            headers = worksheet.row_values(1)
            try:
                terminal_sn_col_index = headers.index(self.TERMINAL_SN_COLUMN) + 1  # gspread uses 1-based indexing
                terminal_status_col_index = headers.index(self.TERMINAL_STATUS_COLUMN) + 1
            except ValueError as e:
                raise CommandError(f'Required column not found in worksheet: {str(e)}')
            
            if self.verbose:
                self.stdout.write(f'Found {len(records)} records to process')
                self.stdout.write(f'Terminal SN column: {terminal_sn_col_index}, Status column: {terminal_status_col_index}')
            
            # Process records
            processed_count = 0
            updated_count = 0
            
            for row_index, record in enumerate(records, start=2):  # Start from row 2 (after headers)
                if self.limit and processed_count >= self.limit:
                    break
                
                terminal_sn = record.get(self.TERMINAL_SN_COLUMN, '').strip()
                current_status = record.get(self.TERMINAL_STATUS_COLUMN, '').strip()
                
                if not terminal_sn:
                    if self.verbose:
                        self.stdout.write(f'Row {row_index}: Skipping empty terminal SN')
                    continue
                
                # Determine the new status
                new_status = self._determine_terminal_status(terminal_sn)
                
                if self.verbose:
                    self.stdout.write(f'Row {row_index}: Terminal {terminal_sn} - Current: "{current_status}" -> New: "{new_status}"')
                
                # Update the status if it has changed
                if new_status != current_status:
                    if not self.dry_run:
                        try:
                            worksheet.update_cell(row_index, terminal_status_col_index, new_status)
                            updated_count += 1
                            if self.verbose:
                                self.stdout.write(self.style.SUCCESS(f'Updated row {row_index}: {terminal_sn} -> {new_status}'))
                        except Exception as e:
                            self.stdout.write(self.style.ERROR(f'Failed to update row {row_index}: {str(e)}'))
                    else:
                        updated_count += 1
                        self.stdout.write(f'[DRY-RUN] Would update row {row_index}: {terminal_sn} -> {new_status}')
                
                processed_count += 1
            
            self.stdout.write(self.style.SUCCESS(f'Processed {processed_count} records, updated {updated_count} statuses'))
            
        except Exception as e:
            raise CommandError(f'Error processing worksheet: {str(e)}')

    def _determine_terminal_status(self, terminal_sn):
        """
        Determine the terminal status based on database records
        
        Status Logic:
        - "Active": TerminalSerialTable exists and user field is not None
        - "Inactive": TerminalSerialTable exists, user field is None, and no TerminalRetrieval record
        - "Retrieved": TerminalRetrieval record exists with status="retrieved"
        - "Recovered": TerminalRetrieval record exists with status="returned"
        - "Not Found": Terminal serial not found in database
        """
        try:
            # Try to find the terminal in TerminalSerialTable
            try:
                terminal_instance = TerminalSerialTable.objects.get(terminal_serial=terminal_sn)
            except TerminalSerialTable.DoesNotExist:
                if self.verbose:
                    self.stdout.write(f'Terminal {terminal_sn} not found in TerminalSerialTable')
                return "-"
            
            # Check for TerminalRetrieval records
            try:
                retrieval_record = TerminalRetrieval.objects.get(terminal=terminal_instance)
                if retrieval_record.status == "retrieved":
                    return "Retrieved"
                elif retrieval_record.status == "returned":
                    return "Recovered"
                # If status is "reassigned", fall through to check user assignment
            except TerminalRetrieval.DoesNotExist:
                # No retrieval record exists
                pass
            
            # Check user assignment
            if terminal_instance.user is not None:
                return "Active"
            else:
                return "Inactive"
                
        except Exception as e:
            logger.error(f"Error determining status for terminal {terminal_sn}: {str(e)}")
            return "Error"
