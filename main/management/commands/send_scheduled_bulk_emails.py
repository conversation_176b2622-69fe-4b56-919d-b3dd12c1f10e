from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from datetime import datetime

from main.models import BulkEmailCampaign
from main.tasks import send_bulk_email_task
from main.helper.logging_utils import log_info, log_warning, log_error


class Command(BaseCommand):
    help = """
    Send scheduled bulk email campaigns.
    
    This command processes BulkEmailCampaign records that are:
    - In 'DRAFT' status
    - Have a scheduled_at datetime that is due (less than or equal to current time)
    
    The command will:
    1. Find all eligible campaigns
    2. Trigger the asynchronous send_bulk_email_task for each campaign
    3. Update campaign status to prevent duplicate processing
    
    Usage:
        python manage.py send_scheduled_bulk_emails
        python manage.py send_scheduled_bulk_emails --dry-run
        python manage.py send_scheduled_bulk_emails --campaign-id 123
    """

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without actually sending emails or updating campaign status',
        )
        parser.add_argument(
            '--campaign-id',
            type=int,
            help='Process a specific campaign by ID (ignores scheduling)',
        )
        parser.add_argument(
            '--max-campaigns',
            type=int,
            default=50,
            help='Maximum number of campaigns to process in one run (default: 50)',
        )

    def handle(self, *args, **options):
        """Main command handler"""
        dry_run = options['dry_run']
        campaign_id = options.get('campaign_id')
        max_campaigns = options['max_campaigns']

        self.stdout.write(
            self.style.SUCCESS(f"Starting scheduled bulk email processing...")
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No emails will be sent or status updated")
            )

        log_info("Starting scheduled bulk email command", "SCHEDULED_BULK_EMAIL_START")

        try:
            if campaign_id:
                # Process specific campaign
                campaigns = self.get_specific_campaign(campaign_id)
            else:
                # Process scheduled campaigns
                campaigns = self.get_scheduled_campaigns(max_campaigns)

            if not campaigns:
                self.stdout.write(
                    self.style.WARNING("No eligible campaigns found")
                )
                log_info("No eligible campaigns found", "SCHEDULED_BULK_EMAIL_NONE")
                return

            self.stdout.write(f"Found {len(campaigns)} eligible campaign(s)")

            processed_count = 0
            success_count = 0
            error_count = 0

            for campaign in campaigns:
                try:
                    result = self.process_campaign(campaign, dry_run)
                    processed_count += 1
                    
                    if result['success']:
                        success_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"✓ Campaign '{campaign.subject}' (ID: {campaign.id}): "
                                f"{result['message']}"
                            )
                        )
                    else:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(
                                f"✗ Campaign '{campaign.subject}' (ID: {campaign.id}): "
                                f"{result['message']}"
                            )
                        )
                        
                except Exception as e:
                    error_count += 1
                    error_msg = f"Error processing campaign '{campaign.subject}' (ID: {campaign.id}): {str(e)}"
                    self.stdout.write(self.style.ERROR(error_msg))
                    log_error(error_msg, "SCHEDULED_BULK_EMAIL_ERROR", {
                        'campaign_id': campaign.id,
                        'campaign_subject': campaign.subject
                    })

            # Summary
            self.stdout.write(
                self.style.SUCCESS(
                    f"\nProcessing complete:\n"
                    f"- Processed: {processed_count}\n"
                    f"- Successful: {success_count}\n"
                    f"- Errors: {error_count}"
                )
            )

            log_info(
                f"Scheduled bulk email processing completed. "
                f"Processed: {processed_count}, Success: {success_count}, Errors: {error_count}",
                "SCHEDULED_BULK_EMAIL_COMPLETE"
            )

        except Exception as e:
            error_msg = f"Fatal error in scheduled bulk email processing: {str(e)}"
            self.stdout.write(self.style.ERROR(error_msg))
            log_error(error_msg, "SCHEDULED_BULK_EMAIL_FATAL")
            raise

    def get_scheduled_campaigns(self, max_campaigns):
        """
        Get campaigns that are scheduled to be sent now.
        
        Args:
            max_campaigns: Maximum number of campaigns to return
            
        Returns:
            QuerySet of BulkEmailCampaign objects
        """
        now = timezone.now()
        
        campaigns = BulkEmailCampaign.objects.filter(
            status=BulkEmailCampaign.DRAFT,
            scheduled_at__isnull=False,
            scheduled_at__lte=now
        ).order_by('scheduled_at')[:max_campaigns]

        log_info(
            f"Found {campaigns.count()} scheduled campaigns due for sending",
            "SCHEDULED_BULK_EMAIL_QUERY",
            {'current_time': now.isoformat(), 'max_campaigns': max_campaigns}
        )

        return campaigns

    def get_specific_campaign(self, campaign_id):
        """
        Get a specific campaign by ID.
        
        Args:
            campaign_id: ID of the campaign to process
            
        Returns:
            List containing the campaign or empty list if not found
        """
        try:
            campaign = BulkEmailCampaign.objects.get(id=campaign_id)
            
            if campaign.status != BulkEmailCampaign.DRAFT:
                log_warning(
                    f"Campaign {campaign_id} is not in DRAFT status (current: {campaign.status})",
                    "SCHEDULED_BULK_EMAIL_INVALID_STATUS"
                )
                return []
                
            return [campaign]
            
        except BulkEmailCampaign.DoesNotExist:
            log_error(
                f"Campaign with ID {campaign_id} not found",
                "SCHEDULED_BULK_EMAIL_NOT_FOUND"
            )
            return []

    def process_campaign(self, campaign, dry_run=False):
        """
        Process a single campaign.
        
        Args:
            campaign: BulkEmailCampaign instance
            dry_run: If True, don't actually send or update status
            
        Returns:
            dict: Processing result with 'success' and 'message' keys
        """
        try:
            # Validate campaign can be sent
            if not campaign.can_be_sent:
                return {
                    'success': False,
                    'message': f"Campaign cannot be sent. Status: {campaign.get_status_display()}"
                }

            # Update recipient count to ensure it's current
            recipient_count = 0
            if not dry_run:
                campaign.update_recipient_count()
            else:
                # For dry run, calculate recipients without updating
                recipients = campaign.get_recipients()
                recipient_count = recipients.count() if recipients else 0
                
            recipient_count = campaign.total_recipients if not dry_run else recipient_count

            if recipient_count == 0:
                return {
                    'success': False,
                    'message': "Campaign has no recipients. Please check filters."
                }

            if not dry_run:
                # Update campaign status to SENDING to prevent duplicate processing
                with transaction.atomic():
                    # Re-fetch campaign with lock to prevent race conditions
                    locked_campaign = BulkEmailCampaign.objects.select_for_update().get(
                        id=campaign.id
                    )
                    
                    # Double-check status hasn't changed
                    if locked_campaign.status != BulkEmailCampaign.DRAFT:
                        return {
                            'success': False,
                            'message': f"Campaign status changed to {locked_campaign.get_status_display()}"
                        }
                    
                    # Update status and started_at timestamp
                    locked_campaign.status = BulkEmailCampaign.SENDING
                    locked_campaign.started_at = timezone.now()
                    locked_campaign.save(update_fields=['status', 'started_at'])

                # Trigger the asynchronous email sending task
                send_bulk_email_task.apply_async(
                    queue="send_campaign_email_task",
                    kwargs={
                        "campaign_id": str(campaign.id)
                    }
                )

                log_info(
                    f"Triggered bulk email task for campaign '{campaign.subject}'",
                    "SCHEDULED_BULK_EMAIL_TRIGGERED",
                    {
                        'campaign_id': campaign.id,
                        'campaign_subject': campaign.subject,
                        'recipient_count': recipient_count,
                        'scheduled_at': campaign.scheduled_at.isoformat() if campaign.scheduled_at else None
                    }
                )

                return {
                    'success': True,
                    'message': f"Email task triggered for {recipient_count} recipients"
                }
            else:
                return {
                    'success': True,
                    'message': f"Would send to {recipient_count} recipients (DRY RUN)"
                }

        except Exception as e:
            log_error(
                f"Error processing campaign {campaign.id}: {str(e)}",
                "SCHEDULED_BULK_EMAIL_PROCESS_ERROR",
                {'campaign_id': campaign.id}
            )
            return {
                'success': False,
                'message': f"Processing error: {str(e)}"
            }

