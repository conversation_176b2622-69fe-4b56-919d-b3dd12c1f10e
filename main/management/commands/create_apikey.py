from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import json

from main.models import APIKey

User = get_user_model()


class Command(BaseCommand):
    help = 'Create a new API key for a user'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=str,
            required=True,
            help='Email or username of the user to create API key for'
        )
        parser.add_argument(
            '--name',
            type=str,
            required=True,
            help='Human-readable name for the API key'
        )
        parser.add_argument(
            '--scopes',
            type=str,
            help='Comma-separated list of scopes (e.g., "transactions:read,users:write")'
        )
        parser.add_argument(
            '--expires-in-days',
            type=int,
            help='Number of days until the API key expires (default: no expiration)'
        )
        parser.add_argument(
            '--rate-limit',
            type=int,
            default=1000,
            help='Maximum requests per rate limit window (default: 1000)'
        )
        parser.add_argument(
            '--rate-window',
            type=int,
            default=3600,
            help='Rate limit window in seconds (default: 3600 = 1 hour)'
        )
        parser.add_argument(
            '--output-format',
            choices=['text', 'json'],
            default='text',
            help='Output format for the generated key (default: text)'
        )

    def handle(self, *args, **options):
        try:
            # Get the user
            user_identifier = options['user']
            try:
                if '@' in user_identifier:
                    user = User.objects.get(email=user_identifier)
                else:
                    user = User.objects.get(username=user_identifier)
            except User.DoesNotExist:
                raise CommandError(f'User "{user_identifier}" does not exist')

            # Parse scopes
            scopes = []
            if options['scopes']:
                scopes = [scope.strip() for scope in options['scopes'].split(',')]

            # Calculate expiration date
            expires_at = None
            if options['expires_in_days']:
                expires_at = timezone.now() + timedelta(days=options['expires_in_days'])

            # Create the API key
            api_key, full_key = APIKey.generate_key(
                user=user,
                name=options['name'],
                scopes=scopes,
                expires_at=expires_at,
                rate_limit_requests=options['rate_limit'],
                rate_limit_window=options['rate_window']
            )

            # Output the result
            if options['output_format'] == 'json':
                result = {
                    'success': True,
                    'api_key': {
                        'id': api_key.key_id,
                        'name': api_key.name,
                        'user': user.email,
                        'scopes': api_key.scopes,
                        'rate_limit_requests': api_key.rate_limit_requests,
                        'rate_limit_window': api_key.rate_limit_window,
                        'expires_at': api_key.expires_at.isoformat() if api_key.expires_at else None,
                        'created_at': api_key.date_created.isoformat()
                    },
                    'full_key': full_key,
                    'warning': 'Store this key securely. It will not be shown again.'
                }
                self.stdout.write(json.dumps(result, indent=2))
            else:
                self.stdout.write(
                    self.style.SUCCESS('✓ API Key created successfully!')
                )
                self.stdout.write('')
                self.stdout.write(f'Key ID: {api_key.key_id}')
                self.stdout.write(f'Name: {api_key.name}')
                self.stdout.write(f'User: {user.email}')
                self.stdout.write(f'Scopes: {", ".join(api_key.scopes) if api_key.scopes else "No restrictions"}')
                self.stdout.write(f'Rate Limit: {api_key.rate_limit_requests} requests per {api_key.rate_limit_window} seconds')
                self.stdout.write(f'Expires: {api_key.expires_at.strftime("%Y-%m-%d %H:%M:%S UTC") if api_key.expires_at else "Never"}')
                self.stdout.write(f'Created: {api_key.date_created.strftime("%Y-%m-%d %H:%M:%S UTC")}')
                self.stdout.write('')
                self.stdout.write(
                    self.style.WARNING('⚠️  IMPORTANT: Store this API key securely. It will not be shown again.')
                )
                self.stdout.write('')
                self.stdout.write(
                    self.style.HTTP_INFO(f'API Key: {full_key}')
                )
                self.stdout.write('')
                self.stdout.write('Usage example:')
                self.stdout.write(f'curl -H "Authorization: ApiKey {full_key}" https://your-api.com/endpoint')

        except Exception as e:
            if options['output_format'] == 'json':
                error_result = {
                    'success': False,
                    'error': str(e)
                }
                self.stdout.write(json.dumps(error_result, indent=2))
            else:
                raise CommandError(f'Failed to create API key: {str(e)}')
