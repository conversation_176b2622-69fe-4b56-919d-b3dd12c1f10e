"""
Django management command to systematically replace print() statements 
with proper logging calls throughout the codebase.
"""

import os
import re
import ast
from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = 'Replace print() statements with proper logging calls throughout the codebase'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )
        parser.add_argument(
            '--path',
            type=str,
            help='Specific path to process (default: entire project)',
        )
        parser.add_argument(
            '--exclude',
            type=str,
            nargs='*',
            default=['migrations', '__pycache__', '.git', 'venv', 'env'],
            help='Directories to exclude from processing',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.exclude_dirs = options['exclude']
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('Running in DRY-RUN mode - no changes will be made'))
        
        # Determine the path to process
        if options['path']:
            base_path = options['path']
        else:
            base_path = settings.BASE_DIR
        
        self.stdout.write(f'Processing path: {base_path}')
        
        # Find all Python files
        python_files = self._find_python_files(base_path)
        self.stdout.write(f'Found {len(python_files)} Python files to process')
        
        # Process each file
        total_replacements = 0
        files_modified = 0
        
        for file_path in python_files:
            replacements = self._process_file(file_path)
            if replacements > 0:
                files_modified += 1
                total_replacements += replacements
                self.stdout.write(f'  {file_path}: {replacements} replacements')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Completed: {total_replacements} replacements in {files_modified} files'
            )
        )

    def _find_python_files(self, base_path):
        """Find all Python files in the given path, excluding specified directories."""
        python_files = []
        
        for root, dirs, files in os.walk(base_path):
            # Remove excluded directories from the search
            dirs[:] = [d for d in dirs if d not in self.exclude_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))
        
        return python_files

    def _process_file(self, file_path):
        """Process a single Python file to replace print statements."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            replacements_made = 0
            
            # Check if the file already imports logging_utils
            has_logging_import = 'from main.helper.logging_utils import' in content
            
            # Find print statements and replace them
            content, count = self._replace_print_statements(content, file_path)
            replacements_made += count
            
            # Add logging import if we made replacements and it's not already there
            if replacements_made > 0 and not has_logging_import:
                content = self._add_logging_import(content)
            
            # Write the modified content back to the file
            if not self.dry_run and content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            return replacements_made
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error processing {file_path}: {str(e)}')
            )
            return 0

    def _replace_print_statements(self, content, file_path):
        """Replace print statements with appropriate logging calls."""
        lines = content.split('\n')
        modified_lines = []
        replacements = 0
        
        for line_num, line in enumerate(lines, 1):
            original_line = line
            
            # Skip lines that are comments or in docstrings
            stripped = line.strip()
            if stripped.startswith('#') or stripped.startswith('"""') or stripped.startswith("'''"):
                modified_lines.append(line)
                continue
            
            # Find print statements using regex
            print_pattern = r'(\s*)print\s*\(\s*([^)]+)\s*\)'
            match = re.search(print_pattern, line)
            
            if match:
                indent = match.group(1)
                print_content = match.group(2)
                
                # Determine appropriate log level based on content
                log_level = self._determine_log_level(print_content, file_path)
                
                # Create the replacement
                replacement = self._create_log_replacement(print_content, log_level, indent)
                
                # Replace the print statement
                new_line = re.sub(print_pattern, replacement, line)
                modified_lines.append(new_line)
                replacements += 1
                
                if self.dry_run:
                    self.stdout.write(f'    Line {line_num}: {original_line.strip()} -> {new_line.strip()}')
            else:
                modified_lines.append(line)
        
        return '\n'.join(modified_lines), replacements

    def _determine_log_level(self, print_content, file_path):
        """Determine the appropriate log level based on the print content."""
        content_lower = print_content.lower()
        
        # Error indicators
        if any(word in content_lower for word in ['error', 'exception', 'failed', 'fail']):
            return 'error'
        
        # Warning indicators
        if any(word in content_lower for word in ['warning', 'warn', 'deprecated']):
            return 'warning'
        
        # Debug indicators
        if any(word in content_lower for word in ['debug', 'trace', '$$$$', '----', '###']):
            return 'debug'
        
        # Check file context
        if 'test' in file_path.lower():
            return 'debug'
        
        # Default to info
        return 'info'

    def _create_log_replacement(self, print_content, log_level, indent):
        """Create the logging replacement for a print statement."""
        # Clean up the print content
        content = print_content.strip()
        
        # Handle f-strings and complex expressions
        if content.startswith('f"') or content.startswith("f'"):
            # F-string - keep as is
            message = content
        elif '"' in content or "'" in content:
            # String literal - keep as is
            message = content
        else:
            # Variable or expression - convert to string
            message = f'str({content})'
        
        # Create the log call
        return f'{indent}log_{log_level}({message}, "PRINT_REPLACEMENT")'

    def _add_logging_import(self, content):
        """Add the logging import to the file."""
        lines = content.split('\n')
        
        # Find the best place to add the import (after existing imports)
        import_line_index = 0
        for i, line in enumerate(lines):
            if line.strip().startswith('from ') or line.strip().startswith('import '):
                import_line_index = i + 1
            elif line.strip() and not line.strip().startswith('#'):
                break
        
        # Add the import
        import_statement = 'from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical'
        lines.insert(import_line_index, import_statement)
        
        return '\n'.join(lines)
