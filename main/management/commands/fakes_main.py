from django.core.management.base import BaseCommand
from django.db.models import Q
from django.conf import settings
from pytz import utc
from datetime import datetime
from accounts.models import WalletSystem, AccountSystem, DebitCreditRecordOnAccount, Transaction, CyberPayClass
from main.models import User, create_unique_id_for_customer, ConstantTable, UserFormatted, UserOtherAccount
from main.helper.helper_function import freshworks_register_contact, send_sms_to_user_on_successful_account_creation, add_agents_whatsapp_group_func
from main.helper.id_verification import IdentityPass
from django.contrib.auth.hashers import make_password, check_password
from horizon_pay.helpers.helper_function import encrypt_trans_pin, decrypt_trans_pin

# from main.models import DisbursementTable, LotteryBatch, LotteryModel, LotteryWinnersTable, PaymentTransaction, PayoutTransactionTable, UserProfile, WovenAccountDetail
# from main.helpers.whisper_sms_managers import notfy_non_winners_on_raffle_end, payment_receipt_sms, demo_sms_compensate

import uuid
import jwt
import json
import requests
import random
from main.helper.logging_utils import log_info



class Command(BaseCommand):

    def handle(self, *args, **kwargs):

        hashed_pin = make_password("0000")
        log_info(str(hashed_pin))

        # for trans in Transaction.objects.filter(id__in=[]):
        #     trans.status="PENDING"
        #     trans.save()


        # for user in User.objects.filter(trans_band__gt=1):
        #     user.trans_band = 1
        #     user.save()




        # today = datetime.now().date()


        # for user in User.objects.filter(type_of_user="LOTTO_AGENT", terminal_suspended=True, updated_at__date=today):
        #     user.terminal_suspended = False
        #     user.save()




        # print(UserOtherAccount.create_other_account(user=User.objects.get(id=3)))
      
        # faulty_users = []

        # for user in User.objects.filter(kyc_level__lte=2, type_of_user="LOTTO_AGENT"):
        #     transactions = Transaction.objects.filter(user=user, transaction_type="FUND_BUDDY", narration__in=["LOTTO_COMMISSION", "LOTTO_WINNING_WITHDRAWAL", "LOTTERY_PLAY_COMMISSION", "LOTTERY_WINNING_COMMISSION"])
        #     if transactions:
        #         faulty_users.append(user)
        #     else:
        #         pass

        # print(faulty_users)

        

        # from django.contrib.auth.hashers import make_password, check_password
        # query_reference_data = CyberPayClass.query_qr_ref(reference="LP_QR_9c1e6665-9e5f-4781-b36f-2530c275377a")
        # print(query_reference_data)







        # image_url = "https://nyc3.digitaloceanspaces.com/liberty-pay/media/95/documents/kyc_docs/liveness_selfie.jpeg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=********************%2F20230217%2Fnyc3%2Fs3%2Faws4_request&X-Amz-Date=20230217T164339Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=74f37c67f58288fae9cd8efd1f3cd9c348a809c01586d571091366fa2d5650be"        


        # user_data = {
        #     "last_name": "Nwaoma",
        #     "first_name": "Chukwuemeka",
        #     "face_image": image_url,
        #     "email": "<EMAIL>",
        # }

        # get_result = IdentityPass.enroll_user(data=user_data)
        # print(get_result)






        
        # records = DebitCreditRecordOnAccount.objects.filter(id__gte=13, user__id=1).order_by('id')

        # carried_balance_before = records.first().balance_after
        # print(carried_balance_before)
        # for record in records:

        #     if record == records.first():
        #         pass
        #     else:
        #         record.balance_before = carried_balance_before

        #         if record.entry == "DEBIT":
        #             record.balance_after = carried_balance_before - record.amount
        #         else:
        #             record.balance_after = carried_balance_before + record.amount
                
        #         record.save()

        #         carried_balance_before = record.balance_after




    # do something with each record

        # pass

        # vfd_acct_inst_qs = AccountSystem.objects.filter(account_number="**********") \
        #     .filter(Q(account_type="COLLECTION") | Q(account_type="OTHERS") | Q(account_type="COMMISSIONS") | Q(account_type="FLOAT")
        # )

        # print(vfd_acct_inst_qs)



        # for user in User.objects.filter(type_of_user="LOTTO_AGENT"):
        #     add_agents_whatsapp_group_func(phone_number=user.phone_number)








        # for user in User.objects.filter(sales_rep_upline_code__isnull=False).exclude(sales_rep_upline_code=""):
        # to_be_enc = {"Content-Type": "application/json",}
        # json_form = json.dumps(to_be_enc)
        # get_hashed_trans_pin = encrypt_trans_pin(json_form)
        # print('encrypted', get_hashed_trans_pin)

        # decrpted = decrypt_trans_pin(get_hashed_trans_pin)
        # print('decrypted', decrpted)
        # print('decrypted_json', json.loads(decrpted))

        # pass
    
        # user_list = []
        # for user in User.objects.filter(terminal_suspended=True).exclude(id__in=[591, 561, 1]):
        #     user.terminal_suspended = False
        #     user.inactive_count = 0
        #     user.save()
        #     user_list.append(user.id)


        #     name = user.bvn_first_name.capitalize() if user.bvn_first_name else user.first_name.capitalize()
        #     num = user.phone_number

        #     print(name)
        #     print(num)

        #     url = settings.WHISPER_URL
        #     payload = json.dumps(
        #         {
        #             "receiver": f"{num}",
        #             "template": "70173f77-fbb9-4ba1-9d3f-83e8bbc73257",
        #             "place_holders": {
        #                 "first_name": f"{name}",
        #             },
        #         }
        #     )
        #     headers = {
        #         "Authorization": f"Api_key {settings.WHISPER_KEY}",
        #         "Content-Type": "application/json",
        #     }

        #     response = requests.request("POST", url, headers=headers, data=payload)
        #     res = json.loads(response.text)

        #     print("SMS SENT TO", num)

        # print(user_list)
        

        # token = jwt.encode({'time': str(datetime.now())}, f'{settings.CARD_LOAD_TOKONE}', algorithm='HS256')
        # print(token)

    # token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************.lK7onpVzk1C6Elk_lz6FMzTh-aAjXcPtOA3sF7KfPsY";
    # decoded = jwt.decode(token, 'dfsd', algorithms=['HS256'])

    # print(decoded)

        # print(send_email("<EMAIL>", "132434"))

        # print(create_unique_id_for_customer())
        # get_all_user = User.objects.filter(email="<EMAIL>").last()


        # send_sms_to_user_on_successful_account_creation(
        #     "*************", "CHUKWUEMEKA NWAOMA", "**********", "VFD"
        # )

        # send_credit_alert = WalletSystem.transaction_alert_notfication_manager(
        #     user=get_all_user,
        #     amount = 10000,
        #     # liberty_commission = 10,
        #     cr_dr = "CR",
        #     narration = "LP-CARD-POS-",
        #     from_wallet_type = "COLLECTION"
        # )




        # import random
        # from django.db.models import Count

        # non_unique = (User.objects.values_list('unique_id', flat=True).annotate(unique_id_count=Count('unique_id')).filter(unique_id_count__gt=1))

        # non_unique_person_objects = User.objects.filter(unique_id__in=list(non_unique))
        # print(non_unique_person_objects)


        # for user in get_all_users:
        #     # get_double = User.objects.filter(unique_id=user.unique_id)
        #     # if len(get_double) > 1:
        #     new_unique_id = create_unique_id_for_customer()
        #     user.unique_id = new_unique_id
        #     user.save()
        #     # else:
        #     #     pass




        # for user in User.objects.all():
        #     freshworks_register_contact(user)


        # import jwt
        # >>> from jwt import PyJWKClient
        # >>> token = "************************************************************************************************************************.*******************************************************************************************************************************************************************************************************************************************************************************************************.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        # >>> kid = "NEE1QURBOTM4MzI5RkFDNTYxOTU1MDg2ODgwQ0UzMTk1QjYyRkRFQw"
        # >>> url = "https://dev-87evx9ru.auth0.com/.well-known/jwks.json"
        # >>> jwks_client = PyJWKClient(url)
        # >>> signing_key = jwks_client.get_signing_key_from_jwt(token)
        # >>> data = jwt.decode(
        # ...     token,
        # ...     signing_key.key,
        # ...     algorithms=["RS256"],
        # ...     audience="https://expenses-api",
        # ...     options={"verify_exp": False},
        # ... )
        # >>> print(data)
        # {'iss': 'https://dev-87evx9ru.auth0.com/', 'sub': 'aW4Cca79xReLWUz0aE2H6kD0O3cXBVtC@clients', 'aud': 'https://expenses-api', 'iat': 1572006954, 'exp': 1572006964, 'azp': 'aW4Cca79xReLWUz0aE2H6kD0O3cXBVtC', 'gty': 'client-credentials'}






        #     try:
        #     jwt.decode("JWT_STRING", "secret", algorithms=["HS256"])
        # except jwt.ExpiredSignatureError:





    # Signature has expired
        # insta = ThirdPartyApis()
        # send_out_otp = insta.send_phone_otp(phone_no="*************")
        # print(send_out_otp)

        # for i in Transaction.objects.all():
        #     i.liberty_reference = str(uuid.uuid4())
        #     i.unique_reference = str(uuid.uuid4())
        #     i.save()


        # import base64

        # message = f"{settings.VFD_WEBHOOK_USERNAME}"
        # message_bytes = message.encode()

        # print(str(base64.b64encode(b"")))


        # print(base64.b64encode(f"{settings.VFD_WEBHOOK_USERNAME}".encode()).decode())

        # auth_username = base64.b64encode(f"{settings.VFD_WEBHOOK_USERNAME}".encode()).decode()
        # auth_password = base64.b64encode(f"{settings.VFD_WEBHOOK_PASSWORD}".encode()).decode()

        # expected = base64.b64encode(f"{auth_username}:{auth_password}".encode()).decode()


        # print(expected)
