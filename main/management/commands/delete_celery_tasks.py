from django.core.management.base import BaseCommand
from django.conf import settings
from django_celery_results.models import TaskResult
from datetime import datetime

import jwt
from main.helper.logging_utils import log_info

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        for data in TaskResult.objects.order_by("id")[:5000]:
            data.delete()

        # print(TaskResult.objects.count())

        log_info("done")
