from django.db import models
from django.utils.translation import gettext as _
from django.contrib.humanize.templatetags import humanize
from django.core.files.storage import FileSystemStorage
from django.conf import settings
from django.core.cache import cache

import pyotp
import base64
from main.helper.logging_utils import log_info



class BaseModel(models.Model):
    updated_at = models.DateTimeField(auto_now=True, blank=True)
    created_at = models.DateTimeField(_("date created"), auto_now_add=True, blank=True)

    class Meta:
        abstract = True

    def time_updated(self):
        return f"{humanize.naturaltime(self.updated_at)} - {self.updated_at.date()}"

    def time_created(self):
        return f"{humanize.naturaltime(self.created_at)} -  {self.created_at.date()}"



def get_ip_address(request):
    # Get IP ADDRESS
    address = request.META.get('HTTP_X_FORWARDED_FOR')
    if address:
        ip_addr = address.split(',')[-1].strip()
    else:
        ip_addr = request.META.get('REMOTE_ADDR')
    
    return ip_addr



class CustomMediaStorage(FileSystemStorage):
    def __init__(self, location=None, base_url=None):
        if location is None:
            location = settings.MEDIA_ROOT_2
        if base_url is None:
            base_url = settings.MEDIA_URL_2
        super().__init__(location, base_url)


def convert_string_to_base32_string(input: str) -> str:
    """
    This converts any string to the base32 format of itself

    Args:
        input (str): the string you wish to convert

    Returns:
        str: base32 format of the input string
    """
    # encoding the email string to bytes
    input_string_bytes = input.encode("UTF-8")

    # encoding the bytes to Base32
    input_base32_bytes = base64.b32encode(input_string_bytes)

    # decoding the base32 bytes to string
    input_base32 = input_base32_bytes.decode("UTF-8")

    return input_base32


def generate_otp(phone_number: str, time: int = 900) -> str:
    """
    This generates an OTP using the pyotp package and the user's phone number
    the OTP will be valid for 15 minutes

    Args:
        phone_number (str): phone number of the ajo user
        time (int, optional): how many seconds should the OTP be valid for. Defaults to 900.

    Returns:
        str: the OTP that was generated
    """
    formatted_phone_number = convert_string_to_base32_string(input=phone_number)
    totp = pyotp.TOTP(s=formatted_phone_number, interval=time)
    otp = totp.now()
    return otp


def generate_ussd_otp(phone_number: str) -> str:
    """
    Generates the OTP and persists it in the cache using the otp and phone number
    as cache_key

    Args:
        phone_number (str): the phone number of the Ajo user

    Returns:
        str: returns a 6 digit
    """
    # generate the OTP
    otp: str = generate_otp(phone_number=phone_number, time=500)
    # define the cache key
    cache_key = f"ajo-{otp}-{phone_number}"

    # cache.delete(cache_key)

    
    # create the cache data
    cache_data = {
        "otp": otp,
        "phone_number": phone_number,
    }
    # persist the data in the redis cache
    cache.set(key=cache_key, value=cache_data, timeout=500)
    

    log_info(str(cache_key))

    return otp



def verify_otp(phone_number: str, otp: str, time: int = 900) -> bool:
    """
    This verifies the OTP against the user's phone number as long as it
    in the valid time window

    Args:
        phone_number (str): the user's phone number
        otp (str): the OTP of the user.

    Returns:
        bool: True/False
    """
    formatted_phone_number = convert_string_to_base32_string(input=phone_number)
    totp = pyotp.TOTP(s=formatted_phone_number, interval=time)
    verify = totp.verify(otp)
    return verify



def verify_ussd_otp(otp: str, phone_number: str) -> bool:
    """
    Verifies the OTP for the phone number through a series of checks

    Args:
        otp (str): the OTP the user supplied
        phone_number (str): the phone number of the Ajo user

    Returns:
        bool: True or False
    """
    # define the cache_key
    cache_key = f"ajo-{otp}-{phone_number}"
    log_info(str(cache_key))

    # obtain the data from the cache
    data = cache.get(cache_key)
    log_info(str(data))

    # # check if the data present in the cache
    # if not data:
    #     print("Invalid or Expired OTP")
    #     return False

    return verify_otp(phone_number=phone_number, otp=otp, time=500)