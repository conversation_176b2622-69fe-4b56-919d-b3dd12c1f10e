import requests
import json
import uuid
from django.conf import settings
from main.helper.logging_utils import log_info

base_url = settings.CORE_BANKING_BASE_URL
email = settings.CORE_BANKING_EMAIL
password = settings.CORE_BANKING_PASSWORD


class LibertyCoreBankingAPI:
    @classmethod
    def login(cls):
        url = f"{base_url}/api/v1/companies/auth/login/"

        payload = {
            "email": email,
            "password": password
        }

        try:
            response = requests.request(
                "POST", url=url, data=payload, timeout=15
            )
            res = response.json()
            return res.get("data", {}).get("access")

        except requests.exceptions.RequestException as err:
            log_info(str(err))
            return None

    @classmethod
    def get_access_token(cls):
        access_token = cls.login()
        return access_token

    @classmethod
    def create_account(cls, first_name, last_name, middle_name, email, phone_no, bvn, provider, **kwargs):
        acct_name = f"{first_name} {last_name}"
        if middle_name and middle_name.strip() != "-":
            acct_name = f"{first_name} {middle_name} {last_name}"

        data = {
          "customer_firstname": first_name,
          "customer_surname": last_name,
          "customer_email": email,
          "customer_mobile_no": phone_no,
          "name_on_account": acct_name,
          "customer_middle_name": middle_name if middle_name else "-",
          "date_of_birth": kwargs.get("dob"),
          "gender": kwargs.get("gender"),
          "title": kwargs.get("title"),
          "address_line_1": kwargs.get("address_1"),
          "address_line_2": kwargs.get("address_2"),
          "city": kwargs.get("city"),
          "state": kwargs.get("state"),
          "country": kwargs.get("country"),
          "marital_status": kwargs.get("marital_status"),
          "nin": kwargs.get("nin"),
          "bvn": bvn,
          "local_govt": kwargs.get("local_govt"),
          "provider": provider
        }

        url = f"{base_url}/api/v1/core/central_account_creation/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {cls.get_access_token()}"
        }

        payload = json.dumps(data)
        log_info(str(payload))

        try:
            response = requests.post(url, headers=headers, data=payload, timeout=30)
            log_info(str(response.text))
            response_data = response.json()

            return {
                "status": response_data.get("status"),
                "message": response_data.get("message"),
                "data": response_data.get("data"),
                "request_payload": data
            }

        except requests.exceptions.RequestException as e:
            # Handle request exceptions
            return {
                "status": "error",
                "message": str(e),
                "data": None,
                "request_payload": data
            }
        except ValueError as e:
            # Handle JSON parsing errors
            return {
                "status": "error",
                "message": f"Invalid response format: {str(e)}",
                "data": None,
                "request_payload": data
            }

    @classmethod
    def retrieve_customer(cls, customer_id):
        # This method is a placeholder for future implementation
        return {"status": "error", "message": "Method not implemented"}

    @classmethod
    def send_money(cls, first_name, last_name, amount, source_account, provider, **kwargs):

        # Set mode based on environment
        mode = "TEST" if settings.ENVIRONMENT == "development" else "LIVE"
        transaction_reference = ""

        data = {
            "first_name": first_name,
            "last_name": last_name,
            "email": kwargs.get("email"),
            "phone_number": kwargs.get("phone_number"),
            "transaction_reference": transaction_reference,
            "amount": amount,
            "beneficiary_account_number": kwargs.get("destination_account_no"),
            "beneficiary_account_name": kwargs.get("destination_account_name"),
            "beneficiary_bank_code": kwargs.get("destination_bank_code"),
            "narration": kwargs.get("narration"),
            "mode": mode,
            "source_account": source_account,
            "provider": provider
        }

        url = f"{base_url}/api/v1/core/central_send_money/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {cls.get_access_token()}"
        }

        payload = json.dumps(data)
        log_info(str(payload))

        try:
            response = requests.post(url, headers=headers, data=payload, timeout=30)
            log_info(str(response.text))
            response_data = response.json()

            return {
                "status": response_data.get("status"),
                "reference": response_data.get("data", {}).get("reference"),
                "account_number": response_data.get("data", {}).get("account_number"),
                "account_name": response_data.get("data", {}).get("account_name"),
                "amount": response_data.get("data", {}).get("amount")
            }

        except requests.exceptions.RequestException as e:
            # Handle request exceptions
            return {
                "status": "error",
                "message": str(e),
                "reference": None,
                "account_number": None,
                "account_name": None,
                "amount": None
            }
        except ValueError as e:
            # Handle JSON parsing errors
            return {
                "status": "error",
                "message": f"Invalid response format: {str(e)}",
                "reference": None,
                "account_number": None,
                "account_name": None,
                "amount": None
            }

    @classmethod
    def verify_transaction(cls, reference):
        """
        Verify the status of a transaction using its reference
        """
        url = f"{base_url}/api/v1/wema/verify_event"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {cls.get_access_token()}"
        }

        params = {
            "reference": reference
        }

        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response_data = response.json()

            return {
                "status": response_data.get("status", "error"),
                "message": response_data.get("message", "No message provided"),
                "data": response_data.get("data"),
                "reference": reference
            }

        except requests.exceptions.RequestException as e:
            # Handle request exceptions
            return {
                "status": "error",
                "message": str(e),
                "data": None,
                "reference": reference
            }
        except ValueError as e:
            # Handle JSON parsing errors
            return {
                "status": "error",
                "message": f"Invalid response format: {str(e)}",
                "data": None,
                "reference": reference
            }

    @classmethod
    def get_float_balance(cls, provider):
        """
        Get account details including balance for a specific provider
        """
        url = f"{base_url}/accounts/details"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {cls.get_access_token()}"
        }

        params = {
            "provider": provider
        }

        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response_data = response.json()

            return {
                "status": response_data.get("status", "error"),
                "message": response_data.get("message", "No message provided"),
                "data": response_data.get("data"),
                "provider": provider
            }

        except requests.exceptions.RequestException as e:
            # Handle request exceptions
            return {
                "status": "error",
                "message": str(e),
                "data": None,
                "provider": provider
            }
        except ValueError as e:
            # Handle JSON parsing errors
            return {
                "status": "error",
                "message": f"Invalid response format: {str(e)}",
                "data": None,
                "provider": provider
            }

    @classmethod
    def name_inquiry(cls, account_number, bank_code):
        """
        Perform other banks name inquiry
        """
        url = f"{base_url}/api/v1/paystack/account-inquiry/"

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {cls.get_access_token()}"
        }

        params = {
            "account_number": account_number,
            "bank_code": bank_code
        }

        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            response_data = response.json()

            return {
                "status": response_data.get("status", "error"),
                "message": response_data.get("message", "No message provided"),
                "data": response_data.get("data"),
                "account_number": account_number,
                "bank_code": bank_code
            }

        except requests.exceptions.RequestException as e:
            # Handle request exceptions
            return {
                "status": "error",
                "message": str(e),
                "data": None,
                "account_number": account_number,
                "bank_code": bank_code
            }
        except ValueError as e:
            # Handle JSON parsing errors
            return {
                "status": "error",
                "message": f"Invalid response format: {str(e)}",
                "data": None,
                "account_number": account_number,
                "bank_code": bank_code
            }

