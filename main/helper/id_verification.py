from django.conf import settings
import requests
import json
from main.helper.logging_utils import log_info

class IdentityPass():
    if settings.ENVIRONMENT == "developmentss":
        base_url = settings.IDENTITYPASS_TEST_BASE_URL
        secret_key = settings.IDENTITYPASS_TEST_SECRET_KEY
        app_id = "test_ifiw3n2lhnx047blrgvynz"
    else:
        base_url = settings.IDENTITYPASS_LIVE_BASE_URL
        secret_key = settings.IDENTITYPASS_LIVE_SECRET_KEY
        app_id = settings.IDENTITYPASS_APP_ID



    response_codes = ["00", "01", "02", "03"]

    @classmethod
    def face_liveliness_check(cls, image_url):
        url = f"{cls.base_url}/biometrics/merchant/face/liveliness_check"
        payload = {
            "image": image_url
        }
        headers = {
            "x-api-key": cls.secret_key,
            "app-id": cls.app_id,
        }

        try:
            response = requests.request("POST", url=url, json=payload, headers=headers)
            resp = response.json()
        except requests.exceptions.RequestException as err:
            resp = f"{err}"

        log_info(str(resp))
        return resp
    

    @classmethod
    def enroll_user(cls, data):
        url = f"{cls.base_url}/biometrics/merchant/user/enroll"
        payload = data
        headers = {
            "x-api-key": cls.secret_key,
            "app-id": cls.app_id,
        }

        try:
            response = requests.request("POST", url=url, json=payload, headers=headers)
            resp = response.json()
        except requests.exceptions.RequestException as err:
            resp = f"{err}"

        log_info(str(resp))
        return resp




          