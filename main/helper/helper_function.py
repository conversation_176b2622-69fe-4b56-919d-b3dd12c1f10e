import hashlib
import os
import random
import re
import uuid
from datetime import datetime
from random import randint
from string import Template

import jwt
import requests
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.core.validators import validate_email
from pytz import timezone
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import HtmlContent, Mail

from main.helper.api import *
from main.helper.logging_utils import log_info, log_error

# A function that picks a phone number and prepend 234 to it


def format_phone_no(phone):
    if phone[:3] == "234":
        phone_no = phone
    else:
        phone_no = phone[:0] + "234" + phone[1:]
    return phone_no


# it encrypt the id of the user
def id_encrypt(id):
    data = "christ" + str(id) + "cross"
    data = hashlib.sha256(data.encode("utf-8")).hexdigest()
    return data


def generate_providus_acct_with_first_lastname(first_name, last_name, user_id, bvn_no):
    acct_name = "{}_{}_{}_LBTYP".format(first_name[0:3], last_name[0:3], user_id)
    # providus create dynamic account
    #
    # providus_response = ThirdPartyApis. \
    #     providus_create_new_dynamic_account(acct_name)

    # providus create reserverd account
    providus_response = ThirdPartyApis.providus_create_reservered_account(acct_name, bvn_no)
    return providus_response


def get_user_bvn_profile(user):
    data = {
        "title": f"{user.title}",
        "first_name": f"{user.first_name}",
        "middle_name": f"{user.middle_name}",
        "last_name": f"{user.last_name}",
        "app_name": f"{user.app_name}",
        # "bvn_no": f"{user.}",
        "email": f"{user.email}",
        "gender": f"{user.gender}",
        "phone": f"{user.phone}",
        "phone2": f"{user.phone2}",
        "date_of_birth": f"{user.date_of_birth}",
        "nationality": f"{user.nationality}",
        "profile_image_url": f"{user.profile_image_url}",
        "watchListed": f"{user.watchListed}",
        "maritalStatus": f"{user.maritalStatus}",
        "stateOfResidence": f"{user.stateOfResidence}",
        "lgaOfResidence": f"{user.lgaOfResidence}",
        "lgaOfOrigin": f"{user.lgaOfOrigin}",
        "residentialAddress": f"{user.residentialAddress}",
        "stateOfOrigin": f"{user.stateOfOrigin}",
        # "enrollmentBank": f"{user.enrollmentBank}",
        # "enrollmentBankCode": f"{user.enrollmentBankCode}",
        # "enrollmentBranch": f"{user.enrollmentBranch}",
        # "nameOnCard": f"{user.nameOnCard}",
        # "bvnRegistrationDate": f"{user.bvnRegistrationDate}",
    }
    return data


def get_agent_profile(user):
    data = {
        "phone_number": f"{user.phone_number}",
        # "bvn_num": f"{user.bvn_num}",
        "city": f"{user.city}",
        "email": f"{user.email}",
        "first_name": f"{user.first_name}",
        "form_username": f"{user.form_username}",
        "last_name": f"{user.last_name}",
        "membership": f"{user.membership}",
        "next_of_kin": f"{user.next_of_kin}",
        "next_of_kin_phone_number": f"{user.next_of_kin_phone_number}",
        "postal_zipcode": f"{user.postal_zipcode}",
        "state_province": f"{user.state_province}",
        "street_address_line_1": f"{user.street_address_line_1}",
        "street_address_line_2": f"{user.street_address_line_2}",
        "submission_date": f"{user.submission_date}",
        "unique_id": f"{user.unique_id}",
    }
    return data


def get_user_profile(user):
    data = {
        "phone_number": f"{user.phone_number}",
        "email": f"{user.email}",
        "first_name": f"{user.first_name}",
        "last_name": f"{user.last_name}",
        "state": f"{user.state}",
        "street": f"{user.street}",
    }
    return data


def passcode_generator():
    n = 6
    range_start = 10 ** (n - 1)
    range_end = (10**n) - 1
    value = randint(range_start, range_end)
    return str(value)


# def send_simple_message():
# 	return requests.post(
# 		"https://api.mailgun.net/v3/mg.whispersms.com/messages",
# 		auth=("api", "**************************************************"),
# <AUTHOR> <EMAIL>",
# <AUTHOR> <EMAIL>",
# 			"subject": "Hello Kpongette Inyang",
# 			"html": """<!DOCTYPE html>
# <html>
# <head>
# <title>Page Title</title>
# </head>
# <body>

# <h1>My First Heading</h1>
# <p>My first paragraph.</p>

# </body>
# </html>"""})


# print(send_simple_message().text)


#################################################################################
# WHSISPER SMS


def send_sms_to_invite_user_to_join(phone_number, user):

    full_name = user.full_name()
    url = settings.WHISPER_URL
    payload = json.dumps(
        {
            "receiver": f"{phone_number}",
            "template": "324reerf343232",
            # "template": f"{settings.LIBERTY_AGENCY_INVITE_NEW_USER}",
            "place_holders": {
                "full_name": f"{full_name}",
            },
        }
    )
    headers = {
        "Authorization": "Api_key 6tyghbhgfctrdxrg",
        # "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    res = json.loads(response.text)

    return res


def credit_debit_alert_sms_to_user(phone_number, sms_charge, commissions, amount, cr_dr, desc, balance):

    formatted_date = datetime.strftime(datetime.now(), "%d-%b-%Y %H:%M")

    url = settings.WHISPER_URL
    payload = {
        "receiver": f"{phone_number}",
        "template": f"{settings.LIBERTY_AGENCY_CREDIT_DEBIT_ALERT}",
        "place_holders": {
            "amount": f"{amount}",
            "cr_dr": cr_dr,
            "sms_charge": f"{sms_charge}",
            "comm": f"{commissions}",
            "desc": desc,
            "balance": balance,
            "date": formatted_date,
        },
    }

    headers = {
        # "Authorization": "Api_key 6tyghbhgfctrdxrg",
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    try:
        response = requests.request("POST", url, headers=headers, json=payload)
        res = response.json()

        new_resp = {
            "receiver": phone_number,
            "whisper_resp": res,
            "message": f"SMS SENT TO {phone_number}",
            "payload": payload,
            "sent": True,
        }

    except requests.exceptions.RequestException as e:
        res = "WHISPER IS DOWN"

        new_resp = {
            "receiver": phone_number,
            "whisper_resp": f"{e}",
            "message": res,
            "payload": payload,
            "sent": False,
        }

    return new_resp


# REUSABLE SEND OTP WHISPER HELPER
def send_sms_to_user_with_passcode_otp(phone_number, first_name, passcode):

    url = settings.WHISPER_URL
    payload = json.dumps(
        {
            "receiver": f"{phone_number}",
            "template": f"{settings.LIBERTY_AGENCY_RESET_PINS}",
            "place_holders": {
                "full_name": f"{first_name}",
                "passcode": f"{passcode}",
            },
        }
    )
    headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    res = json.loads(response.text)

    return res


def standard_str_to_dt(date_str: str) -> datetime:

    return datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%S.%fZ")


def validate_email_function(email):
    regex = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"

    if re.fullmatch(regex, email):
        return True
    else:
        return False


def mask_pan(pan):
    first_six = pan[:6]
    last_four = pan[-4:]
    new_pan = first_six + "********" + last_four
    return new_pan


def convert_number_to_datetime(number_str, year=None):
    """
        Converts a string of digits (MMDDHHMMSS) to a datetime object.

        Args:
        number_str: A string of 10 digits representing MMDDHHMMSS.
        year: An optional integer representing the year. If None, the current year is used.

        Returns:
        A datetime object representing the parsed date and time, or None if the
        input string is not in the expected format.
    """
    if not number_str.isdigit() or len(number_str) != 10:
        return None

    month_str = number_str[0:2]
    day_str = number_str[2:4]
    hour_str = number_str[4:6]
    minute_str = number_str[6:8]
    second_str = number_str[8:10]

    try:
        month = int(month_str)
        day = int(day_str)
        hour = int(hour_str)
        minute = int(minute_str)
        second = int(second_str)

        if year is None:
            current_year = datetime.now().year
            return datetime(current_year, month, day, hour, minute, second)
        else:
            return datetime(year, month, day, hour, minute, second)

    except ValueError:
        return None


def mask_phone_number(phone_number):
    formatted_num = "234" + phone_number[-10:]

    first_covered = formatted_num[0:5]
    second_covered = formatted_num[-3:]
    total_covered = first_covered + "*****" + second_covered
    return total_covered


def mask_email(email):
    email_first = email.split("@")[0]
    email_second = email.split("@")[1]
    first_email_covered = email_first[0:2]
    second_email_covered = email_first[-2:]
    asterisks_length = len(email_first) - 4
    asterisk_sign = "*"

    total_covered = f"{first_email_covered}{asterisk_sign*asterisks_length}{second_email_covered}@{email_second}"
    return total_covered


def mask_account_number(number):
    first_covered = number[0:3]
    second_covered = number[-3:]
    total_covered = first_covered + "****" + second_covered
    return total_covered


def convert_num_to_currency(number):
    return "{:,.2f}".format(float(number))


def freshworks_register_contact(kwargs):
    # curl -H "Authorization: Token token=sfg999666t673t7t82" -H "Content-Type: application/json" -d '{"contact":{"first_name":"James", "last_name":"Sampleton (sample)", "mobile_number":"**************", "custom_field": {"cf_is_active": true} }}' -X POST
    url = "https://libertypay.myfreshworks.com/crm/sales/api/contacts"

    headers = {
        "Authorization": f"Token token={settings.FRESHWORKS_API_KEY}",
        "Content-Type": "application/json",
    }

    # payload = json.dumps(
    #     {
    #         "contact": {
    #             "id": kwargs["id"],
    #             "first_name": kwargs["first_name"],
    #             "last_name": kwargs["last_name"],
    #             "display_name": f"{kwargs['first_name']} {kwargs['last_name']}",
    #             "mobile_number": kwargs["mobile_number"],
    #             "email": kwargs["email"]
    #         }
    #     }
    # )

    payload = {
        "contact": {
            "id": f"{kwargs.id}",
            "first_name": f"{kwargs.first_name}",
            "last_name": f"{kwargs.last_name}",
            "display_name": f"{kwargs.first_name} {kwargs.last_name}",
            "mobile_number": f"{kwargs.phone_number}",
            "email": f"{kwargs.email}",
        }
    }

    try:
        response = requests.request("POST", url, headers=headers, json=payload, timeout=10)
        res = response.json()
        return res

    except requests.exceptions.RequestException as err:
        return None


def custom_validate_email(email):
    try:
        validate_email(email)
        return True
    except ValidationError:
        return False


def send_sms_to_user_on_successful_account_creation(phone_number, bvn_full_name, account_number, bank_name):

    url = settings.WHISPER_URL
    payload = json.dumps(
        {
            "receiver": f"{phone_number}",
            "template": f"{settings.LIBERTY_PAY_ACCOUNT_NUMBER_CREATED_SMS}",
            "place_holders": {
                "bvn_full_name": f"{bvn_full_name}",
                "account_number": f"{account_number}",
                "bank_name": f"{bank_name}",
            },
        }
    )
    headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }

    response = requests.request("POST", url, headers=headers, data=payload)
    res = json.loads(response.text)

    return res


def user_file_upload_path(instance, filename, kyc=None):
    """Set document upload path"""
    filename, ext = os.path.splitext(filename)
    old_filename = f"{filename}"
    filename = f"{filename}{ext}"

    if ext == ".jpg" or ext == ".jpeg" or ext == ".png":
        if old_filename.endswith("profile_pic"):
            if kyc != None:
                return f"media/users/{instance.customer_id}/profile_pics/{kyc}/{filename}"
            return f"media/users/{instance.customer_id}/profile_pics/{filename}"
        return f"media/users/{instance.customer_id}/other_pics/{filename}"
    else:
        f"media/users/{instance.customer_id}/files/{filename}"


def trigger_callback_send_out(url, payload, headers):
    if headers is not None:

        from horizon_pay.helpers.helper_function import decrypt_trans_pin

        get_decrypted_header = decrypt_trans_pin(headers)
        headers = eval(json.loads(get_decrypted_header))
        payload = payload

        log_info(str(headers))
        log_info(str(type(headers)))

        log_info(str(url))
        log_info(str(type(url)))

        log_info(str(payload))
        log_info(str(type(payload)))

    try:
        response = requests.post(url=url, json=payload, headers=headers, timeout=30)
        json_resp = response.json()

        if isinstance(json_resp, dict) and json_resp.get("status") == "success":
            resp = {"status": "sent", "response": json_resp}

        else:
            resp = {"status": "unsent", "response": json_resp}

    except requests.exceptions.RequestException as e:
        resp = {"status": "unsent", "response": f"{e}"}

    log_info(str(resp))
    return resp


def add_agents_whatsapp_group_func(phone_number):
    url = "https://pickyassist.com/app/api/v2/add-group-members"

    payload = {
        "token": settings.PICKY_ASSIST_TOKEN,
        "group_id": "120363027779238324",
        "number": [phone_number],
        "application": 10,
    }

    headers = {"Content-Type": "application/json"}

    try:
        response = requests.request("POST", url, headers=headers, json=payload, timeout=3)
        resp = response.json()

        return resp

    except requests.exceptions.RequestException as e:
        resp = {"status": "error", "message": e}

        return resp


def check_security_questions(user, number, security_answer):

    # ratio = fuzz.ratio(security_answer, security_answer_actual)
    # if security_question == request.user.profile.security_question and ratio >= 80:
    #     # security question and answer are correct
    #     return render(request, 'success.html')
    # else:
    #     # security question and answer are incorrect
    #     messages.error(request, 'Invalid security question or answer')

    # def token_sort_ratio(s1, s2, force_ascii=True):
    # """Return a measure of the sequences' similarity using sorted tokens."""
    # # ...
    # s1_tokens = full_process(s1, force_ascii=force_ascii).split()
    # s2_tokens = full_process(s2, force_ascii=force_ascii).split()
    # # ...
    # s1_tokens_sorted = sorted(s1_tokens)
    # s2_tokens_sorted = sorted(s2_tokens)
    # # ...
    # return ratio(s1_tokens_sorted, s2_tokens_sorted)

    if number == 1:
        security_question = user.first_security_question
        security_answer_actual = user.first_security_answer

    elif number == 2:
        security_question = user.second_security_question
        security_answer_actual = user.second_security_answer

    else:
        return False
        # return {
        #     "error": "656",
        #     "message": "Security Question Number Does Not Exist"
        # }

    security_answer_actual = security_answer_actual.lower()
    security_answer = security_answer.lower()

    if security_answer_actual == security_answer:
        return True
    else:
        return False


def generate_super_token(service_name):
    current_datetime = datetime.utcnow()

    expiration_time = datetime(
        year=current_datetime.year,
        month=current_datetime.month,
        day=current_datetime.day,
        hour=23,
        minute=59,
        second=59,
    )

    payload = {"exp": expiration_time, "service_name": service_name}

    # Generate the JWT token
    token = jwt.encode(payload, settings.SECRET_KEY, algorithm="HS256")

    return token


def verify_super_token(super_token, service_name):
    try:
        verification = jwt.decode(super_token, settings.SECRET_KEY, algorithms=["HS256"])

        # {'exp': 1665260219, 'iat': 1665259799, 'pld': {'amount': '100.00'}}

        verify_service_name = verification.get("service_name")

        if service_name != verify_service_name:
            return False

        return True

    except jwt.exceptions.PyJWTError as e:
        return False


def send_dynamic_sms(phone_number, template_id, place_holders):
    url = settings.WHISPER_URL
    payload = json.dumps({"receiver": phone_number, "template": template_id, "place_holders": place_holders})
    headers = {
        "Authorization": f"Api_key {settings.WHISPER_KEY}",
        "Content-Type": "application/json",
    }
    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        res = json.loads(response.text)
        return res
    except Exception as e:
        pass


def starts_with_prefix(string: str):
    prefixes = ["RC", "BN", "IT", "LP", "LPP"]
    for prefix in prefixes:
        if string.startswith(prefix):
            return True
    return False


def generate_random_phone_number(start_num="2341"):

    all_numbers = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]
    get_num = "".join(random.choice(all_numbers) for i in range(9))
    new_phone_num = start_num + get_num

    return new_phone_num


def check_if_is_sunday(date: datetime):
    return date.weekday() == 6


def phone_number_contains_special_characters(phone_number):
    pattern = r"^[\d\s\+\-\(\)]*$"
    return not re.match(pattern, phone_number) or "@" in phone_number


country_phone_data = {
    "1": {"country": "USA/Canada", "min_length": 10, "max_length": 10},
    "7": {"country": "Russia", "min_length": 10, "max_length": 10},
    "20": {"country": "Egypt", "min_length": 9, "max_length": 10},
    "27": {"country": "South Africa", "min_length": 9, "max_length": 9},
    "30": {"country": "Greece", "min_length": 10, "max_length": 10},
    "31": {"country": "Netherlands", "min_length": 9, "max_length": 9},
    "32": {"country": "Belgium", "min_length": 9, "max_length": 9},
    "33": {"country": "France", "min_length": 9, "max_length": 9},
    "34": {"country": "Spain", "min_length": 9, "max_length": 9},
    "36": {"country": "Hungary", "min_length": 9, "max_length": 9},
    "39": {"country": "Italy", "min_length": 9, "max_length": 11},
    "40": {"country": "Romania", "min_length": 9, "max_length": 9},
    "41": {"country": "Switzerland", "min_length": 9, "max_length": 9},
    "43": {"country": "Austria", "min_length": 10, "max_length": 13},
    "44": {"country": "UK", "min_length": 10, "max_length": 10},
    "45": {"country": "Denmark", "min_length": 8, "max_length": 8},
    "46": {"country": "Sweden", "min_length": 9, "max_length": 9},
    "47": {"country": "Norway", "min_length": 8, "max_length": 8},
    "48": {"country": "Poland", "min_length": 9, "max_length": 9},
    "49": {"country": "Germany", "min_length": 10, "max_length": 11},
    "51": {"country": "Peru", "min_length": 9, "max_length": 9},
    "52": {"country": "Mexico", "min_length": 10, "max_length": 10},
    "54": {"country": "Argentina", "min_length": 10, "max_length": 10},
    "55": {"country": "Brazil", "min_length": 10, "max_length": 11},
    "56": {"country": "Chile", "min_length": 9, "max_length": 9},
    "57": {"country": "Colombia", "min_length": 10, "max_length": 10},
    "58": {"country": "Venezuela", "min_length": 10, "max_length": 10},
    "60": {"country": "Malaysia", "min_length": 9, "max_length": 10},
    "61": {"country": "Australia", "min_length": 9, "max_length": 9},
    "62": {"country": "Indonesia", "min_length": 10, "max_length": 13},
    "63": {"country": "Philippines", "min_length": 10, "max_length": 10},
    "64": {"country": "New Zealand", "min_length": 8, "max_length": 10},
    "65": {"country": "Singapore", "min_length": 8, "max_length": 8},
    "66": {"country": "Thailand", "min_length": 9, "max_length": 9},
    "81": {"country": "Japan", "min_length": 10, "max_length": 10},
    "82": {"country": "South Korea", "min_length": 9, "max_length": 11},
    "84": {"country": "Vietnam", "min_length": 9, "max_length": 10},
    "86": {"country": "China", "min_length": 11, "max_length": 11},
    "90": {"country": "Turkey", "min_length": 10, "max_length": 10},
    "91": {"country": "India", "min_length": 10, "max_length": 10},
    "92": {"country": "Pakistan", "min_length": 10, "max_length": 10},
    "93": {"country": "Afghanistan", "min_length": 9, "max_length": 9},
    "94": {"country": "Sri Lanka", "min_length": 9, "max_length": 9},
    "95": {"country": "Myanmar", "min_length": 8, "max_length": 10},
    "98": {"country": "Iran", "min_length": 10, "max_length": 10},
    "212": {"country": "Morocco", "min_length": 9, "max_length": 9},
    "213": {"country": "Algeria", "min_length": 9, "max_length": 9},
    "216": {"country": "Tunisia", "min_length": 8, "max_length": 8},
    "218": {"country": "Libya", "min_length": 9, "max_length": 9},
    "220": {"country": "Gambia", "min_length": 7, "max_length": 7},
    "221": {"country": "Senegal", "min_length": 9, "max_length": 9},
    "222": {"country": "Mauritania", "min_length": 8, "max_length": 8},
    "223": {"country": "Mali", "min_length": 8, "max_length": 8},
    "224": {"country": "Guinea", "min_length": 9, "max_length": 9},
    "225": {"country": "Ivory Coast", "min_length": 8, "max_length": 8},
    "226": {"country": "Burkina Faso", "min_length": 8, "max_length": 8},
    "227": {"country": "Niger", "min_length": 8, "max_length": 8},
    "228": {"country": "Togo", "min_length": 8, "max_length": 8},
    "229": {"country": "Benin", "min_length": 8, "max_length": 8},
    "230": {"country": "Mauritius", "min_length": 7, "max_length": 8},
    "231": {"country": "Liberia", "min_length": 7, "max_length": 9},
    "232": {"country": "Sierra Leone", "min_length": 8, "max_length": 8},
    "233": {"country": "Ghana", "min_length": 9, "max_length": 9},
    "234": {"country": "Nigeria", "min_length": 7, "max_length": 10},
    "235": {"country": "Chad", "min_length": 8, "max_length": 8},
    "236": {"country": "Central African Republic", "min_length": 8, "max_length": 8},
    "237": {"country": "Cameroon", "min_length": 9, "max_length": 9},
    "238": {"country": "Cape Verde", "min_length": 7, "max_length": 7},
    "239": {"country": "Sao Tome and Principe", "min_length": 7, "max_length": 7},
    "240": {"country": "Equatorial Guinea", "min_length": 9, "max_length": 9},
    "241": {"country": "Gabon", "min_length": 7, "max_length": 8},
    "242": {"country": "Congo", "min_length": 9, "max_length": 9},
    "243": {"country": "Democratic Republic of the Congo", "min_length": 9, "max_length": 9},
    "244": {"country": "Angola", "min_length": 9, "max_length": 9},
    "245": {"country": "Guinea-Bissau", "min_length": 7, "max_length": 7},
    "246": {"country": "Diego Garcia", "min_length": 7, "max_length": 7},
    "247": {"country": "Ascension", "min_length": 4, "max_length": 4},
    "248": {"country": "Seychelles", "min_length": 7, "max_length": 7},
    "249": {"country": "Sudan", "min_length": 9, "max_length": 9},
    "250": {"country": "Rwanda", "min_length": 9, "max_length": 9},
    "251": {"country": "Ethiopia", "min_length": 9, "max_length": 9},
    "252": {"country": "Somalia", "min_length": 8, "max_length": 9},
    "253": {"country": "Djibouti", "min_length": 8, "max_length": 8},
    "254": {"country": "Kenya", "min_length": 9, "max_length": 10},
    "255": {"country": "Tanzania", "min_length": 9, "max_length": 9},
    "256": {"country": "Uganda", "min_length": 9, "max_length": 9},
    "257": {"country": "Burundi", "min_length": 8, "max_length": 8},
    "258": {"country": "Mozambique", "min_length": 8, "max_length": 9},
    "260": {"country": "Zambia", "min_length": 9, "max_length": 9},
    "261": {"country": "Madagascar", "min_length": 9, "max_length": 9},
    "262": {"country": "Reunion", "min_length": 9, "max_length": 9},
    "263": {"country": "Zimbabwe", "min_length": 9, "max_length": 10},
    "264": {"country": "Namibia", "min_length": 9, "max_length": 9},
    "265": {"country": "Malawi", "min_length": 9, "max_length": 9},
    "266": {"country": "Lesotho", "min_length": 8, "max_length": 8},
    "267": {"country": "Botswana", "min_length": 8, "max_length": 8},
    "268": {"country": "Swaziland", "min_length": 8, "max_length": 8},
    "269": {"country": "Comoros", "min_length": 7, "max_length": 7},
    "290": {"country": "Saint Helena", "min_length": 4, "max_length": 4},
    "291": {"country": "Eritrea", "min_length": 7, "max_length": 7},
    "297": {"country": "Aruba", "min_length": 7, "max_length": 7},
    "298": {"country": "Faroe Islands", "min_length": 6, "max_length": 6},
    "299": {"country": "Greenland", "min_length": 6, "max_length": 6},
    "350": {"country": "Gibraltar", "min_length": 8, "max_length": 8},
    "351": {"country": "Portugal", "min_length": 9, "max_length": 9},
    "352": {"country": "Luxembourg", "min_length": 9, "max_length": 9},
    "353": {"country": "Ireland", "min_length": 9, "max_length": 9},
    "354": {"country": "Iceland", "min_length": 7, "max_length": 9},
    "355": {"country": "Albania", "min_length": 9, "max_length": 9},
    "356": {"country": "Malta", "min_length": 8, "max_length": 8},
    "357": {"country": "Cyprus", "min_length": 8, "max_length": 8},
    "358": {"country": "Finland", "min_length": 9, "max_length": 10},
    "359": {"country": "Bulgaria", "min_length": 9, "max_length": 9},
    "370": {"country": "Lithuania", "min_length": 8, "max_length": 8},
    "371": {"country": "Latvia", "min_length": 8, "max_length": 8},
    "372": {"country": "Estonia", "min_length": 8, "max_length": 8},
    "373": {"country": "Moldova", "min_length": 8, "max_length": 8},
    "374": {"country": "Armenia", "min_length": 8, "max_length": 8},
    "375": {"country": "Belarus", "min_length": 9, "max_length": 9},
    "376": {"country": "Andorra", "min_length": 6, "max_length": 9},
    "377": {"country": "Monaco", "min_length": 8, "max_length": 9},
    "378": {"country": "San Marino", "min_length": 10, "max_length": 10},
    "380": {"country": "Ukraine", "min_length": 9, "max_length": 9},
    "381": {"country": "Serbia", "min_length": 9, "max_length": 9},
    "382": {"country": "Montenegro", "min_length": 8, "max_length": 8},
    "385": {"country": "Croatia", "min_length": 9, "max_length": 9},
    "386": {"country": "Slovenia", "min_length": 8, "max_length": 8},
    "387": {"country": "Bosnia and Herzegovina", "min_length": 8, "max_length": 8},
    "389": {"country": "North Macedonia", "min_length": 8, "max_length": 8},
    "420": {"country": "Czech Republic", "min_length": 9, "max_length": 9},
    "421": {"country": "Slovakia", "min_length": 9, "max_length": 9},
    "423": {"country": "Liechtenstein", "min_length": 7, "max_length": 9},
    "500": {"country": "Falkland Islands", "min_length": 5, "max_length": 5},
    "501": {"country": "Belize", "min_length": 7, "max_length": 7},
    "502": {"country": "Guatemala", "min_length": 8, "max_length": 8},
    "503": {"country": "El Salvador", "min_length": 8, "max_length": 8},
    "504": {"country": "Honduras", "min_length": 8, "max_length": 8},
    "505": {"country": "Nicaragua", "min_length": 8, "max_length": 8},
    "506": {"country": "Costa Rica", "min_length": 8, "max_length": 8},
    "507": {"country": "Panama", "min_length": 8, "max_length": 8},
    "508": {"country": "Saint Pierre and Miquelon", "min_length": 6, "max_length": 6},
    "509": {"country": "Haiti", "min_length": 8, "max_length": 8},
    "590": {"country": "Guadeloupe", "min_length": 9, "max_length": 9},
    "591": {"country": "Bolivia", "min_length": 8, "max_length": 8},
    "592": {"country": "Guyana", "min_length": 7, "max_length": 7},
    "593": {"country": "Ecuador", "min_length": 9, "max_length": 9},
    "594": {"country": "French Guiana", "min_length": 9, "max_length": 9},
    "595": {"country": "Paraguay", "min_length": 9, "max_length": 9},
    "596": {"country": "Martinique", "min_length": 9, "max_length": 9},
    "597": {"country": "Suriname", "min_length": 7, "max_length": 7},
    "598": {"country": "Uruguay", "min_length": 8, "max_length": 8},
    "599": {"country": "Netherlands Antilles", "min_length": 7, "max_length": 8},
    "670": {"country": "East Timor", "min_length": 8, "max_length": 8},
    "672": {"country": "Norfolk Island", "min_length": 5, "max_length": 5},
    "673": {"country": "Brunei", "min_length": 7, "max_length": 7},
    "674": {"country": "Nauru", "min_length": 7, "max_length": 7},
    "675": {"country": "Papua New Guinea", "min_length": 8, "max_length": 8},
    "676": {"country": "Tonga", "min_length": 5, "max_length": 7},
    "677": {"country": "Solomon Islands", "min_length": 7, "max_length": 7},
    "678": {"country": "Vanuatu", "min_length": 7, "max_length": 7},
    "679": {"country": "Fiji", "min_length": 7, "max_length": 7},
    "680": {"country": "Palau", "min_length": 7, "max_length": 7},
    "681": {"country": "Wallis and Futuna", "min_length": 6, "max_length": 6},
    "682": {"country": "Cook Islands", "min_length": 5, "max_length": 5},
    "683": {"country": "Niue", "min_length": 4, "max_length": 4},
    "685": {"country": "Samoa", "min_length": 5, "max_length": 7},
    "686": {"country": "Kiribati", "min_length": 5, "max_length": 5},
    "687": {"country": "New Caledonia", "min_length": 6, "max_length": 6},
    "688": {"country": "Tuvalu", "min_length": 5, "max_length": 6},
    "689": {"country": "French Polynesia", "min_length": 8, "max_length": 8},
    "690": {"country": "Tokelau", "min_length": 4, "max_length": 4},
    "691": {"country": "Micronesia", "min_length": 7, "max_length": 7},
    "692": {"country": "Marshall Islands", "min_length": 7, "max_length": 7},
    "850": {"country": "North Korea", "min_length": 8, "max_length": 10},
    "852": {"country": "Hong Kong", "min_length": 8, "max_length": 8},
    "853": {"country": "Macau", "min_length": 8, "max_length": 8},
    "855": {"country": "Cambodia", "min_length": 8, "max_length": 9},
    "856": {"country": "Laos", "min_length": 8, "max_length": 9},
    "880": {"country": "Bangladesh", "min_length": 10, "max_length": 10},
    "886": {"country": "Taiwan", "min_length": 9, "max_length": 9},
    "960": {"country": "Maldives", "min_length": 7, "max_length": 7},
    "961": {"country": "Lebanon", "min_length": 8, "max_length": 8},
    "962": {"country": "Jordan", "min_length": 9, "max_length": 9},
    "963": {"country": "Syria", "min_length": 9, "max_length": 9},
    "964": {"country": "Iraq", "min_length": 10, "max_length": 10},
    "965": {"country": "Kuwait", "min_length": 8, "max_length": 8},
    "966": {"country": "Saudi Arabia", "min_length": 9, "max_length": 9},
    "967": {"country": "Yemen", "min_length": 9, "max_length": 9},
    "968": {"country": "Oman", "min_length": 8, "max_length": 8},
    "970": {"country": "Palestinian Territory", "min_length": 9, "max_length": 9},
    "971": {"country": "United Arab Emirates", "min_length": 9, "max_length": 9},
    "972": {"country": "Israel", "min_length": 9, "max_length": 9},
    "973": {"country": "Bahrain", "min_length": 8, "max_length": 8},
    "974": {"country": "Qatar", "min_length": 8, "max_length": 8},
    "975": {"country": "Bhutan", "min_length": 8, "max_length": 8},
    "976": {"country": "Mongolia", "min_length": 8, "max_length": 8},
    "977": {"country": "Nepal", "min_length": 10, "max_length": 10},
    "992": {"country": "Tajikistan", "min_length": 9, "max_length": 9},
    "993": {"country": "Turkmenistan", "min_length": 8, "max_length": 8},
    "994": {"country": "Azerbaijan", "min_length": 9, "max_length": 9},
    "995": {"country": "Georgia", "min_length": 9, "max_length": 9},
    "996": {"country": "Kyrgyzstan", "min_length": 9, "max_length": 9},
    "998": {"country": "Uzbekistan", "min_length": 9, "max_length": 9},
}


def is_phone_number_valid(phone_number):
    # validate phone number length

    if phone_number_contains_special_characters(phone_number=phone_number) == True:
        return False, "invalid phone number"

    for code in sorted(country_phone_data.keys(), key=len, reverse=True):

        cleaned_number = "".join(char for char in phone_number if char.isdigit())

        if cleaned_number.startswith(code):
            national_number = cleaned_number[len(code) :]
            country_info = country_phone_data[code]

            if country_info["min_length"] <= len(national_number) <= country_info["max_length"]:
                # print("country_info", country_info)

                if len(phone_number) != (len(code) + country_info["max_length"]):
                    return False, "invalid phone nnumber"

                # data = {
                #     'valid': True,
                #     'country': country_info['country'],
                #     'country_code': code,
                #     'national_number': national_number,
                #     'formatted_number': f'+{code} {national_number}'
                # }

                return True, "Valid phone number"
            else:
                # data =  {
                #     'valid': False,
                #     'error': f"Invalid length for {country_info['country']}: {len(national_number)} digits"
                # }
                return False, f"Invalid length for {country_info['country']}: {len(national_number)} digits"

    return False, "Invalid or unsupported country code"


def send_verification_to_lotto_supervisor(user, supervisor_id):
    from main.helper.send_emails import new_send_email
    from main.models import User, SuperAgentProfile, ConstantTable

    try:
        # Create SuperAgentProfile
        supervisor = User.objects.get(id=supervisor_id)
        agent_profile, _ = SuperAgentProfile.objects.get_or_create(agent=user)
        agent_profile.supervisor = supervisor
        agent_profile.verification_id = uuid.uuid4()
        agent_profile.save()
        verification_url = ConstantTable.get_constant_table_instance().lotto_agent_verification_url
        # Send verification url to the supervisor
        template_path = os.path.join('templates/', 'supervisor_verification.html')
        email_template = os.path.abspath(template_path)

        with open(email_template, 'r') as f:
            html = f.read()
        template = Template(html).safe_substitute(
            name=user.full_name, phone=user.phone_number,
            link=str(verification_url) + "/" + str(agent_profile.verification_id), address=user.get_full_address,
            email=user.email
        )
        subject = "New Agent Approval"
        receiver = str(supervisor.email).lower()
        new_send_email(receiver, template, subject, meta_data="")
    except Exception as err:
        log_error(f"Error while sending agent verification email to supervisor: {err}")

    return True


def suspend_watchlisted_bvn_user(user):
    from main.models import BVNWatchlist, User, UserFlag

    user_bvn = user.check_kyc.bvn_rel.bvn_number

    if user_bvn:
        # Check if user's bvn is watchlisted or blacklisted, then suspend the user and log the action before returning a response
        if BVNWatchlist.is_bvn_watchlisted(bvn=user_bvn):
            if not user.is_suspended:
                details = f"ALERTTTTTTTTTTT!!\n\nALERTTTTTTTTTTT!!\nUser with email: {user.email} has a watchlisted BVN: {user_bvn} and therefore has been suspended."
                suspend = False
                if not user.type_of_user == "MERCHANT":
                    suspend = True

                    User.suspend_user(
                        user=user,
                        reason=details
                    )
                # Create UserFlag
                UserFlag.create_suspension_instance(
                    user=user,
                    reason=details,
                    is_suspended=suspend
                )
            return True

    return False


