"""
Comprehensive logging utility for Liberty Pay POS system.
This module provides a centralized logging function to replace all print() statements
throughout the codebase with proper file-based logging.
"""

import logging
import inspect
import traceback
from typing import Any, Optional, Dict
from django.conf import settings


class LibertyPayLogger:
    """
    Centralized logging utility for Liberty Pay POS system.
    Provides structured logging with context information.
    """
    
    def __init__(self):
        self.logger = logging.getLogger('libertypay')
        
    def _get_caller_info(self) -> Dict[str, str]:
        """Get information about the calling function/module."""
        frame = inspect.currentframe()
        try:
            # Go up the stack to find the actual caller (skip this method and log_* methods)
            caller_frame = frame.f_back.f_back
            if caller_frame:
                filename = caller_frame.f_code.co_filename
                function_name = caller_frame.f_code.co_name
                line_number = caller_frame.f_lineno
                
                # Extract just the filename from the full path
                module_name = filename.split('/')[-1] if '/' in filename else filename
                
                return {
                    'module': module_name,
                    'function': function_name,
                    'line': str(line_number)
                }
        except Exception:
            pass
        finally:
            del frame
            
        return {'module': 'unknown', 'function': 'unknown', 'line': 'unknown'}
    
    def _format_message(self, message: Any, description: Optional[str] = None, 
                       extra_context: Optional[Dict] = None) -> str:
        """Format the log message with context information."""
        caller_info = self._get_caller_info()
        
        # Convert message to string if it's not already
        if not isinstance(message, str):
            message = str(message)
        
        # Build the formatted message
        parts = []
        
        if description:
            parts.append(f"[{description}]")
        
        parts.append(f"[{caller_info['module']}:{caller_info['function']}:{caller_info['line']}]")
        parts.append(message)
        
        if extra_context:
            context_str = " | ".join([f"{k}={v}" for k, v in extra_context.items()])
            parts.append(f"| Context: {context_str}")
        
        return " ".join(parts)
    
    def info(self, message: Any, description: Optional[str] = None, 
             extra_context: Optional[Dict] = None):
        """Log an info message."""
        formatted_message = self._format_message(message, description, extra_context)
        self.logger.info(formatted_message)
    
    def debug(self, message: Any, description: Optional[str] = None, 
              extra_context: Optional[Dict] = None):
        """Log a debug message."""
        formatted_message = self._format_message(message, description, extra_context)
        self.logger.debug(formatted_message)
    
    def warning(self, message: Any, description: Optional[str] = None, 
                extra_context: Optional[Dict] = None):
        """Log a warning message."""
        formatted_message = self._format_message(message, description, extra_context)
        self.logger.warning(formatted_message)
    
    def error(self, message: Any, description: Optional[str] = None, 
              extra_context: Optional[Dict] = None, include_traceback: bool = False):
        """Log an error message."""
        formatted_message = self._format_message(message, description, extra_context)
        
        if include_traceback:
            formatted_message += f"\nTraceback: {traceback.format_exc()}"
        
        self.logger.error(formatted_message)
    
    def critical(self, message: Any, description: Optional[str] = None, 
                 extra_context: Optional[Dict] = None, include_traceback: bool = True):
        """Log a critical message."""
        formatted_message = self._format_message(message, description, extra_context)
        
        if include_traceback:
            formatted_message += f"\nTraceback: {traceback.format_exc()}"
        
        self.logger.critical(formatted_message)


# Global logger instance
_logger_instance = None

def get_logger() -> LibertyPayLogger:
    """Get the global logger instance (singleton pattern)."""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = LibertyPayLogger()
    return _logger_instance


# Convenience functions for easy usage throughout the codebase
def log_info(message: Any, description: Optional[str] = None, 
             extra_context: Optional[Dict] = None):
    """Log an info message. Replacement for print() statements."""
    get_logger().info(message, description, extra_context)


def log_debug(message: Any, description: Optional[str] = None, 
              extra_context: Optional[Dict] = None):
    """Log a debug message. Use for detailed debugging information."""
    get_logger().debug(message, description, extra_context)


def log_warning(message: Any, description: Optional[str] = None, 
                extra_context: Optional[Dict] = None):
    """Log a warning message. Use for non-critical issues."""
    get_logger().warning(message, description, extra_context)


def log_error(message: Any, description: Optional[str] = None, 
              extra_context: Optional[Dict] = None, include_traceback: bool = False):
    """Log an error message. Use for error conditions."""
    get_logger().error(message, description, extra_context, include_traceback)


def log_critical(message: Any, description: Optional[str] = None, 
                 extra_context: Optional[Dict] = None, include_traceback: bool = True):
    """Log a critical message. Use for system-critical errors."""
    get_logger().critical(message, description, extra_context, include_traceback)


# Specialized logging functions for common use cases
def log_user_action(user_email: str, action: str, details: Optional[str] = None, 
                   extra_context: Optional[Dict] = None):
    """Log user actions for audit purposes."""
    context = {'user_email': user_email, 'action': action}
    if extra_context:
        context.update(extra_context)
    
    message = f"User action: {action}"
    if details:
        message += f" - {details}"
    
    log_info(message, "USER_ACTION", context)


def log_transaction(transaction_type: str, amount: float, user_email: str, 
                   status: str, transaction_id: Optional[str] = None,
                   extra_context: Optional[Dict] = None):
    """Log transaction activities."""
    context = {
        'transaction_type': transaction_type,
        'amount': amount,
        'user_email': user_email,
        'status': status
    }
    
    if transaction_id:
        context['transaction_id'] = transaction_id
    
    if extra_context:
        context.update(extra_context)
    
    message = f"Transaction {status}: {transaction_type} for {amount}"
    log_info(message, "TRANSACTION", context)


def log_api_call(endpoint: str, method: str, status_code: int, 
                user_email: Optional[str] = None, response_time: Optional[float] = None,
                extra_context: Optional[Dict] = None):
    """Log API calls for monitoring purposes."""
    context = {
        'endpoint': endpoint,
        'method': method,
        'status_code': status_code
    }
    
    if user_email:
        context['user_email'] = user_email
    
    if response_time:
        context['response_time_ms'] = response_time
    
    if extra_context:
        context.update(extra_context)
    
    message = f"API {method} {endpoint} -> {status_code}"
    log_info(message, "API_CALL", context)


def log_system_event(event_type: str, message: str, severity: str = "info",
                    extra_context: Optional[Dict] = None):
    """Log system events like startup, shutdown, configuration changes."""
    context = {'event_type': event_type}
    if extra_context:
        context.update(extra_context)
    
    if severity.lower() == "debug":
        log_debug(message, "SYSTEM_EVENT", context)
    elif severity.lower() == "warning":
        log_warning(message, "SYSTEM_EVENT", context)
    elif severity.lower() == "error":
        log_error(message, "SYSTEM_EVENT", context)
    elif severity.lower() == "critical":
        log_critical(message, "SYSTEM_EVENT", context)
    else:
        log_info(message, "SYSTEM_EVENT", context)


# Migration helper function
def replace_print_with_log(message: Any, log_level: str = "info"):
    """
    Helper function to easily replace print() statements.
    
    Usage:
    # Old: print("Some message")
    # New: replace_print_with_log("Some message")
    
    # Old: print(f"Error: {error}")
    # New: replace_print_with_log(f"Error: {error}", "error")
    """
    if log_level.lower() == "debug":
        log_debug(message, "PRINT_REPLACEMENT")
    elif log_level.lower() == "warning":
        log_warning(message, "PRINT_REPLACEMENT")
    elif log_level.lower() == "error":
        log_error(message, "PRINT_REPLACEMENT")
    elif log_level.lower() == "critical":
        log_critical(message, "PRINT_REPLACEMENT")
    else:
        log_info(message, "PRINT_REPLACEMENT")
