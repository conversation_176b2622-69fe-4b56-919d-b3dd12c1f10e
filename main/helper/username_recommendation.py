from unittest import result
from main.models import User
import ast
from random import choice, sample
import requests
import numpy as np
from itertools import combinations 
from main.helper.logging_utils import log_info

def generate_number(user: User):
    # generates and returns 3 random double digit number strings
    email = user.email
    numbers = []
    broken_email = email.split("@")
    first_half = broken_email[0]
    broken_first_half = [i for i in first_half]
    first_number = ""
    for i in broken_first_half:
        try:
            x = int(i)
            first_number += i
        except Exception as e:
            pass
    if first_number == "":
        first_number = str(choice(range(10, 99)))
    numbers.append(first_number)
    y = str(choice(range(10, 99)))
    z = str(choice(range(10, 99)))
    numbers.extend([y, z])
    return numbers

def generate_words(proposed_username: str):
    # generates and returns 3 random words relating in some way to the user
    url = f"https://api.datamuse.com/words?sl={proposed_username}"
    resp = requests.get(url)
    if resp.status_code == 200:
        lst = ast.literal_eval(resp.text)
        words = sample(lst, 3)
        words = [v['word'] for v in words]
        return words


def generate_usernames(user: User, proposed_username: str):
    # len(numbers) = 3
    numbers = generate_number(user)
    # len(words) = 3
    words = generate_words(proposed_username)

    all = [i for i in numbers]
    all.extend(words)
    comb = combinations(all, 2)
    possible_usernames = [i for i in list(comb)]
    pu = ["".join(i) for i in possible_usernames]
    pus = [i[2:]+i[:2] for i in pu]
    usernames = []
    for i in pus:
        try:
            i = int(i)
        except Exception as e:
            usernames.append(i)
    log_info(str(usernames))
    result = []
    for i in usernames:
        try:
            thisUser = User.objects.get(username=i)
        except User.DoesNotExist:
            log_info("here")
            result.append(i)
    
    if len(result) < 1:
        return generate_usernames(user, proposed_username)
    elif len(result) > 4:
        result = np.random.choice(result, size=4, replace=False)
    return result





if __name__ == "__main__":
    generate_words("blank")