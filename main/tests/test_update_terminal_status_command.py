import pytest
from unittest.mock import Mock, patch, MagicMock
from django.core.management import call_command
from django.core.management.base import CommandError
from django.test import TestCase
from horizon_pay.models import TerminalSerialTable, TerminalRetrieval
from main.models import User


class TestUpdateTerminalStatusCommand(TestCase):
    """Test cases for the update_terminal_status_sheet management command"""
    
    def setUp(self):
        """Set up test data"""
        # Create a test user
        self.test_user = User.objects.create(
            username='testuser',
            email='<EMAIL>',
            phone_number='**********',
            first_name='Test',
            last_name='User'
        )
        
        # Create test terminal records
        self.active_terminal = TerminalSerialTable.objects.create(
            terminal_id='TID001',
            terminal_serial='SN001',
            terminal_provider='TEST_PROVIDER',
            user=self.test_user
        )
        
        self.inactive_terminal = TerminalSerialTable.objects.create(
            terminal_id='TID002',
            terminal_serial='SN002',
            terminal_provider='TEST_PROVIDER',
            user=None
        )
        
        self.retrieved_terminal = TerminalSerialTable.objects.create(
            terminal_id='TID003',
            terminal_serial='SN003',
            terminal_provider='TEST_PROVIDER',
            user=None
        )
        
        # Create retrieval record for retrieved terminal
        TerminalRetrieval.objects.create(
            terminal=self.retrieved_terminal,
            status='retrieved'
        )
        
        self.recovered_terminal = TerminalSerialTable.objects.create(
            terminal_id='TID004',
            terminal_serial='SN004',
            terminal_provider='TEST_PROVIDER',
            user=None
        )
        
        # Create retrieval record for recovered terminal
        TerminalRetrieval.objects.create(
            terminal=self.recovered_terminal,
            status='returned'
        )

    def test_determine_terminal_status_active(self):
        """Test status determination for active terminal"""
        from main.management.commands.update_terminal_status_sheet import Command
        
        command = Command()
        status = command._determine_terminal_status('SN001')
        self.assertEqual(status, 'Active')

    def test_determine_terminal_status_inactive(self):
        """Test status determination for inactive terminal"""
        from main.management.commands.update_terminal_status_sheet import Command
        
        command = Command()
        status = command._determine_terminal_status('SN002')
        self.assertEqual(status, 'Inactive')

    def test_determine_terminal_status_retrieved(self):
        """Test status determination for retrieved terminal"""
        from main.management.commands.update_terminal_status_sheet import Command
        
        command = Command()
        status = command._determine_terminal_status('SN003')
        self.assertEqual(status, 'Retrieved')

    def test_determine_terminal_status_recovered(self):
        """Test status determination for recovered terminal"""
        from main.management.commands.update_terminal_status_sheet import Command
        
        command = Command()
        status = command._determine_terminal_status('SN004')
        self.assertEqual(status, 'Recovered')

    def test_determine_terminal_status_not_found(self):
        """Test status determination for non-existent terminal"""
        from main.management.commands.update_terminal_status_sheet import Command
        
        command = Command()
        status = command._determine_terminal_status('NONEXISTENT')
        self.assertEqual(status, 'Not Found')

    @patch('main.management.commands.update_terminal_status_sheet.gspread')
    @patch('main.management.commands.update_terminal_status_sheet.service_account')
    def test_dry_run_mode(self, mock_service_account, mock_gspread):
        """Test that dry-run mode doesn't make actual changes"""
        # Mock Google Sheets API
        mock_client = Mock()
        mock_spreadsheet = Mock()
        mock_worksheet = Mock()
        
        mock_gspread.authorize.return_value = mock_client
        mock_client.open_by_url.return_value = mock_spreadsheet
        mock_spreadsheet.worksheet.return_value = mock_worksheet
        
        # Mock worksheet data
        mock_worksheet.get_all_records.return_value = [
            {'TERMINAL SN': 'SN001', 'TERMINAL STATUS': 'Unknown'}
        ]
        mock_worksheet.row_values.return_value = ['TERMINAL SN', 'TERMINAL STATUS']
        
        # Run command in dry-run mode
        with patch('django.conf.settings.GOOGLE_SHEET_CREDENTIALS', '{}'):
            call_command('update_terminal_status_sheet', '--dry-run', '--verbose')
        
        # Verify that update_cell was not called
        mock_worksheet.update_cell.assert_not_called()

    @patch('main.management.commands.update_terminal_status_sheet.gspread')
    @patch('main.management.commands.update_terminal_status_sheet.service_account')
    def test_command_with_limit(self, mock_service_account, mock_gspread):
        """Test command with row limit"""
        # Mock Google Sheets API
        mock_client = Mock()
        mock_spreadsheet = Mock()
        mock_worksheet = Mock()
        
        mock_gspread.authorize.return_value = mock_client
        mock_client.open_by_url.return_value = mock_spreadsheet
        mock_spreadsheet.worksheet.return_value = mock_worksheet
        
        # Mock worksheet data with multiple records
        mock_worksheet.get_all_records.return_value = [
            {'TERMINAL SN': 'SN001', 'TERMINAL STATUS': 'Unknown'},
            {'TERMINAL SN': 'SN002', 'TERMINAL STATUS': 'Unknown'},
            {'TERMINAL SN': 'SN003', 'TERMINAL STATUS': 'Unknown'}
        ]
        mock_worksheet.row_values.return_value = ['TERMINAL SN', 'TERMINAL STATUS']
        
        # Run command with limit
        with patch('django.conf.settings.GOOGLE_SHEET_CREDENTIALS', '{}'):
            call_command('update_terminal_status_sheet', '--dry-run', '--limit', '2')
        
        # The exact verification would depend on the implementation details
        # This test ensures the limit parameter is accepted

    def test_command_error_handling(self):
        """Test command error handling for missing credentials"""
        with patch('django.conf.settings.GOOGLE_SHEET_CREDENTIALS', None):
            with self.assertRaises(CommandError):
                call_command('update_terminal_status_sheet')
