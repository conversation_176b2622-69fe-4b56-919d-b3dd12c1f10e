import json

import requests
from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.db.models.signals import post_save, pre_delete, post_delete, pre_save
from django.dispatch import receiver
from django.core.cache import cache
from main.models import User, UserFormatted, ConstantTable, Blacklist, SuperAgentProfile, NewLocationList
from main.tasks import (
    create_kyc_object_on_user_create_task,
    send_user_data_to_freshworks_task,
    add_agents_whatsapp_group_task
)
from main.helper.helper_function import freshworks_register_contact
from kyc_app.models import BVNDetail, DocumentFaceMatchKYC2Detail, KYCTable
from accounts.models import AccountSystem, WalletSystem
from admin_dashboard.models import SalesRep

from firebase_admin.messaging import Message
from liberty_pay.settings import cloud_messaging

from main.helper.logging_utils import log_info

###################################################################
# Create KYC for new user


@receiver(post_save, sender=User)
def create_kyc_for_new_user(sender, instance: User, created, **kwargs):
    if created:
        
        # Convert Email to Lowercase
        instance.email = instance.email.lower()
        instance.save()

        user_kyc, created = KYCTable.objects.get_or_create(user=instance)
        bvn_rel, created = BVNDetail.objects.get_or_create(kyc=user_kyc)
        docs_rel, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=user_kyc)


@receiver(post_save, sender=User)
def create_user_formatted_for_user(sender, instance: User, created, **kwargs):

    if created:
        UserFormatted.objects.create(
            user_instance = instance,
            user_id = instance.id,
            first_name = instance.first_name,
            last_name = instance.last_name,
            username = instance.username,
            phone_number = instance.phone_number,
            email = instance.email,
            type_of_user = instance.type_of_user,
            state = instance.state,
            lga = instance.lga,
            nearest_landmark = instance.nearest_landmark,
            street = instance.street,
            business_name = instance.business_name,
            date_joined = instance.date_joined,
            kyc_level = instance.kyc_level,
            terminal_serial = instance.terminal_serial,
            date_assigned = instance.date_assigned
        )
    else:
        user_form = UserFormatted.objects.filter(user_instance=instance).last()
        if user_form:
            # Update the instance of UserFormatted
            user_form.user_instance = instance
            user_form.user_id = instance.id
            user_form.first_name = instance.first_name
            user_form.last_name = instance.last_name
            user_form.username = instance.username
            user_form.phone_number = instance.phone_number
            user_form.email = instance.email
            user_form.type_of_user = instance.type_of_user
            user_form.state = instance.state
            user_form.lga = instance.lga
            user_form.nearest_landmark = instance.nearest_landmark
            user_form.street = instance.street
            user_form.business_name = instance.business_name
            user_form.date_joined = instance.date_joined
            user_form.kyc_level = instance.kyc_level
            user_form.terminal_serial = instance.terminal_serial
            user_form.date_assigned = instance.date_assigned
            user_form.save()
        
        # else:

        #     UserFormatted.objects.create(
        #         user_instance = instance,
        #         user_id = instance.id,
        #         first_name = instance.first_name,
        #         last_name = instance.last_name,
        #         username = instance.username,
        #         phone_number = instance.phone_number,
        #         email = instance.email,
        #         type_of_user = instance.type_of_user,
        #         state = instance.state,
        #         lga = instance.lga,
        #         nearest_landmark = instance.nearest_landmark,
        #         street = instance.street,
        #         business_name = instance.business_name,
        #         date_joined = instance.date_joined,
        #         kyc_level = instance.kyc_level,
        #         terminal_serial = instance.terminal_serial,
        #         date_assigned = instance.date_assigned
        #     )


@receiver(post_save, sender=User)
def send_user_data_to_freshworks(sender, instance: User, created, **kwargs):
    if created:
        register_contact_task = send_user_data_to_freshworks_task.delay(instance.id)


# @receiver(post_save, sender=User)
# def send_notification_for_new_user(sender, instance: User, created, **kwargs):
#     if created:
#         register_contact_task = send_user_data_to_freshworks_task.delay(instance.id)






# @receiver(post_save, sender=KYCTable)
# def create_nin_bvn_drivers_kyc_for_new_user(
#     sender, instance: KYCTable, created, **kwargs
# ):
#     if created:
#         create_bvn_nin_drivers_objects_on_kyc_create_task.delay(instance.id)
        # BVNDetail.objects.create(kyc=instance)
        # NINDetail.objects.create(kyc=instance)
        # DriversLicenseDetail.objects.create(kyc=instance)


# @receiver(post_save, sender=User)
# def create_all_accounts(sender, instance: User, created, **kwargs):
#   if created:
#     AccountSystem.create_accounts(user_id=instance.id)


# @receiver(post_save, sender=User)
# def create_all_wallets(sender, instance: User, created, **kwargs):
#     if created:
#         WalletSystem.create_wallets(user_id=instance.id)

@receiver(signal=post_save, sender=User)
def notify_user_on_user_create(sender, instance, created, **kwargs):

    if created:
        token = instance.firebase_key
        data = {"status": instance.email, "time": str(instance.date_joined)}

        title = f"Welcome, {instance.first_name.capitalize()}"

        message = f"""On behalf of all of us at Liberty Pay,
            we would like to specially welcome you to this journey of possibilities.
            Our goal is to help you expand your business by giving you reliable platform to bank on.
            Thank you for choosing Liberty Pay."""

            
        # title = f"""{instance.first_name.capitalize()}, welcome to this awesome experience."""
        # message = f"""Dear {instance.first_name.capitalize()},on behalf of all of us at team Agency Banking,
        #     we will like to specially welcome you to this journey of possibilities.
        #     Our goal is to expand your business through communication and surely we
        #     believe we will achieve this together.Thank you for choosing whisper.

        #     Liberty Pay"""

        cloud_messaging.send_broadcast(
            token=token, title=title, body=message)


@receiver(post_save, sender=User)
def update_referal_code(sender, instance: User, created, **kwargs):
    if SalesRep.objects.filter(sales_rep=instance).exists():
        pass
    else:
        SalesRep.objects.create(sales_rep=instance)


@receiver(post_save, sender=User)
def add_lotto_agents_to_whatsapp(sender, instance: User, created, **kwargs):
    if created and instance.type_of_user == "LOTTO_AGENT":
        pass
        # add_agents_whatsapp_group_task.delay(phone_number=instance.phone_number)
    else:
        pass



# Signal receiver to clear the cache when the instance or model is updated
@receiver(post_save, sender=ConstantTable)
def clear_constant_table_cache(sender, instance, **kwargs):
    cache_key = 'constant_table_instance_v1'
    cache.delete(cache_key)


@receiver(post_save, sender=Blacklist)
def clear_blacklist_list_cache(sender, instance: Blacklist, created, **kwargs):
    cache_key = f"blacklist_{instance.list_type}"
    cache.delete(cache_key)

##############################################################################################################

# Signal receiver to clear the cache when the instance or model is deleted
@receiver(post_delete, sender=Blacklist)
def clear_user_wallets_list_cache_delete(sender, instance: Blacklist, **kwargs):
    cache_key = f"blacklist_{instance.list_type}"
    cache.delete(cache_key)


@receiver(pre_save, sender=ConstantTable)
def disallow_create_constant_instance(sender, instance: ConstantTable, **kwargs):
    if instance.pk:
        if ConstantTable.objects.filter(pk=instance.pk).exists():
            is_update = True
        else:
            is_update = False
    else:
        is_update = False

    if not is_update:
        if ConstantTable.objects.all().count() > 0:
            raise PermissionDenied("Another instance cannot be created.")


@receiver(pre_delete, sender=ConstantTable)
def protect_constant_instance(sender, instance, **kwargs):
    raise PermissionDenied("This instance cannot be deleted.")


@receiver(post_save, sender=SuperAgentProfile)
def clear_user_wallets_list_cache_delete(sender, instance: SuperAgentProfile, **kwargs):
    # Send notification to liberty draw
    url = str(settings.LIBERTY_DRAW_BASE_URL) + "agent/api/super_agent/down_line_assignment_notification/"
    data = dict()
    agent = instance.agent
    data["agent"] = dict()
    data["agent"]["first_name"] = agent.first_name
    data["agent"]["last_name"] = agent.last_name
    data["agent"]["phone"] = agent.phone_number
    data["agent"]["email"] = agent.email
    data["agent"]["user_id"] = agent.id
    data["agent"]["user_uuid"] = str(agent.customer_id)
    data["agent"]["address"] = agent.get_full_address

    if instance.super_agent:
        super_agent = instance.super_agent
        data["super_agent"] = dict()
        data["super_agent"]["first_name"] = super_agent.first_name
        data["super_agent"]["last_name"] = super_agent.last_name
        data["super_agent"]["phone"] = super_agent.phone_number
        data["super_agent"]["email"] = super_agent.email
        data["super_agent"]["user_id"] = super_agent.id
        data["super_agent"]["user_uuid"] = str(super_agent.customer_id)
        data["super_agent"]["address"] = super_agent.get_full_address

    if instance.supervisor and instance.is_verified:
        supervisor_location = NewLocationList.objects.filter(supervisor=instance.supervisor).last()
        supervisor = instance.supervisor
        data["supervisor"] = dict()
        data["supervisor"]["first_name"] = supervisor.first_name
        data["supervisor"]["last_name"] = supervisor.last_name
        data["supervisor"]["phone"] = supervisor.phone_number
        data["supervisor"]["email"] = supervisor.email
        data["supervisor"]["user_id"] = supervisor.id
        data["supervisor"]["user_uuid"] = str(supervisor.customer_id)
        data["supervisor"]["address"] = supervisor.get_full_address
        data["supervisor"]["supervisor_type"] = instance.supervisor_type
        data["supervisor"]["location"] = supervisor_location.location if supervisor_location else ""

    if instance.team_lead and instance.is_verified:
        team_lead = instance.team_lead
        data["team_lead"] = dict()
        data["team_lead"]["first_name"] = team_lead.first_name
        data["team_lead"]["last_name"] = team_lead.last_name
        data["team_lead"]["phone"] = team_lead.phone_number
        data["team_lead"]["email"] = team_lead.email
        data["team_lead"]["user_id"] = team_lead.id
        data["team_lead"]["user_uuid"] = str(team_lead.customer_id)
        data["team_lead"]["address"] = team_lead.get_full_address
        data["team_lead"]["team_lead_type"] = instance.team_lead_type

    payload = json.dumps(data)

    headers = {'Content-Type': 'application/json'}
    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        log_info(str(response.text))
    except requests.RequestException as err:
        log_info(str(err))
        pass



