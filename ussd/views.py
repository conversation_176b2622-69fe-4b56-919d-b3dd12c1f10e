from django.shortcuts import render
from django.contrib.auth import authenticate

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response

from ussd.models import SendMoneyTable, BillsPayTable
from ussd.helpers.helper_functions import get_access_token_of_user, send_pay_buddy_redirect, fetch_bank_account_name, send_bank_transfer_redirect, ussd_bills_pay_request
from ussd.tasks import send_out_bills_pay_ussd

from main.models import User, ConstantTable, TerminalRetrievalRequest
from main.permissions import CustomIsAuthenticated, CheckIPAddresses

from accounts.models import WalletSystem
from accounts.authentication import CustomTokenAuthentication

from horizon_pay.helpers.helper_function import encrypt_trans_pin

import uuid
import time
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


def get_true_or_false_input(value):
    return bool(int(value))

class SendMoneyTableUSSDCallBackAPIView(APIView):
    # serializer_class = KYCSerializer
    # permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        response = request.data

        SendMoneyTable.objects.create(payload = str(response))


#         {
# }

        {
        "send_money_data":"BUDDY*<EMAIL>*123454*0000*SPEND*COLLECTION*************100*Food*0*0*0*0"
        }

        {
        "send_money_data": "BANK_TRANSFER*<EMAIL>*123454*0000*SPEND************058*GTBank*100*Food*0*0*0*0*100*130.00"
        }
        {
        "send_money_data": "BANK_TRANSFER*<EMAIL>*123454*0000*SPEND************044*Access Bank*100*Food*0*0*0*0*100*130.00"
        }


#             {
#             "send_money_data": "account_number": "**********",
#                   "account_name": "Chukwuemeka Nwaoma",
#                   "bank_code": "058",
#                   "bank_name": "GTBank",
#                   "amount": 100.00,
#                   "narration": "Food",
#                   "is_beneficiary": "False",
#                   "save_beneficiary": "True",
#                   "remove_beneficiary": "False",
#                   "is_recurring": "False"
#                }
#    ],
#    "total_amount": 100.00,
#    "total_amount_with_charge": 130.00,
#    "transaction_pin": "0000"
# }

            # {
            #     "user_email":"nwaomac@gmail",
            #     "user_pin":"12345",
            #     "transaction_pin":"0000",
            #     "from_wallet_type":"SPEND",
            #     "to_wallet_type":"COLLECTION",
            #     "buddy_phone_number":"***********",
            #     "narration":"Food",
            #     "is_beneficiary":"0",
            #     "save_beneficiary":"0",
            #     "remove_beneficiary":"0",
            #     "is_recurring":"0"
            # }

        str_data = response.get("send_money_data")
        data_list = str_data.split("*")

        if data_list[0] == "BUDDY":

            transfer_keys = ["user_email", "user_pin", "transaction_pin", "from_wallet_type", "to_wallet_type", "buddy_phone_number", "amount", "narration", "is_beneficiary", "save_beneficiary", "remove_beneficiary", "is_recurring"]
            transfer_values = data_list[1:]

            # to convert lists to dictionary
            res = dict(zip(transfer_keys, transfer_values))

            user_email = res.get("user_email")
            user_password = res.get("user_pin")
            from_wallet_type = res.get("from_wallet_type")
            to_wallet_type = res.get("to_wallet_type")
            buddy_phone_number = User.format_number_from_back_add_234(res.get("buddy_phone_number"))
            amount = float(res.get("amount"))
            narration = res.get("narration")
            is_beneficiary = get_true_or_false_input(res.get("is_beneficiary"))
            save_beneficiary = get_true_or_false_input(res.get("save_beneficiary"))
            remove_beneficiary = get_true_or_false_input(res.get("remove_beneficiary"))
            is_recurring = get_true_or_false_input(res.get("is_recurring"))
            transaction_pin = res.get("transaction_pin")

            send_money_pay_buddy_payload = {
                "from_wallet_type": from_wallet_type,
                "to_wallet_type": to_wallet_type,
                "data": [
                            {
                                "buddy_phone_number": buddy_phone_number,
                                "amount": amount,
                                "narration": narration,
                                "is_beneficiary": is_beneficiary,
                                "save_beneficiary": save_beneficiary,
                                "remove_beneficiary": remove_beneficiary,
                                "is_recurring": is_recurring
                            }
                ],
                "transaction_pin": transaction_pin
            }

            log_info(str(send_money_pay_buddy_payload))

            user = authenticate(username=res.get("user_email"), password=res.get("user_pin"))
            log_info(str(user))
            if user is not None:
                # login(request=request, user=user)

                # url = reverse('send_money:send_money_pay_buddy'
                # )
                user_access_token = get_access_token_of_user(user_email=user_email, user_password=user_password)
                send_money_pay_buddy = send_pay_buddy_redirect(
                                            send_money_pay_buddy_payload,
                                            user_access_token = user_access_token
                                        )
                # send_money_pay_buddy = send_pay_buddy_redirect(
                #                             from_wallet_type = from_wallet_type,
                #                             to_wallet_type = to_wallet_type,
                #                             buddy_phone_number = buddy_phone_number,
                #                             amount = amount,
                #                             narration = narration,
                #                             is_beneficiary = is_beneficiary,
                #                             save_beneficiary = save_beneficiary,
                #                             remove_beneficiary = remove_beneficiary,
                #                             is_recurring = is_recurring,
                #                             transaction_pin = transaction_pin,
                #                             user_access_token = user_access_token
                #                         )
                log_info(str(send_money_pay_buddy))

        elif data_list[0] == "BANK_TRANSFER":
            transfer_keys = ["user_email", "user_pin", "transaction_pin", "from_wallet_type", "account_number", "bank_code", "bank_name", "amount", "narration", "is_beneficiary", "save_beneficiary", "remove_beneficiary", "is_recurring", "total_amount", "total_amount_with_charge"]
            transfer_values = data_list[1:]

            # to convert lists to dictionary
            res = dict(zip(transfer_keys, transfer_values))

            user_email = res.get("user_email")
            user_password = res.get("user_pin")
            transaction_pin = res.get("transaction_pin")
            from_wallet_type = res.get("from_wallet_type")
            account_number = res.get("account_number")
            # account_name = res.get("account_name")
            bank_code = res.get("bank_code")
            bank_name = res.get("bank_name")
            amount = float(res.get("amount"))
            narration = res.get("narration")
            is_beneficiary = get_true_or_false_input(res.get("is_beneficiary"))
            save_beneficiary = get_true_or_false_input(res.get("save_beneficiary"))
            remove_beneficiary = get_true_or_false_input(res.get("remove_beneficiary"))
            is_recurring = get_true_or_false_input(res.get("is_recurring"))
            total_amount = res.get("total_amount")
            total_amount_with_charge = res.get("total_amount_with_charge")




            user = authenticate(username=res.get("user_email"), password=res.get("user_pin"))
            if user is not None:
                # login(request=request, user=user)

                # url = reverse('send_money:send_money_pay_buddy'
                # )
                user_access_token = get_access_token_of_user(user_email=user_email, user_password=user_password)

                fetch_account_detail = fetch_bank_account_name(account_number=account_number, bank_code=bank_code, user_access_token=user_access_token)
                if fetch_account_detail["message"] == "account name found":
                # "data": {
                #     "account_number": f"{fetch_account_detail['data']['account_number']}",
                #     "account_name": f"{fetch_account_detail['data']['account_name']}",
                # },

                    account_name = fetch_account_detail['data']['account_name']

                    send_money_bank_transfer_payload = {
                        "from_wallet_type": from_wallet_type,
                        "data": [
                                    {
                                        "account_number": account_number,
                                        "account_name": account_name,
                                        "bank_code": bank_code,
                                        "bank_name": bank_name,
                                        "amount": amount,
                                        "narration": narration,
                                        "is_beneficiary": is_beneficiary,
                                        "save_beneficiary": save_beneficiary,
                                        "remove_beneficiary": remove_beneficiary,
                                        "is_recurring": is_recurring
                                    }
                        ],
                        "total_amount": total_amount,
                        "total_amount_with_charge": total_amount_with_charge,
                        "transaction_pin": transaction_pin
                    }

                    send_money_bank_transfer = send_bank_transfer_redirect(
                                                send_money_bank_transfer_payload,
                                                user_access_token = user_access_token
                                            )
                    log_info(str(send_money_bank_transfer))
                else:
                    pass

        else:
            pass



        # print(type(data_list))

        return Response(
            {"status":"true", "message": "response received"},
            status=status.HTTP_200_OK
        )
















# from django.shortcuts import render

# # Create your views here.

# # Afrika's talking
# class MainMenuView(APIView):
#     def post(self, request):

#         request_body = dict(request.POST.items())
#         phone_number = request_body["phoneNumber"].strip('+')
#         service_code = request_body["serviceCode"]
#         text = request_body["text"]
#         session_id = request_body["sessionId"]
#         network_code = request_body["networkCode"]


#         # response = first_menu_view()

#         if text == "":
#             send_new_user_registration_form.delay(phone_number)
#             response = first_menu_view()

#             user_exists = USSD_User.objects.filter(phone_number=phone_number).exists()
#             if not user_exists:
#                 # logger.info(f"User with phone number: {phone_number} does not exist Now creating instance")
#                 USSD_User.objects.create(phone_number=phone_number)

#             # Check if customer profile exists
#             customer_exists = Customer.objects.filter(phone_number=phone_number).exists()
#             if not customer_exists:
#                 initiate_24_hr_reminder.delay(phone_number)
#                 initiate_36_hr_reminder.delay(phone_number)

#         elif text == "1":

#             response = "END We've sent you an SMS with a form to register for a POS. Thank you."


#         elif text == "2":
#             talk_to_agent_sms.delay(phone_number)
#             initiate_whatsapp_conversation_talk_to_agent.delay(phone_number)
#             response = "END  We've sent you a WhatsApp message to further recieve your requests. We'll be waiting to hear from you"

#         elif text == "3":
#             response = "END Our office is located at: No 27, Alara Street, Sabo, Yaba, Lagos."

#         elif text == "4":
#             response = "END Coming Soon!"

#         elif text == "5":
#             response = "END Step 1: Request for a form \nStep 2: Fill the form and choice of POS \nStep 3: Make payment or visit the office \nStep 4: Pick up your device and GO LIVE!"

#         elif text == "6":
#             celery_task = create_account_for_ussd_user.delay(phone_number)
#             sleep(5)
#             get_celery_task = TaskResult.objects.filter(task_id=celery_task).first()
#             task_result = json.loads(get_celery_task.result)

#             if get_celery_task.result != "null":
#                 acct_num = task_result["acct_num"]
#                 acct_name = task_result['acct_name']
#                 acct_bank = task_result['acct_bank']

#                 response = f"END Account Number: {acct_num} \nAccount Name: {acct_name} \nBank Name: {acct_bank}"
#             else:
#                 response = f"END We have forwarded a unique account number to your inbox to enable you make payment"
#         else:
#             response = "END Invalid Command!"


#         return HttpResponse(response)




class UssdGetUserDetailsAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated, CheckIPAddresses(service_name="USSD_BACKEND")]


    def get(self, request):
        phone_number = request.query_params.get('phone')

        if phone_number is None:
            response = {
                "status": False,
                "message": "No phone attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


        get_user = User.objects.filter(phone_number=phone_number).first()
        if get_user:
            response = {
                "status": True,
                "message": "user found",
                "data": {
                    "email": get_user.email,
                    "phone_number": get_user.phone_number
                }
            }

            return Response(response, status=status.HTTP_200_OK)

        else:
            response = {
                "status": False,
                "message": "no user found",
                "data": {}
            }

            return Response(response, status=status.HTTP_404_NOT_FOUND)




class UssdCheckUserPinAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        phone_number = request.query_params.get('phone')
        first_amount = request.query_params.get('amount')
        pin = request.query_params.get('pin')
        network_code = request.query_params.get('network_code')
        source_ip = request.query_params.get('source_ip')

        networks_and_codes = {
            "62120": "AIRTEL_VTU",
            "62130": "MTN_VTU",
            "62150": "GLO_VTU",
            "62160": "9MOBILE_VTU"
        }

        biller = networks_and_codes.get(network_code)

        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')

        log_info(str(ip_addr))

        # Generate a unique session key
        epoch = int(time.time())
        session_key = f"{str(epoch)[-10:]}-{str(uuid.uuid4())}"

        bills_pay_instance = BillsPayTable.objects.create(
            phone_number = phone_number,
            amount = first_amount,
            ip_addr = ip_addr,
            session_key = session_key,
            network_code = network_code,
            biller = biller, 
            source_ip = source_ip
        )


        if phone_number is None or pin is None or first_amount is None or network_code is None or source_ip is None:
            response = {
                "status": False,
                "message": "No phone or pin or amount or network_code or source_ip attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
        # if ip_addr not in ConstantTable.get_constant_table_instance().africas_talking_ips and source_ip not in ConstantTable.get_constant_table_instance().africas_talking_ips:
        if ip_addr not in ConstantTable.get_constant_table_instance().africas_talking_ips:
            # send_sms
            response = {
                "status": False,
                "message": "Error occured"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        

        if network_code not in networks_and_codes:
            response = {
                "status": False,
                "message": "Invalid Number"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        

        try:
            amount = float(first_amount)
        except:
            response = {
                "status": False,
                "message": "Invalid Amount"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
            

        wallet_instance = WalletSystem.objects.filter(user__phone_number=phone_number, wallet_type="COLLECTION").last()
        
        if wallet_instance:
            user_instance = wallet_instance.user

            if user_instance.ussd_active == False:
                response = {
                    "status": False,
                    "message": f"You have not been profiled for USSD",
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            check_pin = User.check_sender_transaction_pin(user_instance, pin)
            if not check_pin:
                retries = User.count_down_transaction_pin_retries(user_instance)
                response = {
                    "status": False,
                    "message": f"Incorrect Pin. Remaining Retries: {retries['remaining_retries']}",
                }

                return Response(response, status=status.HTTP_401_UNAUTHORIZED)

            else:            
                get_user_wallet_balance = WalletSystem.check_wallet_balance(
                    amount = amount,
                    wallet_instance = wallet_instance
                )

                if get_user_wallet_balance["status"] == False:
                    response = {
                        "status": False,
                        "message": f"You do not have sufficient balance to make this transaction of {amount}",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                else:
                    # buy_airtime_task

                    formatted_phone = User.format_number_from_back_add_zero(phone_number=phone_number)
                    if formatted_phone is not None:

                        encypt_pin = encrypt_trans_pin(pin)
                        
                        send_out_task = send_out_bills_pay_ussd.apply_async(
                            queue="ussdbills",
                            kwargs={
                                "phone_number": formatted_phone,
                                "amount": amount,
                                "biller": biller,
                                "user_tid": user_instance.terminal_id,
                                "session_key": session_key,
                                "pin": encypt_pin,                            
                            }
                        )

                        bills_pay_instance.task_id = send_out_task.task_id
                        bills_pay_instance.save()

                        # send_out_request = ussd_bills_pay_request(
                        #     phone_number=formatted_phone,
                        #     amount=amount,
                        #     biller=biller,
                        #     user_tid=user_instance.terminal_id,
                        #     session_key=session_key,
                        #     pin = encypt_pin
                        # )

                        # print(send_out_request)



                        # Set a short expiration time for the session
                        # request.session.set_expiry(settings.SESSION_COOKIE_AGE)

                    response = {
                        "status": True,
                        "message": f"Transaction Processing. Dial *347*180# to request a POS for your business.",
                    }
                    return Response(response, status=status.HTTP_200_OK)
            
        else:

            response = {
                "status": False,
                "message": f"No User Exists with this phone number",
            }

            return Response(response, status=status.HTTP_401_UNAUTHORIZED)



class UssdRequestAccountUnsuspension(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated, CheckIPAddresses(service_name="USSD_BACKEND")]


    def get(self, request):
        phone_number = request.query_params.get('phone_number')

        if phone_number is None:
            response = {
                "status": False,
                "message": "No phone attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


        get_user = User.objects.filter(phone_number=phone_number).first()
        if get_user:
            get_user.reactivation_request = True
            get_user.save()


            response = {
                "status": True,
                "message": "user found",
            }

            return Response(response, status=status.HTTP_200_OK)

        else:
            response = {
                "status": False,
                "message": "no user found",
                "data": {}
            }

            return Response(response, status=status.HTTP_404_NOT_FOUND)


class TerminalRetrievalRequestAPIView(APIView):
    authentication_classes = [CustomTokenAuthentication]
    permission_classes = [CustomIsAuthenticated, CheckIPAddresses(service_name="USSD_BACKEND")]


    def get(self, request):
        phone_number = request.query_params.get('phone_number')

        if phone_number is None:
            response = {
                "status": False,
                "message": "No phone attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)


        get_user = User.objects.filter(phone_number=phone_number).first()
        
        TerminalRetrievalRequest.objects.create(
            user = get_user,
            phone_number = phone_number
        )



        response = {
            "status": True,
            "message": "request accepted",
        }

        return Response(response, status=status.HTTP_200_OK)

