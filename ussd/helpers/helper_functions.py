from django.conf import settings

import json
import requests

import random
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


#################################################################################


{'refresh': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTY1NjYwODEzNywianRpIjoiMDRkMjUyZDVlYzk5NGVjMGJlMWRmNTRhNDhiMzY2M2QiLCJ1c2VyX2lkIjo3MH0.NMLa4LuOx3bf_K61Hc6I747pqK0LQsc3JiwqdF2l_z4', 'access': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNjU2ODY3MzM3LCJqdGkiOiJhNTJhNTc5ODI0ZDg0MDg5YWIyNjhmZDUyM2M0ZDA3MCIsInVzZXJfaWQiOjcwfQ.u2no4fojI7drDxt7Nro0LP83xqaT1OzcuRaK5PhYhpI'}

def get_access_token_of_user(user_email, user_password):
    if settings.DEBUG:
        url = "http://localhost:8000/auth_token/jwt/create/"
    else:
        "http://***************/auth_token/jwt/create/"


    payload = {
        "email": user_email,
        "password": user_password
    }


    response = requests.request("POST", url, json=payload)
    res = response.json()
    return res.get("access")



def send_pay_buddy_redirect(
    send_money_pay_buddy_payload,
    user_access_token):


    url = "http://localhost:8000/send/send_money_paybuddy/"

    payload = send_money_pay_buddy_payload

    headers = {
        "Authorization": f"Bearer {user_access_token}",
    }

    response = requests.request("POST", url, headers=headers, json=payload)
    res = response.json()

    return res


def fetch_bank_account_name(
    account_number,
    bank_code,
    user_access_token):

    url = "http://localhost:8000/send/fetch_account_name/"

    payload = {
        "account_number": account_number,
        "bank_code": bank_code
    }
    headers = {
        "Authorization": f"Bearer {user_access_token}",
    }

    response = requests.request("POST", url, headers=headers, json=payload)
    res = response.json()
    log_info(str(res))
    return res



def send_bank_transfer_redirect(
    send_money_bank_transfer_payload,
    user_access_token):


    url = "http://localhost:8000/send/send_money_bank_account/"

    payload = send_money_bank_transfer_payload

    headers = {
        "Authorization": f"Bearer {user_access_token}",
    }

    response = requests.request("POST", url, headers=headers, json=payload)
    res = response.json()

    return res




def send_salesrep_ussd_code(code):

    url = f"{settings.LIBERTY_MARKETING_URL}/api/salesrep_code/"

    payload = {
        "ussd_code": code
    }

    headers = {
        "Authorization": f"Token 09c1ae2a26c132f0cbd54bac41f43c735b333306",
    }

    response = requests.request("POST", url, headers=headers, json=payload)
    return response.json()



def ussd_bills_pay_request(phone_number, amount, biller, user_tid, session_key, pin):

    url = f"{settings.BASE_URL}/accounts/bills_payments/"

    params = {settings.USSD_SESSION_KEY_KEY: session_key}


    payload = {
        "from_wallet_type": "COLLECTION",
        "customerId": phone_number,
        "packageSlug": biller,
        "channel": "WEB",
        "amount": amount, 
        "customerName": "name",
        "phoneNumber": phone_number,
        "tID": user_tid, 
        "bills_type": "VTU",
        "biller": biller,
        "local_channel": "USSD",
        "transaction_pin": pin
    }

    # headers = {
    #     "Authorization": f"Token 09c1ae2a26c132f0cbd54bac41f43c735b333306",
    # }

    try:
        response = requests.request("POST", url, params=params, json=payload)
        return response.json()
    except requests.exceptions.RequestException as err:
        return f"{err}"
