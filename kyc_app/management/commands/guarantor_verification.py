from kyc_app.serializers import GuarantorDetailSerializer
from django.core.management.base import BaseCommand
from main.serializers import UserCreateSerializer
from kyc_app.models import GuarantorDetail
from main.helper.send_emails import new_send_email, send_single_sendgrid_email
from sendgrid import SendGrid<PERSON><PERSON>lient
from django.conf import settings
from string import Template
import requests
import os
from main.helper.logging_utils import log_info

class Command(BaseCommand):
    help = 'To get all unverified user guarantors and send them a mail and/or SMS'
    html_template = os.path.join('templates/', 'guarantors_form.html')
    email_template = os.path.abspath(html_template)
    sms_template = settings.GUARANTOR_SMS_TEMPLATE
    form_link = f"{settings.GUARANTOR_FORM_LINK}/"
    cancel_link = settings.GUARANTOR_CANCEL_LINK
    whisper_url = settings.WHISPER_URL
    whisper_key = settings.WHISPER_KEY

    def fetch_all_unverified_guarantors(self):
        guarantors = GuarantorDetail.objects.filter(email_sms_sent=False, guarantor_email__isnull=False, is_lotto_user=False, verf_started=False)[:5]
        if len(guarantors) < 1:
            return None, None
        # Key: guarantor email
        # value: user email

        for i in guarantors:
            i.email_sms_sent = True
            i.verf_started = True

        GuarantorDetail.objects.bulk_update(guarantors, fields=["email_sms_sent"])


        g2u = {}
        for g in guarantors:
            user_email = g.kyc.user.email
            user_name = g.kyc.user.first_name + " " + g.kyc.user.last_name
            user_phone = g.kyc.user.phone_number
            g2u[g.guarantor_email] = [user_email, user_phone, user_name]
            g.verification_status = 'PENDING'
            g.save()
        serializer = GuarantorDetailSerializer(guarantors, many=True)
        all_guarantors = [{k: v for k, v in i.items()} for i in serializer.data]
        return all_guarantors, g2u

    def send_bulk_email(self, guarantors, g2u):
        with open(self.email_template, 'r') as f:
            html = f.read()
        for i in guarantors:
            if i['guarantor_email'] != "":
                template = Template(html).safe_substitute(
                    name=g2u[i['guarantor_email']][2],
                    phone=g2u[i['guarantor_email']][1],
                    link=self.form_link + g2u[i['guarantor_email']][1],
                    cancelLink = self.cancel_link + g2u[i['guarantor_email']][1],
                )
                subject = '[Liberty Pay]Guarantor Verification'
                receiver = [{'email': i['guarantor_email']}]
                guarantor_email = i['guarantor_email']
                # resp = send_single_sendgrid_email(receiver, template, subject)
                resp = new_send_email(guarantor_email, template, subject, meta_data="")


    def send_bulk_sms(self, guarantors, g2u):
        for i in guarantors:
            if i['guarantor_email'] != "" and i['guarantor_phone_number'] != "":
                payload = {
                    "receiver": f"{i['guarantor_phone_number']}",
                    "template": self.sms_template,
                    "place_holders": {
                        "name": g2u[i['guarantor_email']][2],
                        "phone": g2u[i['guarantor_email']][1],
                        "link": self.form_link + g2u[i['guarantor_email']][1],
                        "cancelLink": self.form_link + g2u[i['guarantor_email']][1]
                    }
                }
                headers = {
                    "Authorization": f"Api_key {self.whisper_key}",
                    "Content-Type": "application/json",
                }
                try:
                    response = requests.request("POST", self.whisper_url, headers=headers, json=payload)
                except requests.exceptions.RequestException as e:
                    log_info("WHISPER IS DOWN")

    def handle(self, *args, **kwargs):
        all_guarantors, g2u = self.fetch_all_unverified_guarantors()
        if all_guarantors and g2u:
            self.send_bulk_email(all_guarantors, g2u)
            self.send_bulk_sms(all_guarantors, g2u)
        else:
            pass