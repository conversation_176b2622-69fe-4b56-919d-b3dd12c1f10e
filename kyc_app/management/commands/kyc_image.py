import requests
import os
import json
import uuid
import ast

from pprint import pprint
from django.core.management.base import BaseCommand
from django.db.models import Q
from django.core.files.base import ContentFile
from main.serializers import UserCreateSerializer
from kyc_app.models import BVNDetail, DocumentFaceMatchKYC2Detail, GuarantorDetail
from kyc_app.helpers.helper_functions import upload_image
from main.helper.send_emails import send_single_sendgrid_email
from kyc_app.helpers.metamap_manager import MetaMap

from sendgrid import SendGridAPIClient
from django.conf import settings
from base64 import b64decode
from urllib.parse import urlparse
from PIL import Image
from io import BytesIO
from storages.backends.s3boto3 import S3Boto3Storage


from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical

class Command(BaseCommand):

    # def handle(self, *args, **kwargs):
    #     for data in BVNDetail.objects.filter(is_verified=True):
    #         # payload_dump = json.loads(json.dumps(data.payload))
    #         payload_dump = eval(data.payload)

    #         # print(payload_dump)
    #         print(type(payload_dump))

    #         image_url = payload_dump.get("data").get("data")["photo"]
    #         user = data.kyc.user
    #         kyc_level = "kyc_one"

    #         encoded = image_url.replace("data:image/jpg;base64,", "")
    #         print(encoded)
    #         # image_data = b64decode(encoded)
    #         # print(image_data)
        
    #         # print(image_url)

    #         # get_link = upload_image(url=image_url, user=user, kyc=kyc_level)
    #         # print(get_link)


    def handle(self, *args, **kwargs):
        import boto3
        import jwt
        from botocore.client import Config

         # Login to metamap
        metamap_instance = MetaMap()
        access_token = metamap_instance.authenticate_metamap()

        docs_qs = DocumentFaceMatchKYC2Detail.objects.exclude(Q(payload="") | Q(payload=" ")).filter(is_verified=True, payload__isnull=False)

        for data in docs_qs.filter(Q(resource_id__isnull=True)):
            log_debug("------------------------------------------------")
            log_info(f"{data.id}, {data.kyc.user.email}", "user")

            try:
                payload_dump_one = eval(data.payload)
            except:
                payload_dump_one = json.loads(data.payload)

            channel = "METAMAP"
            id_type = payload_dump_one["documents"][0]["type"]
            id_sub_type = payload_dump_one["documents"][0].get("subtype")

            data.resource_id = payload_dump_one["id"]
            data.resource_url = f"https://api.getmati.com/v2/verifications/{data.resource_id}"
            data.channel = channel
            data.id_type = id_type
            data.id_sub_type = id_sub_type


            payload_dump = metamap_instance.retrieve_webhook_resource(verification_id=data.resource_id, access_token=access_token)

            front_view_url = payload_dump["documents"][0]["photos"][0]
            back_view_url = payload_dump["documents"][0]["photos"][1] if len(payload_dump["documents"][0]["photos"]) > 1 else None
            liveness_data = next(item for item in payload_dump["steps"] if item["id"] == "liveness")["data"]

            selfie_url = liveness_data.get("selfieUrl")
            sprite_url = liveness_data.get("spriteUrl")
            video_url = liveness_data.get("videoUrl")

            data.front_view_url = front_view_url
            data.back_view_url = back_view_url
            data.selfie_url = selfie_url
            data.sprite_url = sprite_url
            data.video_url = video_url

            data.save()


        log_debug("------------------------------------------------")
        log_info("DONE WITH FIRST LOOP")
        log_debug("------------------------------------------------")


        for doc in docs_qs.filter(resource_retrieved=False):
            try:
                log_debug("------------------------------------------------")
                log_info(f"{doc.id}, {doc.kyc.user.email}", "user")

                payload_dump = metamap_instance.retrieve_webhook_resource(verification_id=doc.resource_id, access_token=access_token)

                log_info(str(payload_dump))

                front_view_url = payload_dump["documents"][0]["photos"][0]
                back_view_url = payload_dump["documents"][0]["photos"][1] if len(payload_dump["documents"][0]["photos"]) > 1 else None
                liveness_data = next(item for item in payload_dump["steps"] if item["id"] == "liveness")["data"]

                selfie_url = liveness_data.get("selfieUrl")
                sprite_url = liveness_data.get("spriteUrl")
                video_url = liveness_data.get("videoUrl")
                
                for field in [doc.selfie_url, doc.sprite_url, doc.video_url, doc.front_view_url, doc.back_view_url]:
                    user = doc.kyc.user
                    if field == doc.selfie_url:
                        field_url = selfie_url
                        file_name = "liveness_selfie.jpeg"
                        file_path = f"media/{user.id}/documents/kyc_docs/{file_name}"

                    elif field == doc.sprite_url:
                        field_url = sprite_url
                        file_name = "liveness_sprite.jpeg"
                        file_path = f"media/{user.id}/documents/kyc_docs/{file_name}"

                    elif field == doc.video_url:
                        field_url = video_url
                        file_name = "liveness_video.mp4"
                        file_path = f"media/{user.id}/documents/kyc_docs/{file_name}"

                    elif field == doc.front_view_url:
                        field_url = front_view_url
                        file_name = "doc_front.jpeg"
                        file_path = f"media/{user.id}/documents/kyc_docs/{file_name}"

                    elif field == doc.back_view_url:
                        field_url = back_view_url
                        file_name = "doc_back.jpeg"
                        file_path = f"media/{user.id}/documents/kyc_docs/{file_name}"

                    
                    if field_url == None:
                        pass
                    else:

                        # # Parse the image URL to get the filename
                        # parsed_url = urlparse(field_url)
                        # try:
                        #     filename = parsed_url.path.split('/')[-1]
                        # except:
                        #     filename = parsed_url.path.decode().split('/')[-1]


                        response = metamap_instance.retrieve_resource(resource_url=field_url, access_token=access_token)

                        # Download the image from the URL
                        response = requests.get(field_url)
                        log_info(str(response))

                        if field == doc.video_url:
                            s3 = boto3.resource('s3',
                                    aws_access_key_id=settings.AWS_S3_ACCESS_KEY_ID,
                                    aws_secret_access_key=settings.AWS_S3_SECRET_ACCESS_KEY,
                                    endpoint_url=settings.AWS_S3_ENDPOINT_URL
                            )
                            s3.Object(settings.AWS_STORAGE_BUCKET_NAME, file_path).put(Body=response.content)
                        
                        else:
                            img = Image.open(BytesIO(response.content))

                            # Save the image to Digital Ocean Spaces with the desired directory structure
                            s3 = S3Boto3Storage()
                            image_file = ContentFile(response.content)
                            s3.save(file_path, image_file)


                doc.resource_retrieved = True
                doc.save()

            except:
                pass













        # # Set up a client for Digital Ocean Spaces using the API key and secret key
        # s3 = boto3.client(
        #     's3',
        #     region_name='nyc3',
        #     endpoint_url='https://nyc3.digitaloceanspaces.com',
        #     aws_access_key_id=settings.AWS_S3_ACCESS_KEY_ID,
        #     aws_secret_access_key=settings.AWS_S3_SECRET_ACCESS_KEY,
        #     config=Config(signature_version='s3v4')
        # )

        # # # Set up your JWT secret and payload
        # # jwt_secret = 'your_jwt_secret'
        # # payload = {'user_id': '1234'}

        # # # Generate a JWT token
        # # token = jwt.encode(payload, jwt_secret, algorithm='HS256')

        # # Set the name of the Space and the path to the object you want to get the pre-signed URL for
        # space_name = settings.AWS_STORAGE_BUCKET_NAME
        # # object_path = "media/95/documents/kyc_docs/liveness_video.mp4"
        # object_path = "media/95/documents/kyc_docs/liveness_selfie.jpeg"

        # # Set an expiration time for the pre-signed URL (in seconds)
        # expiration_time = 3600

        # # Generate a pre-signed URL for the object with the given expiration time
        # url = s3.generate_presigned_url(
        #     'get_object',
        #     Params={'Bucket': space_name, 'Key': object_path},
        #     ExpiresIn=expiration_time,
            
        # )

        # # Print the URL to the console
        # print(url)

        # # https://nyc3.digitaloceanspaces.com/liberty-pay/media/95/documents/kyc_docs/liveness_video.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=DO00WKFBU7CE8JKFXY2Z%2F20230217%2Fnyc3%2Fs3%2Faws4_request&X-Amz-Date=20230217T163818Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=a8ae813161b2f1f7b939e5631f661febde0a5ee480914e652f8ee942837fbc66

        






















        #     # image_url = payload_dump.get("data").get("data")["photo"]
        #     # user = data.kyc.user
        #     # kyc_level = "kyc_one"

        #     # encoded = image_url.replace("data:image/jpg;base64,", "")
        #     # print(encoded)
        #     # image_data = b64decode(encoded)
        #     # print(image_data)
        
        #     # print(image_url)

        #     # get_link = upload_image(url=image_url, user=user, kyc=kyc_level)
        #     # print(get_link)