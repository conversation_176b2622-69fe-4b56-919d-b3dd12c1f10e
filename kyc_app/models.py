import json
from django.db import models
from django.core.exceptions import ValidationError
from django.conf import settings
from django.db.models import Q
from kyc_app.helpers.helper_functions import create_doc_verfication_reference, verify_bvn_with_uverify, verify_bvn_with_dojah

from kyc_app.utils import decode_and_validate_image, transform_rekognition_response, validate_image
from main.helper.helper_function import mask_phone_number
from main.models import User

from datetime import datetime
from storages.backends.s3boto3 import S3Boto3Storage
import uuid
import boto3
import base64


# from main.tasks import send_bvn_otp_task

# Create your models here.


class KYCTable(models.Model):
    user = models.OneToOneField(
        User, related_name="check_kyc", on_delete=models.CASCADE
    )
    is_kyc_level_one = bool
    is_kyc_level_two = bool
    is_kyc_level_three = bool
    kyc_level = int
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    # kyc_is_complete = bool

    def __str__(self) -> str:
        return f"{self.user}"

    @staticmethod
    def create_kyc_dependencies(kyc_id) -> bool:
        kyc = KYCTable.objects.filter(id=kyc_id).first()
        BVNDetail.objects.create(kyc=kyc)
        DocumentFaceMatchKYC2Detail.objects.create(kyc=kyc)
        # DriversLicenseDetail.objects.create(kyc=kyc)

    @property
    def is_kyc_level_one(self) -> bool:
        # bvn_kyc = BVNDetail.objects.filter(kyc=self).last()
        # if not bvn_kyc:
        #     return False
        # else:
        #     if bvn_kyc.is_verified:
        #         # KYC LEVEL 1
        #         return True
        #     else:
        #         return False

        bvn_kyc, created = BVNDetail.objects.get_or_create(kyc=self)
        if bvn_kyc.is_verified:
            # KYC LEVEL 1
            return True
        else:
            return False

    @property
    def is_kyc_level_two(self) -> bool:

        # docface_kyc = DocumentFaceMatchKYC2Detail.objects.filter(kyc=self).last()
        # if not docface_kyc:
        #     return False
        # else:
        #     if docface_kyc.is_verified and self.is_kyc_level_one == True:
        #         # KYC LEVEL 1
        #         return True
        #     else:
        #         return False

        docface_kyc, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=self)
        if docface_kyc.is_verified and self.is_kyc_level_one == True:
            # KYC LEVEL 1
            return True
        else:
            return False

    @property
    def is_kyc_level_three(self) -> bool:

        # guarantor_rel, created = GuarantorDetail.objects.get_or_create(kyc=self)
        # if guarantor_rel.is_verified and self.is_kyc_level_one == True and self.is_kyc_level_two:
        query = Q(kyc=self)
        guarantor_rel = GuarantorDetail.objects.filter(query)
        if self.user.type_of_user == "STAFF_AGENT":
            query |= Q(kyc_extra=self)
            guarantor_rel = GuarantorDetail.objects.filter(query, is_verified=True)
            if len(guarantor_rel) < 2:
                return False
        if not guarantor_rel:
            return False
        if guarantor_rel.last().is_verified and self.is_kyc_level_one and self.is_kyc_level_two:
            # KYC LEVEL 1
            return True
        else:
            return False

    @property
    # @staticmethod
    def kyc_level(self) -> int:
        """
        Check user KYC level
        """

        if self.is_kyc_level_three:
            # KYC LEVEL 2
            return 3
        elif self.is_kyc_level_two:
            # KYC LEVEL 2
            return 2
        elif self.is_kyc_level_one:
            # KYC LEVEL 1
            return 1
        else:
            # No KYC
            return 0

    @staticmethod
    def check_user_kyc_level(user) -> int:
        """
        Check user KYC level
        """

        kyc_user = KYCTable.objects.filter(user=user).last()
        if not kyc_user:
            return 0
        else:
            if kyc_user.is_kyc_level_three:
                # KYC LEVEL 3
                return 3
            elif kyc_user.is_kyc_level_two:
                # KYC LEVEL 1
                return 2
            elif kyc_user.is_kyc_level_one:
                # KYC LEVEL 2
                return 1
            else:
                # No KYC
                return 0

    def save(self, *args, **kwargs):
        # kyc_level = KYCTable.check_user_kyc_level(self.user)
        # self.user.kyc_level = kyc_level
        # self.user.save()
        super(KYCTable, self).save(*args, **kwargs)


# BVN Detail
class BVNDetail(models.Model):
    SUCCESSFUL = "SUCCESSFUL"
    PENDING = "PENDING"
    NOT_VERIFIED = "NOT_VERIFIED"
    REJECTED = "REJECTED"

    VERIFICATION_STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (PENDING, "PENDING"),
        (NOT_VERIFIED, "NOT_VERIFIED"),
        (REJECTED, "REJECTED")
    ]

    kyc = models.OneToOneField(
        KYCTable, related_name="bvn_rel", on_delete=models.CASCADE
    )
    # bvn_image = models.ImageField(upload_to=get_uplaod_file_name, null=True)
    verified_by = models.CharField(max_length=100, null=True, blank=True)
    bvn_number = models.CharField(max_length=100, null=True, blank=True)
    bvn_phone_number = models.CharField(max_length=100, null=True, blank=True)
    bvn_phone_number_2 = models.CharField(max_length=100, null=True, blank=True)
    bvn_first_name = models.CharField(max_length=250, null=True, blank=True)
    bvn_middle_name = models.CharField(max_length=250, null=True, blank=True)
    bvn_last_name = models.CharField(max_length=250, null=True, blank=True)
    bvn_gender = models.CharField(max_length=250, null=True, blank=True)
    bvn_birthdate = models.CharField(max_length=250, null=True, blank=True)
    bvn_nationality = models.CharField(max_length=250, null=True, blank=True)
    bvn_watchlisted = models.CharField(max_length=100, null=True, blank=True)
    bvn_marital_status = models.CharField(max_length=250, null=True, blank=True)
    bvn_lga_of_origin = models.CharField(max_length=250, null=True, blank=True)
    bvn_residential_address = models.CharField(max_length=250, null=True, blank=True)
    bvn_state_of_origin = models.CharField(max_length=250, null=True, blank=True)
    id_start_date = models.DateField(null=True, blank=True)
    id_end_date = models.DateField(null=True, blank=True)
    date_added = models.DateTimeField(null=True, blank=True)
    expires = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    channel = models.CharField(max_length=150, null=True, blank=True)
    verification_status = models.CharField(max_length=200, choices=VERIFICATION_STATUS_CHOICES, default="NOT_VERIFIED")
    failure_reason = models.CharField(max_length=1000, null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    verf_payload = models.TextField(null=True, blank=True)
    selfie_url = models.TextField(null=True, blank=True)
    sprite_url = models.TextField(null=True, blank=True)
    video_url = models.TextField(null=True, blank=True)
    bvn_image_url = models.TextField(null=True, blank=True)
    kyc_verf_payload = models.TextField(null=True, blank=True)
    kyc_verf_response = models.TextField(null=True, blank=True)

    @staticmethod
    def verify_non_user_bvn(bvn_number, last_name):

        {'error': None,
         'data': {'bvn': '', 'firstName': '', 'lastName': '', 'middleName': '', 'gender': 'Male', 'phone': '', 'dateOfBirth': '', 'photo': ''}}

        response = verify_bvn_with_uverify(
            bvn_number=bvn_number, last_name=last_name
        )

        if response.get("success") == True and response.get("statusCode") == 200:
            if response.get("verified_by") == "youverify":

                data = response.get("data")["data"]

                bvn_phone_number = data.get("mobile")
                if bvn_phone_number and bvn_phone_number not in ["", " "]:
                    formatted_bvn_phone_number = User.format_number_from_back_add_234(
                        bvn_phone_number
                    )
                    return {
                        "status": True,
                        "bvn_phone_number": formatted_bvn_phone_number,
                        "data": response
                    }
                else:
                    return {
                        "status": False,
                        "bvn_phone_number": None,
                        "data": response
                    }


            elif response.get("verified_by") == "hadada":
                data = response.get("data")["data"]
                bvn_phone_number = data.get("phone")
                if bvn_phone_number:
                    formatted_bvn_phone_number = User.format_number_from_back_add_234(
                        bvn_phone_number
                    )

                    return {
                        "status": True,
                        "bvn_phone_number": formatted_bvn_phone_number,
                        "data": response
                    }
                else:
                    return {
                        "status": False,
                        "bvn_phone_number": None,
                        "data": response
                    }
        else:
            return {
                "status": False,
                "bvn_phone_number": None,
                "data": response
            }

    @staticmethod
    def verify_bvn(bvn_number, user, last_name=None, verified_for=None, verification_id=None, get_masked_phone=False):

        if user is None and last_name is not None:
            new_last_name = last_name
            non_user = True
        else:
            new_last_name = user.last_name
            non_user = False

        if settings.ENVIRONMENT == "development":
            return {
                "status": True,
                "bvn_phone_number": "070xxxxxxxxx",
                "data": None
            }

        # First verify with YouVerify
        response = verify_bvn_with_uverify(
            bvn_number=bvn_number, last_name=new_last_name
        )

        request_passed = False
        bvn_phone_number = bvn_digit = phone_2_digit = first_name = mid_name = l_name = dob = ""
        verifier = gender = nationality = marital_status = lga_of_origin = residential_address = state_of_origin = ""

        if response.get("success") and response.get("statusCode") == 200 and response.get("data", {}).get("data", {}).get("firstName"):
            request_passed = True
            data = response.get("data")["data"]

            bvn_phone_number = data.get("mobile")
            bvn_digit = data.get("idNumber")
            phone_2_digit = data.get("phone2", None)
            first_name = data.get("firstName")
            mid_name = data.get("middleName")
            l_name = data.get("lastName")
            dob = data.get("dateOfBirth")
            verifier = "youverify"

        else:
            # Try verify with DoJah
            response = verify_bvn_with_dojah(bvn_number)
            if response.get("success") and response.get("statusCode") == 200:
                request_passed = True

                data = response["data"]["entity"]
                bvn_phone_number = data["phone_number1"]
                bvn_digit = data["bvn"]
                phone_2_digit = data["phone_number2"]
                first_name = data["first_name"]
                mid_name = data["middle_name"]
                l_name = data["last_name"]
                dob = data["date_of_birth"]
                gender = data["gender"]
                nationality = data["nationality"]
                marital_status = data["marital_status"]
                lga_of_origin = data["lga_of_origin"]
                residential_address = data["residential_address"]
                state_of_origin = data["state_of_origin"]
                verifier = "dojah"

        if request_passed:
            # if not non_user:

            if bvn_phone_number and bvn_phone_number not in ["", " ", None]:
                formatted_bvn_phone_number = User.format_number_from_back_add_234(
                    bvn_phone_number
                )
            else:
                formatted_bvn_phone_number = bvn_phone_number

            if get_masked_phone:
                return {
                    "status": True,
                    "bvn_phone_number": mask_phone_number(formatted_bvn_phone_number),
                    "data": dict()
                }

            BVNDetail.objects.filter(kyc=user.check_kyc).update(
                bvn_number=bvn_digit,
                bvn_phone_number=formatted_bvn_phone_number,
                bvn_phone_number_2=phone_2_digit,
                bvn_first_name=first_name,
                bvn_middle_name=mid_name,
                bvn_last_name=l_name,
                bvn_birthdate=dob,
                date_added=datetime.now(),
                payload=response,
                verified_by=verifier,
                bvn_gender=gender,
                bvn_nationality=nationality,
                bvn_marital_status=marital_status,
                bvn_lga_of_origin=lga_of_origin,
                bvn_residential_address=residential_address,
                bvn_state_of_origin=state_of_origin
            )

            return {
                "status": True,
                "bvn_phone_number": formatted_bvn_phone_number,
                "data": response
            }

        else:
            return {
                "status": False,
                "bvn_phone_number": None,
                "data": dict()
            }

    def save(self, *args, **kwargs):

        if self.is_verified:
            self.kyc.user.bvn_first_name = self.bvn_first_name
            self.kyc.user.bvn_last_name = self.bvn_last_name
            self.kyc.user.bvn_number = self.bvn_number
            self.verification_status = "SUCCESSFUL"

            first_bvn_user = User.objects.filter(bvn_number=self.bvn_number).first()
            if first_bvn_user and self.kyc.user != first_bvn_user:
                self.kyc.user.vfd_bvn_acct_num_count = first_bvn_user.vfd_bvn_acct_num_count

            if self.kyc.user.ussd_active_admin_lock == False:
                self.kyc.user.ussd_active = True

            # user = self.kyc.user

            # check_master_user = User.objects.filter(bvn_number=user.check_kyc.bvn_rel.bvn_number)
            # if len(check_master_user) > 0:
            #     get_master_bvn_user = check_master_user.filter(master_bvn=True).first()

            #     user.master_bvn = False

            #     get_master_bvn_user.vfd_bvn_acct_num_count += 1
            #     get_master_bvn_user.save()

            #     for user_data in check_master_user.exclude(id=get_master_bvn_user.id):
            #         user_data.vfd_bvn_acct_num_count = get_master_bvn_user.vfd_bvn_acct_num_count
            #         user_data.save()

            # for user_data in User.objects.exclude(id=user.id).filter(bvn_number=user.check_kyc.bvn_rel.bvn_number):
            # user_data.vfd_bvn_acct_num_count = user.vfd_bvn_acct_num_count

            # self.kyc.user.kyc_one_progress = self.verification_status
        else:
            self.verification_status = "NOT_VERIFIED"
            self.kyc.user.bvn_number = None
            self.kyc.user.bvn_first_name = None
            self.kyc.user.bvn_last_name = None

            self.kyc.user.ussd_active = False

        ############################################################################
        # UPDATE NUMBER OF USER ACCOUNTS

        # user.vfd_bvn_acct_num_count += 1
        # user.save(update_fields=["vfd_bvn_acct_num_count"])

        # user_data.save(update_fields=["vfd_bvn_acct_num_count"])

        self.kyc.user.save()

        # if self.kyc.user.email == "<EMAIL>":
        # if self.kyc.user.email == "<EMAIL>":
        #     print("I AM HERRRRRRRRRRRRRRRRRR")
        #     self.is_verified = False
        # self.kyc.save()

        # update_fields = kwargs["update_fields"]
        # for i in update_fields:
        #     if i == "bvn_number":
        #         get_user = self.kyc.user
        #         get_bvn = self.bvn_number

        #         same_bvns = BVNDetail.objects.exclude(id=self.id).filter(bvn_number=self.bvn_number)

        #         for bvn in same_bvns:
        #             get_user = bvn.kyc.user
        #             get_user.vfd_bvn_acct_num_count += 1
        #             get_user.save()

        super(BVNDetail, self).save(*args, **kwargs)


# KYC TWO

class CreateReferenceForVerification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    reference = models.CharField(max_length=150)
    for_kyc_level = models.PositiveIntegerField()
    ip_addr = models.CharField(max_length=150, null=True, blank=True)
    verf_complete = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    @classmethod
    def create_verf_ref(cls, user, ip_addr, for_kyc_level):
        create_reference = create_doc_verfication_reference()

        if cls.objects.filter(reference=create_reference).exists():
            return cls.create_verf_ref(user=user, ip_addr=ip_addr)

        cls.objects.create(
            user=user,
            reference=create_reference,
            ip_addr=ip_addr,
            for_kyc_level=for_kyc_level
        )

        return create_reference


class DocumentFaceMatchKYC2Detail(models.Model):
    SUCCESSFUL = "SUCCESSFUL"
    PENDING = "PENDING"
    NOT_VERIFIED = "NOT_VERIFIED"
    REJECTED = "REJECTED"

    VERIFICATION_STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (PENDING, "PENDING"),
        (NOT_VERIFIED, "NOT_VERIFIED"),
        (REJECTED, "REJECTED")
    ]

    kyc = models.OneToOneField(
        KYCTable, related_name="docsface_rel", on_delete=models.CASCADE
    )
    # doc_image_url = models.ImageField(upload_to=get_uplaod_file_name, null=True)
    # image_url = models.ImageField(upload_to=get_uplaod_file_name, null=True)
    # image_url = models.ImageField(upload_to=get_uplaod_file_name, null=True)
    # image_url = models.ImageField(upload_to=get_uplaod_file_name, null=True)
    # image_url = models.ImageField(upload_to=get_uplaod_file_name, null=True)
    verification_status = models.CharField(max_length=200, choices=VERIFICATION_STATUS_CHOICES, default="NOT_VERIFIED")
    payload = models.TextField(null=True, blank=True)
    nin_number = models.CharField(max_length=100, null=True, blank=True)
    verified_by = models.CharField(max_length=100, null=True, blank=True)
    resource_id = models.CharField(max_length=200, null=True, blank=True)
    resource_url = models.TextField(null=True, blank=True)
    date_added = models.DateTimeField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    channel = models.CharField(max_length=150, null=True, blank=True)
    id_type = models.CharField(max_length=100, null=True, blank=True)
    id_sub_type = models.CharField(max_length=100, null=True, blank=True)
    selfie_url = models.TextField(null=True, blank=True)
    sprite_url = models.TextField(null=True, blank=True)
    video_url = models.TextField(null=True, blank=True)
    front_view_url = models.TextField(null=True, blank=True)
    back_view_url = models.TextField(null=True, blank=True)
    resource_retrieved = models.BooleanField(default=False)
    kyc_verf_payload = models.TextField(null=True, blank=True)
    kyc_verf_response = models.TextField(null=True, blank=True)


    def save(self, *args, **kwargs):
        if self.is_verified:
            self.verification_status = "SUCCESSFUL"
            # self.kyc.user.kyc_two_progress = self.verification_status
        else:
            self.verification_status = "NOT_VERIFIED"
            # self.kyc.user.kyc_two_progress = self.verification_status

        self.kyc.user.save()

        super(DocumentFaceMatchKYC2Detail, self).save(*args, **kwargs)


def get_verf_id():
    return f"LP-GUA-{datetime.now().timestamp()}-{uuid.uuid4()}"


class GuarantorDetail(models.Model):
    SUCCESSFUL = "SUCCESSFUL"
    PENDING = "PENDING"
    NOT_VERIFIED = "NOT_VERIFIED"
    NOT_STARTED = "NOT_STARTED"
    REJECTED = "REJECTED"

    VERIFICATION_STATUS_CHOICES = [
        (SUCCESSFUL, "SUCCESSFUL"),
        (PENDING, "PENDING"),
        (NOT_VERIFIED, "NOT_VERIFIED"),
        (NOT_STARTED, "NOT_STARTED"),
        (REJECTED, "REJECTED")
    ]

    kyc = models.OneToOneField(
        KYCTable, related_name="guarantor_rel", on_delete=models.SET_NULL, blank=True, null=True
    )
    kyc_extra = models.OneToOneField(
        KYCTable, related_name="guarantor_rel_extra", on_delete=models.SET_NULL, blank=True, null=True
    )
    face_image = models.ImageField(upload_to="guarantor-images", blank=True, null=True, help_text="Guarantor's Face Image/Passport")
    utility_bill = models.ImageField(upload_to="guarantor-images", blank=True, null=True, help_text="Guarantor's Utility Bill, not more than 6months old")
    work_id_card = models.ImageField(upload_to="guarantor-images", blank=True, null=True, help_text="Guarantor's Workplace ID card")
    nin = models.ImageField(upload_to="guarantor-images", blank=True, null=True, help_text="Guarantor's NIN")

    verification_status = models.CharField(max_length=200, choices=VERIFICATION_STATUS_CHOICES, default="NOT_STARTED")
    verification_unique_id = models.CharField(max_length=150, default=get_verf_id)
    guarantor_name = models.CharField(max_length=150, null=True, blank=True)
    guarantor_phone_number = models.CharField(max_length=150, null=True, blank=True)
    guarantor_email = models.CharField(max_length=150, null=True, blank=True)
    guarantor_occupation = models.CharField(max_length=150, null=True, blank=True)
    guarantor_address = models.CharField(max_length=150, null=True, blank=True)
    guarantor_verified_first_name = models.CharField(max_length=150, null=True, blank=True)
    guarantor_verified_last_name = models.CharField(max_length=150, null=True, blank=True)
    guarantor_verified_address = models.CharField(max_length=350, null=True, blank=True)
    guarantor_id_type = models.CharField(max_length=350, null=True, blank=True)
    guarantor_id_number = models.CharField(max_length=350, null=True, blank=True)
    guarantor_id_is_verified = models.BooleanField(default=False)
    guarantor_id_verification_payload = models.TextField(null=True, blank=True)
    next_of_kin_name = models.CharField(max_length=150, null=True, blank=True)
    next_of_kin_relationship = models.CharField(max_length=150, null=True, blank=True)
    next_of_kin_phone_number = models.CharField(max_length=150, null=True, blank=True)
    next_of_kin_address = models.CharField(max_length=150, null=True, blank=True)
    payload = models.TextField(null=True, blank=True)
    awaiting_verification = models.BooleanField(default=False)
    email_sms_sent = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    verf_started = models.BooleanField(default=False)
    is_lotto_user = models.BooleanField(default=False)
    initial_payload = models.TextField(null=True)
    back_to_lotto_response = models.TextField(null=True)
    date_added = models.DateTimeField(auto_now_add=True)
    date_started = models.DateTimeField(null=True, blank=True)
    date_verified = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if self.is_verified:
            self.verification_status = "SUCCESSFUL"
            if self.kyc:
                self.kyc.user.kyc_three_progress = "SUCCESSFUL"
            if self.kyc_extra:
                self.kyc_extra.user.kyc_three_progress = "SUCCESSFUL"
            # self.kyc.user.kyc_three_progress = self.verification_status
        else:
            self.verification_status = "NOT_VERIFIED"
            # self.kyc.user.kyc_three_progress = self.verification_status

        # if self.verification_unique_id is None or self.verification_unique_id == "" or self.verification_unique_id == " ":
        #     self.verification_unique_id = f"LP-GUA-{uuid.uuid4()}"

        if self.kyc and self.kyc.user.type_of_user == "LOTTO_AGENT":
            self.is_lotto_user = True
        if self.kyc_extra and self.kyc_extra.user.type_of_user == "LOTTO_AGENT":
            self.is_lotto_user = True

        super(GuarantorDetail, self).save(*args, **kwargs)


class MetamapRawData(models.Model):
    METAMAP = "METAMAP"
    DOJAH = "DOJAH"

    VERIFICATION_SOURCE_CHOICES = (
        (METAMAP, "METAMAP"),
        (DOJAH, "DOJAH")
    )

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    provider = models.CharField(max_length=100, null=True, blank=True)
    verification_type = models.CharField(max_length=200, null=True, blank=True)
    payload = models.TextField()
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)
    source = models.CharField(max_length=100, choices=VERIFICATION_SOURCE_CHOICES, blank=True, null=True)


class BlacklistedBVN(models.Model):
    bvn_number = models.CharField(max_length=100)
    bvn_names = models.CharField(max_length=100, null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.bvn_number

    def clean(self):
        if BlacklistedBVN.objects.filter(bvn_number=self.bvn_number).exists():
            raise ValidationError(f'This BVN is already in Blacklist list')

        # class OtherBVNDetail(models.Model):

#     VERIFICATION_TYPE_CHOICES = [
#         ("GUARANTOR", "GUARANTOR"),
#         ("OTHERS", "OTHERS"),
#         ("AJO", "AJO"),
#     ]
#     verified_for = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
#     verification_type = models.CharField(max_length=150, choices=VERIFICATION_TYPE_CHOICES)
#     verified_by = models.CharField(max_length=100, null=True, blank=True)
#     bvn_number = models.CharField(max_length=100)
#     times_used_for_acct = models.IntegerField(default=0)
#     verification_id = models.CharField(max_length=300, null=True, blank=True)
#     bvn_phone_number = models.CharField(max_length=100, null=True, blank=True)
#     bvn_phone_number_2 = models.CharField(max_length=100, null=True, blank=True)
#     bvn_first_name = models.CharField(max_length=250, null=True, blank=True)
#     bvn_middle_name = models.CharField(max_length=250, null=True, blank=True)
#     bvn_last_name = models.CharField(max_length=250, null=True, blank=True)
#     bvn_gender = models.CharField(max_length=250, null=True, blank=True)
#     bvn_birthdate = models.CharField(max_length=250, null=True, blank=True)
#     is_verified = models.BooleanField(default=False)
#     channel = models.CharField(max_length=150, null=True, blank=True)
#     failure_reason = models.CharField(max_length=1000, null=True, blank=True)
#     payload = models.TextField(null=True, blank=True)
#     bvn_image_url = models.TextField(null=True, blank=True)


#     def __str__(self) -> str:
#         return f"{self.verified_for.email}-{self.bvn_number}"

#     @classmethod
#     def create_other_bvn(cls, verified_for, verification_type, bvn_number):
#         get_user = User.objects.filter(bvn_number=bvn_number).
#         get_bvn = cls.objects.filter(bvn_number).first()
#         if not get_bvn:
#             get_bvn = cls.objects.create(
#                 verified_for = verified_for,
#                 verification_type = verification_type,
#                 bvn_number = bvn_number
#             )

#         return get_bvn.times_used_for_acct


def validate_base64_image(value):
    """Validate that the string is a base64 encoded image"""
    if value:
        if not value.startswith('data:image'):
            raise ValidationError('Invalid base64 image format')

class UserImage(models.Model):
    user = models.ForeignKey(
        'main.User',
        on_delete=models.CASCADE,
        related_name='user_images'
    )
    bvn_number = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        help_text="User's BVN number"
    )
    snapshot = models.TextField(
        null=True,
        blank=True,
        help_text="Base64 encoded snapshot image",
        validators=[validate_base64_image]
    )
    bvn_photo = models.TextField(
        null=True,
        blank=True,
        help_text="Base64 encoded BVN photo"
    )
    app_photo = models.TextField(
        null=True,
        blank=True,
        help_text="Base64 encoded photo captured from app"
    )
    bvn_payload = models.JSONField(
        null=True,
        blank=True,
        help_text="Full BVN verification payload"
    )
    verification_response = models.TextField(
        null=True,
        blank=True,
        help_text="Response from verification service"
    )
    is_match = models.BooleanField(
        default=False,
        help_text="Indicates if the photos match"
    )
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Image Verification for {self.user.email}, Match: {self.is_match}"

    class Meta:
        ordering = ['-date_created']

    def get_bvn_photo_from_details(self):
        """Extract BVN photo from BVNDetail payload"""
        try:
            kyc = KYCTable.objects.get(user=self.user)
            bvn_detail = kyc.bvn_rel
            
            if not bvn_detail.payload:
                raise ValueError("No BVN payload available")
            
            payload = json.loads(bvn_detail.payload)
            data = payload.get('data', {}).get('data', {})
            return data.get('photo') or data.get('image')
        except (KYCTable.DoesNotExist, json.JSONDecodeError) as e:
            raise ValueError(f"Error getting BVN photo: {str(e)}")

    def verify_user_image(self, payload=None):
        try:
            # Use provided payload or fetch from existing KYC
            if payload is None:
                kyc = KYCTable.objects.get(user=self.user)
                bvn_detail = kyc.bvn_rel

                if not bvn_detail.payload:
                    raise ValueError("No BVN payload available")

                try:
                    payload = json.loads(bvn_detail.payload)
                except json.JSONDecodeError:
                    raise ValueError("Invalid BVN payload format")

            # Validate payload structure
            try:
                bvn_photo = None
                bvn_number = ""
                data = payload.get('data')
                if "image" or "photo" in data:
                    bvn_photo = data.get('image') or data.get('photo')
                    bvn_number = data.get('idNumber') if data.get('idNumber') else data.get('bvn')
                if "data" in data:
                    bvn_photo = data.get("data").get('image') or data.get("data").get('photo')
                    bvn_number = data['data']['idNumber'] if data['data']['idNumber'] else data['data']['bvn']
                if "entity" in data:
                    bvn_photo = data.get("entity").get('image') or data.get("entity").get('photo')
                    bvn_number = data['entity']['bvn'] if data['entity']['bvn'] else data['entity']['idNumber']

                if not bvn_photo:
                    raise ValueError("BVN photo not found in payload")
                if bvn_number:
                    self.bvn_number = bvn_number

                self.bvn_payload = payload
                self.save()
            except (KeyError, TypeError, ValueError) as e:
                raise ValueError("BVN photo or details not found in payload") from e

            self.bvn_photo = bvn_photo

            if not self.bvn_photo or not self.app_photo:
                raise ValueError("Both BVN photo and app photo are required.")

            bvn_bytes = decode_and_validate_image(self.bvn_photo)
            app_bytes = decode_and_validate_image(self.app_photo)

            # Rekognition client
            rekognition_client = boto3.client(
                "rekognition",
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_REGION,
            )

            # Compare faces
            rekognition_response = rekognition_client.compare_faces(
                SourceImage={"Bytes": bvn_bytes},
                TargetImage={"Bytes": app_bytes},
                SimilarityThreshold=60,
            )

            # Process results
            verification_result = transform_rekognition_response(rekognition_response)
            self.is_match = verification_result['is_match']
            self.verification_response = str(verification_result)
            self.save()

            # Get or create user_image for sub accounts
            from main.models import UserOtherAccount
            other_accounts = UserOtherAccount.objects.filter(owner=self.user)
            for account in other_accounts:
                UserImage.objects.get_or_create(
                    user=account.other_account,
                    defaults={
                        "bvn_number": self.bvn_number,
                        "snapshot": self.snapshot,
                        "bvn_photo": self.bvn_photo,
                        "app_photo": self.app_photo,
                        "bvn_payload": self.bvn_payload,
                        "verification_response": self.verification_response,
                        "is_match": self.is_match
                    }
                )

            return verification_result['is_match']

        except KYCTable.DoesNotExist:
            self.is_match = False
            self.verification_response = "KYC record not found"
            self.save()
            raise ValueError("KYC record not found for the user")

        except Exception as e:
            self.is_match = False
            self.verification_response = f"Error: {e}"
            self.save()
            raise ValueError(f"Image verification failed: {e}")


class UpdateKYC(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)

    # KYC 1
    bvn = models.CharField(max_length=50, blank=True, null=True)
    bvn_image = models.ImageField(upload_to="kyc-images", blank=True, null=True, help_text="BVN image from KYC Level One", editable=False)
    phone_bvn_match = models.BooleanField(default=False)
    kyc_one_verified_by = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL, related_name="kyc_one_verified_by")
    kyc_one_completed = models.BooleanField(default=False)

    # KYC 2
    nin = models.CharField(max_length=50, blank=True, null=True)
    nin_image = models.ImageField(upload_to="kyc-images", blank=True, null=True, help_text="Image received from NIN check", editable=False)
    first_selfie = models.ImageField(upload_to="kyc-images", blank=True, null=True, help_text="Upload the image with eye-open here")
    second_selfie = models.ImageField(upload_to="kyc-images", blank=True, null=True, help_text="Upload the image with eye-closed here")
    kyc_two_verified_by = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL, related_name="kyc_two_verified_by")
    kyc_two_completed = models.BooleanField(default=False)

    # KYC 3
    guarantor_name = models.CharField(max_length=100, blank=True, null=True)
    guarantor_phone_number = models.CharField(max_length=20, blank=True, null=True)
    guarantor_email = models.EmailField(blank=True, null=True)
    guarantor_address = models.CharField(max_length=100, blank=True, null=True)
    guarantor_id_number = models.CharField(max_length=100, blank=True, null=True)
    kyc_three_verified_by = models.ForeignKey(User, blank=True, null=True, on_delete=models.SET_NULL, related_name="kyc_three_verified_by")
    kyc_three_completed = models.BooleanField(default=False)

    created_on = models.DateTimeField(auto_now_add=True)
    updated_on = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.user.email)






