from django.contrib.auth import get_user_model
from django.db.models import Q
from django.conf import settings

from kyc_app.helpers.helper_functions import verify_nin_with_uverify, verify_nin_with_dojah
from liberty_pay.exceptions import InvalidRequestException
from main.models import ONBOARDING_STATUS, B<PERSON><PERSON>atchlist
from main.helper.logging_utils import log_info, log_error, log_warning, log_debug
from rest_framework.exceptions import ValidationError
from rest_framework import serializers
from kyc_app.models import KYCTable, UserImage, GuarantorDetail, BVNDetail, DocumentFaceMatchKYC2Detail
from kyc_app.tasks import send_guarantor_detail_to_admin_email, send_email_to_guarantor

User = get_user_model()


######################################################################################
# BVN

TYPE_OF_OTP_CHOICE = (
    ("TEXT", "TEXT"),
    ("VOICE", "VOICE"),
    ("WHATSAPP", "WHATSAPP"),
)


class NoneBVNSerializer(serializers.Serializer):
    bvn_number = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)


class BVNSerializer(serializers.Serializer):
    otp_type = serializers.ChoiceField(required=False, choices=TYPE_OF_OTP_CHOICE, allow_null=True, allow_blank=True)
    bvn_number = serializers.CharField()
    phone_number = serializers.CharField(required=False)
    onboarding_status = serializers.ChoiceField(required=False, allow_blank=True, allow_null=True, choices=ONBOARDING_STATUS)


class GuarantorDetailSerializer(serializers.Serializer):
    guarantor_name = serializers.CharField(required=True)
    guarantor_phone_number = serializers.CharField(required=True)
    guarantor_email = serializers.EmailField(required=True)
    guarantor_occupation = serializers.CharField(required=True)
    guarantor_address = serializers.CharField(required=True)
    next_of_kin_name = serializers.CharField(required=True)
    next_of_kin_relationship = serializers.CharField(required=True)
    next_of_kin_phone_number = serializers.CharField(required=True)
    next_of_kin_address = serializers.CharField(required=True)

    def is_valid(self, raise_exception=False):
        if self._kwargs["data"]["guarantor_phone_number"].isnumeric() or self._kwargs["data"]["next_of_kin_phone_number"].isnumeric():
            pass
        else:
            raise serializers.ValidationError(
                {"error_code": "14", "message": "You must supply an integer for guarantor phone_number and next of kin phone number"}
            )
        return super().is_valid(raise_exception)


class GuarantorLottoVerfSerializer(serializers.Serializer):
    VERF_STAT_CHOICE = (
        ("SUCCESSFUL", "SUCCESSFUL"),
        ("REJECTED", "REJECTED"),
    )

    verification_unique_id = serializers.CharField(required=True)
    verification_status = serializers.ChoiceField(required=True, choices=VERF_STAT_CHOICE)
    guarantor_verified_first_name = serializers.CharField(required=True)
    guarantor_verified_last_name = serializers.CharField(required=True)
    guarantor_verified_address = serializers.CharField(required=True)
    guarantor_id_type = serializers.CharField(required=True)
    guarantor_id_number = serializers.CharField(required=True)
    guarantor_id_is_verified = serializers.BooleanField(required=True)
    guarantor_id_verification_payload = serializers.JSONField(required=True)
    # is_verified = serializers.BooleanField(required=True)


class GuarantorWebLinkVerfSerializer(serializers.Serializer):
    verification_unique_id = serializers.CharField(required=True)
    bvn_number = serializers.CharField(required=True)
    # verification_status = serializers.ChoiceField(required=True, choices=VERF_STAT_CHOICE)
    guarantor_verified_first_name = serializers.CharField(required=True)
    guarantor_verified_last_name = serializers.CharField(required=True)
    guarantor_verified_address = serializers.CharField(required=True)
    guarantor_id_type = serializers.CharField(required=True)
    guarantor_id_number = serializers.CharField(required=True)
    guarantor_id_is_verified = serializers.BooleanField(required=True)
    guarantor_id_verification_payload = serializers.JSONField(required=True)


class GuarantorDetailAdminVerificationSerializerIn(serializers.Serializer):
    guarantor_first_name = serializers.CharField()
    guarantor_last_name = serializers.CharField()
    guarantor_address = serializers.CharField()
    guarantor_state = serializers.CharField()
    guarantor_city = serializers.CharField()
    nin_number = serializers.IntegerField()
    face_image = serializers.ImageField(required=False, allow_null=True)
    utility_bill = serializers.ImageField(required=False, allow_null=True)
    work_id_card = serializers.ImageField(required=False, allow_null=True)
    nin = serializers.ImageField(required=False, allow_null=True)

    def _normalize_name(self, name):
        """Normalize name for comparison by removing extra whitespace and converting to uppercase."""
        if not name:
            return ""
        return str(name).strip().upper()

    def _validate_name_matching(self, provided_first_name, provided_last_name,
                               nin_first_name, nin_last_name):
        """
        Enhanced name matching logic that validates if ANY of the following conditions are met:
        - NIN first name matches provided first name
        - NIN last name matches provided last name
        - NIN first name matches provided last name
        - NIN last name matches provided first name

        Returns: (is_valid: bool, match_details: str)
        """
        # Normalize all names for comparison
        p_first = self._normalize_name(provided_first_name)
        p_last = self._normalize_name(provided_last_name)
        n_first = self._normalize_name(nin_first_name)
        n_last = self._normalize_name(nin_last_name)

        log_debug(
            f"Name matching validation - Provided: '{p_first}' '{p_last}', NIN: '{n_first}' '{n_last}'",
            "GUARANTOR_NAME_VALIDATION",
            {
                'provided_first': provided_first_name,
                'provided_last': provided_last_name,
                'nin_first': nin_first_name,
                'nin_last': nin_last_name
            }
        )

        # Check if any names are empty
        if not all([p_first, p_last, n_first, n_last]):
            missing_fields = []
            if not p_first: missing_fields.append("provided_first_name")
            if not p_last: missing_fields.append("provided_last_name")
            if not n_first: missing_fields.append("nin_first_name")
            if not n_last: missing_fields.append("nin_last_name")

            log_warning(
                f"Missing name fields for validation: {missing_fields}",
                "GUARANTOR_NAME_VALIDATION"
            )
            return False, f"Missing required name fields: {', '.join(missing_fields)}"

        # Perform all possible matching combinations
        matches = []

        # Direct matches
        if n_first == p_first:
            matches.append("NIN first name matches provided first name")

        if n_last == p_last:
            matches.append("NIN last name matches provided last name")

        # Cross matches (handles cases where names might be swapped)
        if n_first == p_last:
            matches.append("NIN first name matches provided last name")

        if n_last == p_first:
            matches.append("NIN last name matches provided first name")

        # Return validation result
        if matches:
            match_details = "; ".join(matches)
            log_info(
                f"Name validation successful: {match_details}",
                "GUARANTOR_NAME_VALIDATION",
                {
                    'provided_names': f"{provided_first_name} {provided_last_name}",
                    'nin_names': f"{nin_first_name} {nin_last_name}",
                    'matches': matches
                }
            )
            return True, match_details
        else:
            error_msg = f"No name matches found. Provided: '{provided_first_name} {provided_last_name}', NIN: '{nin_first_name} {nin_last_name}'"
            log_warning(error_msg, "GUARANTOR_NAME_VALIDATION")
            return False, error_msg

    def update(self, instance, validated_data):
        first_name = str(validated_data.get("guarantor_first_name")).capitalize()
        last_name = str(validated_data.get("guarantor_last_name")).capitalize()
        address = str(validated_data.get("guarantor_address", ""))
        state = str(validated_data.get("guarantor_state", "")).capitalize()
        city = str(validated_data.get("guarantor_city", "")).capitalize()
        image = validated_data.get("face_image")
        utils_bill = validated_data.get("utility_bill")
        work_id = validated_data.get("work_id_card")
        nin = validated_data.get("nin")
        nin_number = validated_data.get("nin_number")

        # if not any([work_id, nin]):
        #     raise InvalidRequestException({"detail": "Please upload either your Workplace ID Card, or NIN"})

        if instance.kyc_extra:
            user = instance.kyc_extra.user
        else:
            user = instance.kyc.user

        if not user:
            raise InvalidRequestException({"detail": "User not found"})

        user_first_name = str(user.first_name).capitalize()
        user_last_name = str(user.last_name).capitalize()
        email = str(user.email).lower()
        guarantor_email = str(instance.guarantor_email).lower()

        instance.guarantor_address = address + city + state
        instance.face_image = image
        instance.utility_bill = utils_bill
        instance.work_id_card = work_id
        instance.nin = nin
        instance.save()

        # send_guarantor_detail_to_admin_email.delay(
        #     first_name, last_name, address, state, city, user_first_name, user_last_name, email, guarantor_email
        # )
        if user.type_of_user in ["STAFF_AGENT", "DMO_AGENT"]:
            try:
                send_guarantor_detail_to_admin_email(
                    first_name, last_name, address, state, city, user_first_name, user_last_name, email, guarantor_email
                )
            except Exception as err:
                log_error(str(err), "GUARANTOR_EMAIL_SEND_ERROR", include_traceback=True)
        else:
            try:
                # Validate user via uVerify and Dojah
                log_info(
                    f"Starting NIN verification for guarantor",
                    "GUARANTOR_NIN_VERIFICATION_START",
                    {
                        'nin_number': str(nin_number),
                        'provided_names': f"{first_name} {last_name}",
                        'user_email': user.email
                    }
                )

                nin_verified_first_name = settings.GUARANTOR_TEST_FIRST_NAME
                nin_verified_last_name = settings.GUARANTOR_TEST_LAST_NAME
                nin_verified_phone_number = ""
                nin_verified_email = ""
                request_passed = False
                response = verify_nin_with_uverify(nin_number)
                if response["success"] is True and response["data"]["success"] is True:
                    if "status" in response["data"]["data"] and response["data"]["data"]["status"] == "found":
                        nin_verified_first_name = response["data"]["data"]["firstName"]
                        nin_verified_last_name = response["data"]["data"]["lastName"]
                        nin_verified_phone_number = response["data"]["data"]["mobile"]
                        nin_verified_email = response["data"]["data"]["email"]
                        request_passed = True
                        log_info(
                            "NIN verification successful with uVerify",
                            "GUARANTOR_NIN_VERIFICATION_SUCCESS",
                            {
                                'nin_number': str(nin_number),
                                'verified_names': f"{nin_verified_first_name} {nin_verified_last_name}",
                                'provider': 'uVerify'
                            }
                        )
                else:
                    log_info("uVerify failed, trying Dojah", "GUARANTOR_NIN_VERIFICATION_FALLBACK")
                    response = verify_nin_with_dojah(nin_number)
                    if response["success"] is True:
                        if response["data"] and "entity" in response["data"]:
                            data = response["data"]["entity"]
                            nin_verified_first_name = data["first_name"]
                            nin_verified_last_name = data["last_name"]
                            request_passed = True
                            log_info(
                                "NIN verification successful with Dojah",
                                "GUARANTOR_NIN_VERIFICATION_SUCCESS",
                                {
                                    'nin_number': str(nin_number),
                                    'verified_names': f"{nin_verified_first_name} {nin_verified_last_name}",
                                    'provider': 'Dojah'
                                }
                            )
                # Use enhanced name matching validation
                name_validation_passed = False
                validation_details = ""

                if request_passed:
                    name_validation_passed, validation_details = self._validate_name_matching(
                        first_name, last_name, nin_verified_first_name, nin_verified_last_name
                    )

                if request_passed and name_validation_passed:
                    instance.guarantor_verified_first_name = nin_verified_first_name
                    instance.guarantor_verified_last_name = nin_verified_last_name
                    if nin_verified_email:
                        instance.guarantor_email = nin_verified_email
                    instance.guarantor_id_is_verified = True
                    if nin_verified_phone_number:
                        instance.guarantor_phone_number = nin_verified_phone_number
                    instance.guarantor_name = str(nin_verified_first_name) + " " + str(nin_verified_last_name)
                    instance.guarantor_id_number = nin_number
                    instance.guarantor_id_verification_payload = response
                    instance.is_verified = True
                    instance.verification_status = 'SUCCESSFUL'
                    instance.save()

                    log_info(
                        "Guarantor verification completed successfully",
                        "GUARANTOR_VERIFICATION_COMPLETE",
                        {
                            'nin_number': str(nin_number),
                            'provided_names': f"{first_name} {last_name}",
                            'verified_names': f"{nin_verified_first_name} {nin_verified_last_name}",
                            'validation_details': validation_details,
                            'user_email': user.email,
                            'guarantor_email': instance.guarantor_email
                        }
                    )
                else:
                    error_msg = "Name validation failed"
                    if not request_passed:
                        error_msg = "NIN verification failed with both uVerify and Dojah"
                    else:
                        error_msg = f"Name validation failed: {validation_details}"

                    log_warning(
                        error_msg,
                        "GUARANTOR_VERIFICATION_FAILED",
                        {
                            'nin_number': nin_number,
                            'provided_names': f"{first_name} {last_name}",
                            'nin_names': f"{nin_verified_first_name} {nin_verified_last_name}" if request_passed else "N/A",
                            'request_passed': request_passed,
                            'validation_details': validation_details
                        }
                    )
                    raise InvalidRequestException({"detail": error_msg})
            except Exception as err:
                log_error(
                    str(err),
                    "GUARANTOR_VERIFICATION_EXCEPTION",
                    {
                        'nin_number': nin_number,
                        'provided_names': f"{first_name} {last_name}",
                        'user_email': user.email if user else "unknown"
                    },
                    include_traceback=True
                )
                raise InvalidRequestException({"detail": "Verification failed due to system error"})

        return {"detail": "Guarantor form submitted successfully"}


class UserImageSerializer(serializers.ModelSerializer):
    """
    Serializer for handling user image verification data
    """
    class Meta:
        model = UserImage
        fields = [
            'user', 'app_photo', 'bvn_number',
        ]
        read_only_fields = ['date_created', 'date_updated', 'is_match']

    def validate(self, data):
        """
        Validate that photo is provided
        """
        if not data.get('app_photo'):
            raise serializers.ValidationError("App photo is required")
        return data


class UserImageResponseSerializer(serializers.ModelSerializer):
    """
    Serializer for returning user image verification data
    """
    user_email = serializers.EmailField(source='user.email', read_only=True)

    class Meta:
        model = UserImage
        fields = [
            'user_email',
            'is_match',
            'verification_response',
            'date_created'
        ]


class GuarantorInformationSerializerIn(serializers.Serializer):
    name = serializers.CharField()
    phone_number = serializers.CharField(min_length=11, max_length=13)
    email = serializers.EmailField()
    occupation = serializers.CharField()
    address = serializers.CharField()

    def validate(self, attrs):
        phone_number = attrs.get("phone_number")
        email = attrs.get("email")
        if User.objects.filter(type_of_user="STAFF_AGENT", email=email).exists():
            raise InvalidRequestException({"message": "Provided email cannot be used as a guarantor, as the user is a staff agent"})
        if not str(phone_number).isnumeric():
            raise InvalidRequestException({"message": "Guarantor's phone number can only be numeric"})
        if len(phone_number) > 11 and not str(phone_number).startswith("234"):
            raise InvalidRequestException({"message": "Guarantor's phone number is not valid, please check and try again"})
        return attrs


class NextOfKinSerializerIn(serializers.Serializer):
    next_of_kin_name = serializers.CharField()
    next_of_kin_relationship = serializers.CharField()
    next_of_kin_phone_number = serializers.CharField(min_length=11, max_length=13)
    next_of_kin_address = serializers.CharField()

    def validate(self, attrs):
        phone_number = attrs.get("next_of_kin_phone_number")
        if not str(phone_number).isnumeric():
            raise InvalidRequestException({"message": "Next of kin's phone number can only be numeric"})
        if len(phone_number) > 11 and not str(phone_number).startswith("234"):
            raise InvalidRequestException({"message": "Next of kin's phone number is not valid, please check and try again"})
        return attrs


class NewGuarantorFormSerializerIn(NextOfKinSerializerIn):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    guarantors = serializers.ListField(child=GuarantorInformationSerializerIn())

    def validate(self, attrs):
        user = attrs.get("user")
        guarantors = attrs.get("guarantors")
        if user.kyc_level == 3 or user.get_kyc_level == 3:
            raise InvalidRequestException({"message": "KYC Level 3 already completed"})
        emails = list()
        for guarantor in guarantors:
            email = guarantor.get("email")
            if email in emails:
                raise InvalidRequestException({"message": f"Multiple guarantor cannot share the same email: {email}"})
            emails.append(email)
            if user.email == email:
                raise InvalidRequestException({"message": "Your email is same as one provided for a guarantor"})
            # query = Q(kyc__user=user) | Q(kyc_extra__user=user)
            # if GuarantorDetail.objects.filter(query).exists():
            #     raise InvalidRequestException({"message": "Guarantor(s) already provided and under review"})

        return attrs

    def create(self, validated_data):
        user = validated_data.get("user")
        guarantors = validated_data.get("guarantors")
        nok_name = validated_data.get("next_of_kin_name")
        nok_relationship = validated_data.get("next_of_kin_relationship")
        nok_phone_number = validated_data.get("next_of_kin_phone_number")
        nok_address = validated_data.get("next_of_kin_address")

        if user.type_of_user == "STAFF_AGENT" and len(guarantors) < 2:
            raise InvalidRequestException({"message": "You need to provide two(2) guarantors as a Staff Agent"})

        if len(guarantors) > 2:
            raise InvalidRequestException({"message": "Only maximum of two(2) guarantor is needed at this time"})

        iterator = 0
        for guarantor in guarantors:
            name = guarantor.get("name")
            phone = guarantor.get("phone_number")
            email = guarantor.get("email")
            work_occupation = guarantor.get("occupation")
            address = guarantor.get("address")

            guarantor_detail, _ = GuarantorDetail.objects.get_or_create(kyc=user.check_kyc)
            guarantor_detail.guarantor_email = email
            guarantor_detail.guarantor_name = name
            guarantor_detail.guarantor_phone_number = phone
            guarantor_detail.guarantor_occupation = work_occupation
            guarantor_detail.guarantor_address = address
            guarantor_detail.next_of_kin_name = nok_name
            guarantor_detail.next_of_kin_address = nok_address
            guarantor_detail.next_of_kin_relationship = nok_relationship
            guarantor_detail.next_of_kin_phone_number = nok_phone_number
            guarantor_detail.save()

            if iterator > 0:
                second_guarantor_detail, _ = GuarantorDetail.objects.get_or_create(kyc_extra=user.check_kyc)
                second_guarantor_detail.guarantor_email = email
                second_guarantor_detail.guarantor_name = name
                second_guarantor_detail.guarantor_phone_number = phone
                second_guarantor_detail.guarantor_occupation = work_occupation
                second_guarantor_detail.guarantor_address = address
                second_guarantor_detail.next_of_kin_name = nok_name
                second_guarantor_detail.next_of_kin_address = nok_address
                second_guarantor_detail.next_of_kin_relationship = nok_relationship
                second_guarantor_detail.next_of_kin_phone_number = nok_phone_number
                second_guarantor_detail.save()

            iterator += 1
            # Send email to guarantor
            try:
                send_email_to_guarantor(guarantor_detail.id)
            except Exception as err:
                log_info(str(err))
            # send_email_to_guarantor.delay(str(guarantor_detail.id))

        return {"message": "Guarantor and Next of Kin information submitted successfully"}


class BVNPhoneNumberSerializerIn(serializers.Serializer):
    bvn_number = serializers.CharField(max_length=11)

    def validate(self, attrs):
        bvn = attrs.get("bvn_number")
        if not str(bvn).isnumeric():
            raise InvalidRequestException({"message": "BVN Number must be numeric"})
        # Confirm BVN is not watchlisted
        if BVNWatchlist.is_bvn_watchlisted(bvn):
            raise InvalidRequestException({"message": "BVN is watchlisted. Please contact admin."})

        return attrs

    def create(self, validated_data):
        bvn_number = validated_data.get("bvn_number")
        # Verify BVN
        response = BVNDetail.verify_bvn(bvn_number=bvn_number, user=None, last_name="BVN", get_masked_phone=True)
        if "status" in response and response["status"] is True:
            bvn_digit = response.get("bvn_phone_number")
            return {"message": "Phone number found", "phone_number": bvn_digit}
        else:
            raise InvalidRequestException({"message": "Phone number for provided BVN not found", "phone_number": ""})


class VerificationCallBackSerializerIn(serializers.Serializer):
    bvn_number = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    first_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    last_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    middle_name = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    mobile = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    date_of_birth = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    gender = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    nin = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    country = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    selfie_image = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    bvn_verified = serializers.BooleanField(required=False)
    bvn_image_verified = serializers.BooleanField(required=False)
    email = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    def create(self, validated_data):
        bvn_number = validated_data.get("bvn_number")
        first_name = validated_data.get("first_name")
        last_name = validated_data.get("last_name")
        middle_name = validated_data.get("middle_name")
        mobile = validated_data.get("mobile")
        date_of_birth = validated_data.get("date_of_birth")
        gender = validated_data.get("gender")
        nin = validated_data.get("nin")
        country = validated_data.get("country")
        selfie_image = validated_data.get("selfie_image")
        bvn_verified = validated_data.get("bvn_verified")
        bvn_image_verified = validated_data.get("bvn_image_verified")
        email = validated_data.get("email")
        user = None
        response = {"message": "Received, data updated"}

        # Get user
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            response = {"message": "User not found"}
        except User.MultipleObjectsReturned:
            response = {"message": f"Multiple user instance found for {email}"}

        if user:
            kyc = KYCTable.objects.get(user=user)
            bvn_rel, _ = BVNDetail.objects.get_or_create(kyc=kyc)
            doc_rel, _ = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=kyc)
            bvn_rel.kyc_verf_payload = doc_rel.kyc_verf_payload = validated_data

            if not bvn_rel.is_verified and bvn_verified is True:
                # Means KYC One is done
                bvn_rel.bvn_phone_number = mobile
                bvn_rel.bvn_number = bvn_number
                bvn_rel.bvn_first_name = first_name
                bvn_rel.bvn_last_name = last_name
                bvn_rel.bvn_middle_name = middle_name
                bvn_rel.bvn_birthdate = date_of_birth
                bvn_rel.bvn_nationality = country
                bvn_rel.bvn_gender = gender
                bvn_rel.verified_by = "Liberty KYC"
                bvn_rel.is_verified = True
                bvn_rel.verification_status = "SUCCESSFUL"
            if not doc_rel.is_verified and bvn_image_verified is True:
                # Means KYC Two is done
                doc_rel.selfie_url = selfie_image
                doc_rel.nin_number = nin
                doc_rel.is_verified = True
                doc_rel.verification_status = "SUCCESSFUL"
                doc_rel.verified_by = "Liberty KYC"
            bvn_rel.save()
            doc_rel.save()

            return response




