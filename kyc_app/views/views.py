import ast

from django.http import Http404
from django.shortcuts import get_object_or_404

from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser, FormParser

from kyc_app.models import BVNDetail, BlacklistedBVN, GuarantorDetail, KYCTable, CreateReferenceForVerification
from kyc_app.serializers import BVNSerializer, NoneBVNSerializer, GuarantorDetailSerializer, GuarantorLottoVerfSerializer, \
    GuarantorWebLinkVerfSerializer, GuarantorDetailAdminVerificationSerializerIn, NewGuarantorFormSerializerIn, BVNPhoneNumberSerializerIn, \
    VerificationCallBackSerializerIn
from kyc_app.models import MetamapRawData, DocumentFaceMatchKYC2Detail
from kyc_app.helpers.metamap_manager import MetaMap
from kyc_app.helpers.helper_functions import send_out_verification_status, verify_bvn_with_uverify, verify_bvn_with_dojah
from kyc_app.tasks import send_data_to_lotto_back_task_without_delay, send_email_to_guarantor
from liberty_pay.exceptions import raise_serializer_error_msg, InvalidRequestException

from main.models import User, Users_Otp, ConstantTable, BVNWatchlist, UserOtherAccount
from main.permissions import HasKYC, HasKYCLevelTwo, OTPVerified, HasTransactionPin, MetaMapViewWhitelist, OtherServiceOtherPermissions, \
    CustomIsAuthenticated, CheckIPAddresses, WhitelistPermission
from main.serializers import OTPVerificationSerializer
from main.helper.helper_function import custom_validate_email, suspend_watchlisted_bvn_user
from main.helper.api import ThirdPartyApis

from accounts.tasks import create_wallets_and_accounts_task

from datetime import datetime
import json

from kyc_app.serializers import UserImageSerializer, UserImageResponseSerializer
from kyc_app.models import UserImage

from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


class CreateVerificatioReferenceAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def get(self, request):
        user = request.user

        kyc_level = request.query_params.get('kyc_level')
        if kyc_level is None:
            response = {
                "error": "543",
                "message": "No kyc level number attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            get_int_id = int(kyc_level)
        except:
            response = {
                "error": "544",
                "message": "must supply integer for kyc level number"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


        if not get_int_id >= 0 and not get_int_id <= 3:
            response = {
                "error": "545",
                "message": "kyc level must be between 0 and 3"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)

        if user.kyc_level >= get_int_id:
            response = {
                "error": "546",
                "message": f"user is only on kyc level {get_int_id}"
            }
            return Response(response, status=status.HTTP_404_NOT_FOUND)


        # Get IP ADDRESS
        address = request.META.get('HTTP_X_FORWARDED_FOR')
        if address:
            ip_addr = address.split(',')[-1].strip()
        else:
            ip_addr = request.META.get('REMOTE_ADDR')


        get_reference = CreateReferenceForVerification.create_verf_ref(
            user = user,
            ip_addr = ip_addr,
            for_kyc_level = get_int_id
        )

        response = {
            "message": "verification reference generated successfully",
            "rrn": get_reference
        }

        return Response(response, status=status.HTTP_200_OK)



class VerifyBVNAPIViewForNoneUser(APIView):
    permission_classes = [OtherServiceOtherPermissions]

    serializer_class = NoneBVNSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            bvn_number = serializer.validated_data["bvn_number"]
            last_name = serializer.validated_data["last_name"]

            if BVNWatchlist.is_bvn_watchlisted(bvn=bvn_number):
                response = {
                    "error": "044",
                    "message": "This BVN is watchlisted. Please contact admin"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            bvn_resp = BVNDetail.verify_non_user_bvn(bvn_number=bvn_number, last_name=last_name)


            # if bvn_resp is None:
            if bvn_resp.get("status") == True and bvn_resp.get("bvn_phone_number"):
                bvn_phone_number = bvn_resp.get("bvn_phone_number")
                first_covered = bvn_phone_number[0:4]
                second_covered = bvn_phone_number[-3:]
                total_covered = first_covered + "****" + second_covered
                # send_whisper_otp = send_bvn_otp_task.delay(
                #     formatted_bvn_phone_number=User.format_number_from_back_add_234(
                #         bvn_phone_number
                #     ),
                #     app_name="Liberty Agency BVN OTP",
                # )

                # send_whisper_otp = Users_Otp.send_new_otp(
                #     phone_number=User.format_number_from_back_add_234(
                #         bvn_phone_number
                #     ),
                #     app_name="Liberty Agency BVN OTP",
                # )

                response = {
                    # "message": f"An OTP code has been sent to your phone number linked to your BVN {total_covered}",
                    "data": bvn_resp.get("data")
                }
                return Response(response, status=status.HTTP_200_OK)

            else:
                response = {
                    "error": "23",
                    "message": "Could not verify BVN",
                    "data": bvn_resp,
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VerifyBVNAPIView(APIView):

    serializer_class = BVNSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            otp_type = serializer.validated_data.get("otp_type")
            bvn_number = serializer.validated_data["bvn_number"]
            phone_number = User.format_number_from_back_add_234(
                serializer.validated_data["phone_number"]
            )
            onboarding_status = serializer.validated_data.get("onboarding_status", None)

            if ConstantTable.get_constant_table_instance().allow_many_bvn == False:

                get_bvn_exist = BVNDetail.objects.filter(bvn_number=bvn_number, is_verified=True).last()
                if get_bvn_exist:
                    get_bvn_exist.save()
                    response = {
                        "error": "042",
                        "message": "This BVN is currently tied to a user on our system"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)


            if BlacklistedBVN.objects.filter(bvn_number=bvn_number).exists():
                response = {
                    "error": "043",
                    "message": "This BVN cannot be verified for security reasons"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if BVNWatchlist.is_bvn_watchlisted(bvn=bvn_number):
                response = {
                    "error": "044",
                    "message": "This BVN is watchlisted. Please contact admin"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            try:
                user = User.objects.get(phone_number=phone_number)
                user_kyc, created = KYCTable.objects.get_or_create(user=user)
                bvn_rel, created = BVNDetail.objects.get_or_create(kyc=user_kyc)
                docs_rel, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=user_kyc)
                if bvn_rel.is_verified:
                    response = {
                        "error": "23",
                        "message": "bvn already verified",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                else:

                        bvn_resp = BVNDetail.verify_bvn(bvn_number=bvn_number, user=user)

                        if bvn_resp:
                            if bvn_resp.get("status") == True:
                                if bvn_resp.get("bvn_phone_number") not in ["", " ", None]:
                                    bvn_phone_number = bvn_resp.get("bvn_phone_number")
                                    first_covered = bvn_phone_number[0:4]
                                    second_covered = bvn_phone_number[-3:]
                                    total_covered = first_covered + "****" + second_covered
                                    # send_whisper_otp = send_bvn_otp_task.delay(
                                    #     formatted_bvn_phone_number=User.format_number_from_back_add_234(
                                    #         bvn_phone_number
                                    #     ),
                                    #     app_name="Liberty Agency BVN OTP",
                                    # )

                                    if otp_type == "TEXT" or otp_type == None or otp_type == "":
                                        send_whisper_otp = Users_Otp.send_new_otp(
                                            phone_number=User.format_number_from_back_add_234(
                                                bvn_phone_number
                                            ),
                                            app_name="Liberty Agency BVN OTP",
                                            no_callback=True
                                        )

                                        response = {
                                            "message": f"An OTP code has been sent to your phone number linked to your BVN {total_covered}",
                                            "bvn_phone": total_covered,
                                        }

                                    elif otp_type == "VOICE":
                                        send_whisper_otp_voice = Users_Otp.send_voice_otp_function(
                                            phone_number=User.format_number_from_back_add_234(
                                                bvn_phone_number
                                            ),
                                            app_name="Liberty Agency BVN OTP",
                                        )
                                        response = {
                                            "message": f"You will receive a call on the phone number linked to your BVN {total_covered}. Enter the OTP code you hear",
                                            "bvn_phone": total_covered,
                                        }

                                else:
                                    response = {
                                        "message": f"An OTP code has been sent to your phone number linked to your BVN",
                                    }
                                if onboarding_status:
                                    user.onboarding_status = onboarding_status
                                    user.save()

                                return Response(response, status=status.HTTP_200_OK)

                            # elif bvn_resp.get("status") == True and bvn_resp.get("bvn_phone_number") is None:

                            #     response = {
                            #         "message": f"An OTP code has been sent to your phone number linked to your BVN",
                            #     }
                            #     return Response(response, status=status.HTTP_200_OK)

                            else:
                                response = {
                                    "error": "23",
                                    "message": "Could not verify BVN",
                                    "data": bvn_resp,
                                }
                                return Response(response, status=status.HTTP_200_OK)

                        else:
                            response = {
                                "error": "23",
                                "message": "Could not verify BVN",
                                "data": bvn_resp,
                            }
                            return Response(response, status=status.HTTP_200_OK)


            except User.DoesNotExist:
                response = {"error_code": "01", "message": "user does not exist"}
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class V2VerifyBVNAPIView(APIView):
    permission_classes = [CustomIsAuthenticated]
    serializer_class = BVNSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            otp_type = serializer.validated_data.get("otp_type")
            bvn_number = serializer.validated_data["bvn_number"]


            if ConstantTable.get_constant_table_instance().allow_many_bvn == False:

                get_bvn_exist = BVNDetail.objects.filter(bvn_number=bvn_number, is_verified=True).last()
                if get_bvn_exist:
                    get_bvn_exist.save()
                    response = {
                        "status": "error",
                        "error_code": "042",
                        "message": "This BVN is currently tied to a user on our system"
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)


            if BlacklistedBVN.objects.filter(bvn_number=bvn_number).exists():
                response = {
                    "status": "error",
                    "error_code": "043",
                    "message": "This BVN cannot be verified for security reasons"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if BVNWatchlist.is_bvn_watchlisted(bvn=bvn_number):
                response = {
                    "error": "044",
                    "message": "This BVN is watchlisted. Please contact admin"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)



            try:
                user = request.user
                user_kyc, created = KYCTable.objects.get_or_create(user=user)
                bvn_rel, created = BVNDetail.objects.get_or_create(kyc=user_kyc)
                docs_rel, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=user_kyc)
                if bvn_rel.is_verified:
                    response = {
                        "status": "error",
                        "error_code": "23",
                        "message": "bvn already verified",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                else:

                        bvn_resp = BVNDetail.verify_bvn(bvn_number=bvn_number, user=user)

                        if bvn_resp:
                            if bvn_resp.get("status") == True:
                                if bvn_resp.get("bvn_phone_number") not in ["", " ", None]:
                                    bvn_phone_number = bvn_resp.get("bvn_phone_number")
                                    first_covered = bvn_phone_number[0:4]
                                    second_covered = bvn_phone_number[-3:]
                                    total_covered = first_covered + "****" + second_covered
                                    # send_whisper_otp = send_bvn_otp_task.delay(
                                    #     formatted_bvn_phone_number=User.format_number_from_back_add_234(
                                    #         bvn_phone_number
                                    #     ),
                                    #     app_name="Liberty Agency BVN OTP",
                                    # )

                                    if otp_type == "TEXT" or otp_type == None or otp_type == "":
                                        send_whisper_otp = Users_Otp.send_new_otp(
                                            phone_number=User.format_number_from_back_add_234(
                                                bvn_phone_number
                                            ),
                                            app_name="Liberty Agency BVN OTP",
                                            no_callback=True
                                        )

                                        response = {
                                            "message": f"An OTP code has been sent to your phone number linked to your BVN {total_covered}",
                                            "bvn_phone": total_covered,
                                        }

                                    elif otp_type == "VOICE":
                                        send_whisper_otp_voice = Users_Otp.send_voice_otp_function(
                                            phone_number=User.format_number_from_back_add_234(
                                                bvn_phone_number
                                            ),
                                            app_name="Liberty Agency BVN OTP",
                                        )
                                        response = {
                                            "message": f"You will receive a call on the phone number linked to your BVN {total_covered}. Enter the OTP code you hear",
                                            "bvn_phone": total_covered,
                                        }

                                else:

                                    response = {
                                        "message": f"An OTP code has been sent to your phone number linked to your BVN",
                                    }

                                response["status"] = "success"
                                return Response(response, status=status.HTTP_200_OK)


                            else:
                                response = {
                                    "status": "error",
                                    "error_code": "23",
                                    "message": "Could not verify BVN",
                                    "data": bvn_resp,
                                }
                                return Response(response, status=status.HTTP_200_OK)

                        else:
                            response = {
                                "status": "error",
                                "error_code": "23",
                                "message": "Could not verify BVN",
                                "data": bvn_resp,
                            }
                            return Response(response, status=status.HTTP_200_OK)


            except User.DoesNotExist:
                response = {
                    "status": "error",
                    "error_code": "01",
                    "message": "user does not exist"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class VerifyBVNAPIViewMETAMAP(APIView):
    # permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin]

    serializer_class = BVNSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            bvn_number = serializer.validated_data["bvn_number"]
            phone_number = User.format_number_from_back_add_234(
                serializer.validated_data["phone_number"]
            )

            if BVNDetail.objects.filter(bvn_number=bvn_number).exists():
                response = {
                    "error": "23",
                    "message": "A user with this bvn already exists"
                }
                return Response(response, status=status.HTTP_200_OK)
            else:

                try:
                    user = User.objects.get(phone_number=phone_number)
                    if user.check_kyc.bvn_rel.is_verified:
                        response = {
                            "error": "23",
                            "message": "bvn already verified",
                        }
                        return Response(response, status=status.HTTP_200_OK)
                    else:
                        bvn_resp = BVNDetail.verify_bvn(bvn_number=bvn_number, user=user)

                        if bvn_resp.get("status") == True and bvn_resp.get("bvn_phone_number"):
                            bvn_phone_number = bvn_resp.get("bvn_phone_number")
                            first_covered = bvn_phone_number[0:4]
                            second_covered = bvn_phone_number[-3:]
                            total_covered = first_covered + "****" + second_covered
                            # send_whisper_otp = send_bvn_otp_task.delay(
                            #     formatted_bvn_phone_number=User.format_number_from_back_add_234(
                            #         bvn_phone_number
                            #     ),
                            #     app_name="Liberty Agency BVN OTP",
                            # )
                            send_whisper_otp = Users_Otp.send_new_otp(
                                phone_number=User.format_number_from_back_add_234(
                                    bvn_phone_number
                                ),
                                app_name="Liberty Agency BVN OTP",
                            )

                            response = {
                                "message": f"An OTP code has been sent to your phone number linked to your BVN {total_covered}",
                            }
                            return Response(response, status=status.HTTP_200_OK)
                        else:
                            response = {
                                "error": "23",
                                "message": "Could not verify BVN",
                                "data": bvn_resp,
                            }
                            return Response(response, status=status.HTTP_200_OK)

                except User.DoesNotExist:
                    response = {"error_code": "01", "message": "user does not exist"}
                    return Response(response, status=status.HTTP_404_NOT_FOUND)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class NonUserConfirmBVNOTPVerifyAPIView(APIView):
    # permission_classes = []

    def post(self, request):
        serializer = OTPVerificationSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(
                serializer.validated_data["phone_number"]
            )
            app_name = serializer.validated_data["app_name"]
            otp_value = serializer.validated_data["otp"]
            # user_instance = request.user
            # user_instance = User.objects.filter(phone_number=phone_number).last()
            # if user_instance:
            #     bvn_instance = user_instance.check_kyc.bvn_rel

            true_bvn_phone_num = User.format_number_from_back_add_234(phone_number)

            if len(true_bvn_phone_num) == 13:

                    verify_otp = Users_Otp.verify_new_otp(
                        true_bvn_phone_num, otp_value, app_name
                    )
                    if verify_otp["verified"] is True:

                        response = {"message": "OTP Verified"}

                        return Response(response, status=status.HTTP_202_ACCEPTED)

                    else:
                        response = {"error": "18", "message": "Invalid OTP"}

                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

            else:
                response = {
                    "message": f"Incorrect phone number format: {phone_number}"
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ConfirmBVNOTPVerifyAPIView(APIView):
    # permission_classes = []

    def post(self, request):
        serializer = OTPVerificationSerializer(data=request.data)
        if serializer.is_valid():
            phone_number = User.format_number_from_back_add_234(
                serializer.validated_data["phone_number"]
            )
            app_name = serializer.validated_data["app_name"]
            otp_value = serializer.validated_data["otp"]
            onboarding_status = serializer.validated_data.get("onboarding_status", None)
            # user_instance = request.user
            user_instance = User.objects.filter(phone_number=phone_number).last()
            if user_instance:
                bvn_instance = user_instance.check_kyc.bvn_rel

                if bvn_instance and bvn_instance.bvn_phone_number:
                    # Check if bvn is watchlisted
                    if suspend_watchlisted_bvn_user(user_instance):
                        return Response({"message": "BVN is watchlisted, and you has been suspended. Please contact admin"},
                                        status=status.HTTP_400_BAD_REQUEST)

                    true_bvn_phone_num = User.format_number_from_back_add_234(
                        bvn_instance.bvn_phone_number
                    )

                    if len(true_bvn_phone_num) == 13:

                        verify_otp = Users_Otp.verify_new_otp(
                            true_bvn_phone_num, otp_value, app_name
                        )
                        if verify_otp["verified"] is True:
                            bvn_instance.is_verified = True
                            bvn_instance.channel = "WHISPER_OTP"
                            bvn_instance.verification_status = "SUCCESSFUL"
                            bvn_instance.save()

                            if onboarding_status:
                                user_instance.onboarding_status = onboarding_status
                                user_instance.save()

                            create_wallets = create_wallets_and_accounts_task(bvn_instance_id=bvn_instance.id)

                            response = {"message": "OTP Verified"}

                            return Response(response, status=status.HTTP_202_ACCEPTED)

                        else:
                            response = {"error": "18", "message": "Invalid OTP"}
                        return Response(response, status=status.HTTP_409_CONFLICT)

                    else:
                        response = {
                            "message": f"Incorrect phone number format: {phone_number}"
                        }
                        return Response(response, status=status.HTTP_404_NOT_FOUND)

                else:
                    response = {
                        "message": "No BVN Phone Number Found"
                    }
                    return Response(response, status=status.HTTP_404_NOT_FOUND)
            else:
                response = {
                    "error": "24",
                    "message": "user with phone number does not exist",
                }
                return Response(response, status=status.HTTP_404_NOT_FOUND)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


################################################################################################

# Metamap
class MetamapAPIView(APIView):
    permission_classes = [MetaMapViewWhitelist]

    def post(self, request):
        resp = request.data

        metamap_rawdata = MetamapRawData.objects.create(
            payload = json.dumps(resp)
        )

        flow_id = resp.get("flowId")
        meta_data = resp.get("metadata")

        # print(meta_data)


        event_name = resp.get("eventName")
        if event_name == "verification_completed" or "verification_updated":
            resource_url = resp.get("resource")

            metamap_instance = MetaMap()

            access_token = metamap_instance.authenticate_metamap()

            # print(access_token)

            # if meta_data:
            #     user_email = meta_data.get("user_email")
            #     user_type = meta_data.get("user_type")

            #     if user_type == "SALES_REP":
            #         sales_rep_phone_number = user_email
            #         sen

            #     elif user_type == "AGENT_USER":
            #         if user_email:
            #             get_user_instance = User.objects.filter(email=user_email).last()
            #             if get_user_instance and user_type == "AGENT_USER":
            #                 user_instance = get_user_instance
            #             else:
            #                 user_instance = None
            #         else:
            #             user_instance = None
            # else:
            #     user_instance = None

            retrieve_resource_raw = metamap_instance.retrieve_resource(resource_url=resource_url, access_token=access_token)
            retrieve_resource = retrieve_resource_raw.json()
            # print(retrieve_resource)

            resource_retreive_flow_id = retrieve_resource.get("flow").get("id")
            resource_retreive_status = retrieve_resource.get("identity").get("status")

            if meta_data:
                user_email = meta_data.get("user_email")
                user_type = meta_data.get("user_type")


                if user_type == "SALES_REP":
                    sales_rep_phone_number = user_email
                    send_verification_status_out = send_out_verification_status(phone_number = sales_rep_phone_number, verification_status=resource_retreive_status)

                elif user_type == "AGENT_USER":
                    if user_email:
                        get_user_instance = User.objects.filter(email=user_email).last()
                        if get_user_instance and user_type == "AGENT_USER":
                            user_instance = get_user_instance
                        else:
                            user_instance = None
                    else:
                        user_instance = None


                    # Start Agent Verification
                    if user_instance is not None:
                        kyc_instance = KYCTable.objects.filter(user=user_instance).last()
                        doc_face_ver, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=kyc_instance)
                    else:
                        doc_face_ver = None



                    if resource_retreive_flow_id is not None and resource_retreive_status is not None:
                        if resource_retreive_flow_id == flow_id and resource_retreive_status == "verified":
                            if user_instance is not None and doc_face_ver is not None:

                                metamap_rawdata.user = user_instance
                                metamap_rawdata.verification_type = "KYC2_DOCUMENT_VERIFICATION"
                                metamap_rawdata.save()


                                doc_face_ver.verification_status = "SUCCESSFUL"
                                doc_face_ver.is_verified = True
                                doc_face_ver.date_added = datetime.now()
                                doc_face_ver.resource_id = retrieve_resource["id"]
                                doc_face_ver.resource_url = f"https://api.getmati.com/v2/verifications/{doc_face_ver.resource_id}"
                                doc_face_ver.payload = json.dumps(retrieve_resource)

                                payload_dump = retrieve_resource

                                channel = "METAMAP"
                                id_type = payload_dump["documents"][0]["type"]
                                id_sub_type = payload_dump["documents"][0].get("subtype")
                                front_view_url = payload_dump["documents"][0]["photos"][0]
                                back_view_url = payload_dump["documents"][0]["photos"][1] if len(payload_dump["documents"][0]["photos"]) > 1 else None
                                liveness_data = next(item for item in payload_dump["steps"] if item["id"] == "liveness")["data"]

                                selfie_url = liveness_data.get("selfieUrl")
                                sprite_url = liveness_data.get("spriteUrl")
                                video_url = liveness_data.get("videoUrl")

                                doc_face_ver.channel = channel
                                doc_face_ver.id_type = id_type
                                doc_face_ver.id_sub_type = id_sub_type
                                doc_face_ver.front_view_url = front_view_url
                                doc_face_ver.back_view_url = back_view_url
                                doc_face_ver.selfie_url = selfie_url
                                doc_face_ver.sprite_url = sprite_url
                                doc_face_ver.video_url = video_url

                                doc_face_ver.save()

                            else:
                                pass

                        elif resource_retreive_flow_id == flow_id and resource_retreive_status == "reviewNeeded":
                            if user_instance is not None:

                                metamap_rawdata.user = user_instance
                                metamap_rawdata.verification_type = "KYC2_DOCUMENT_VERIFICATION"
                                metamap_rawdata.save()

                                doc_face_ver.verification_status = "PENDING"
                                doc_face_ver.is_verified = False
                                doc_face_ver.date_added = datetime.now()
                                doc_face_ver.resource_id = retrieve_resource["id"]
                                doc_face_ver.resource_url = f"https://api.getmati.com/v2/verifications/{doc_face_ver.resource_id}"
                                doc_face_ver.payload = json.dumps(retrieve_resource)
                                doc_face_ver.save()
                            else:
                                pass

                        elif resource_retreive_flow_id == flow_id and resource_retreive_status == "rejected":
                            if user_instance is not None:
                                metamap_rawdata.user = user_instance
                                metamap_rawdata.verification_type = "KYC2_DOCUMENT_VERIFICATION"
                                metamap_rawdata.save()


                                doc_face_ver.verification_status = "REJECTED"
                                doc_face_ver.is_verified = False
                                doc_face_ver.date_added = datetime.now()
                                doc_face_ver.resource_id = retrieve_resource["id"]
                                doc_face_ver.resource_url = f"https://api.getmati.com/v2/verifications/{doc_face_ver.resource_id}"
                                doc_face_ver.payload = json.dumps(retrieve_resource)
                                doc_face_ver.save()

                            else:
                                pass


                elif user_type == "AGENT_BVN_CHECK":

                    if user_email:
                        get_user_instance = User.objects.filter(email=user_email).last()

                        if get_user_instance:
                            user_instance = get_user_instance
                        else:
                            user_instance = None
                    else:
                        user_instance = None


                    # Start Agent Verification
                    if user_instance is not None:
                        kyc_instance = KYCTable.objects.filter(user=user_instance).last()
                        bvn_rel, created = BVNDetail.objects.get_or_create(kyc=kyc_instance)
                    else:
                        bvn_rel = None


                    if resource_retreive_flow_id is not None and resource_retreive_status is not None:
                        if resource_retreive_flow_id == flow_id:
                            if user_instance is not None and bvn_rel is not None:


                                metamap_rawdata.user = user_instance
                                metamap_rawdata.verification_type = "KYC1_BVN_VERIFICATION"
                                metamap_rawdata.save()



                                face_match_score_qs = retrieve_resource.get("steps")[0]["data"]
                                if face_match_score_qs is not None:
                                    face_match_score = retrieve_resource.get("steps")[0]["data"]["facematchScore"]
                                    bvn_other_data = retrieve_resource.get("steps")[2]["data"]
                                    bvn_number = retrieve_resource.get("steps")[2]["data"]["bvn"]

                                    bvn_phone_number = bvn_other_data["phone"]

                                    if len(bvn_phone_number) < 10:
                                        formatted_bvn_phone_number = user_instance.phone_number
                                    else:
                                        formatted_bvn_phone_number = User.format_number_from_back_add_234(bvn_other_data["phone"])

                                    if BlacklistedBVN.objects.filter(bvn_number=bvn_number).exists():
                                        pass
                                    else:

                                        get_bvn_check_error = None

                                        if bvn_rel.is_verified:
                                            pass
                                        else:

                                            if not bvn_rel.bvn_number:
                                                bvn_rel.bvn_number = bvn_number
                                                bvn_rel.bvn_phone_number = formatted_bvn_phone_number
                                                bvn_rel.bvn_phone_number_2 = bvn_other_data.get("phone2")
                                                bvn_rel.bvn_first_name = bvn_other_data.get("firstName")
                                                bvn_rel.bvn_middle_name = bvn_other_data.get("middleName")
                                                bvn_rel.bvn_last_name = bvn_other_data.get("lastName")
                                                bvn_rel.bvn_gender = bvn_other_data.get("gender")
                                                bvn_rel.bvn_birthdate = bvn_other_data.get("dateOfBirth")
                                                bvn_rel.bvn_nationality = bvn_other_data.get("nationality")
                                                bvn_rel.bvn_watchlisted = bvn_other_data.get("watchListed")
                                                bvn_rel.bvn_marital_status = bvn_other_data.get("maritalStatus")
                                                bvn_rel.bvn_lga_of_origin = bvn_other_data.get("lgaOfOrigin")
                                                bvn_rel.bvn_residential_address = bvn_other_data.get("residentialAddress")
                                                bvn_rel.bvn_state_of_origin = bvn_other_data.get("stateOfOrigin")
                                                bvn_rel.payload = retrieve_resource
                                                bvn_rel.verified_by = "metamap"
                                                bvn_rel.save()



                                            if bvn_number == bvn_rel.bvn_number:

                                                if face_match_score > 70:
                                                    bvn_rel.verification_status = "SUCCESSFUL"
                                                    bvn_rel.is_verified = True
                                                    bvn_rel.date_added = datetime.now()
                                                    # bvn_rel.payload = str(retrieve_resource)
                                                    bvn_rel.channel = "METAMAP"
                                                    bvn_rel.save()

                                                    create_wallets = create_wallets_and_accounts_task(bvn_instance_id=bvn_rel.id)

                                                else:
                                                    get_bvn_check_error = "Face Does not match"

                                                    bvn_rel.failure_reason = get_bvn_check_error
                                                    bvn_rel.save()

                                            else:
                                                get_bvn_check_error = "BVN number is different"

                                                bvn_rel.failure_reason = get_bvn_check_error
                                                bvn_rel.save()


                                    # if retrieve_resource.get("steps")[2]["error"] is None:
                                    #     # print(face_match_score)
                                    #     # print(bvn_number)
                                    #     # get_bvn_check_error = None

                                    #     if bvn_number == bvn_rel.bvn_number:

                                    #         get_bvn_check_error = None

                                    #         if resource_retreive_status == "verified":

                                    #             bvn_rel.verification_status = "SUCCESSFUL"
                                    #             bvn_rel.is_verified = True
                                    #             bvn_rel.date_added = datetime.now()
                                    #             bvn_rel.payload = str(retrieve_resource)
                                    #             bvn_rel.save()

                                    #             create_wallets = create_wallets_and_accounts_task.delay(bvn_instance_id=bvn_rel.id)


                                    #         elif resource_retreive_status == "reviewNeeded":
                                    #             bvn_rel.verification_status = "PENDING"
                                    #             bvn_rel.is_verified = False
                                    #             bvn_rel.date_added = datetime.now()
                                    #             bvn_rel.payload = str(retrieve_resource)
                                    #             bvn_rel.save()


                                    #         elif resource_retreive_status == "rejected":
                                    #             bvn_rel.verification_status = "REJECTED"
                                    #             bvn_rel.is_verified = False
                                    #             bvn_rel.date_added = datetime.now()
                                    #             bvn_rel.payload = str(retrieve_resource)
                                    #             bvn_rel.save()

                                    #     else:
                                    #         get_bvn_check_error = "BVN enetered on signing up does not match with what was received on facematch"

                                    #     print(get_bvn_check_error)
                                    # else:
                                    #     get_bvn_check_error = retrieve_resource.get("steps")[2]["error"]["message"]

                                    # bvn_rel.failure_reason = get_bvn_check_error
                                    # bvn_rel.save()
                                else:
                                    pass
                            else:
                                pass
                        else:
                            pass
                    else:
                        pass

                else:
                    pass

        return Response({"status": "True"}, status=status.HTTP_200_OK)


class MetaMapBVNAPIView(APIView):

    def post(self, request):
        resp = request.data

        return Response({"status": "True"}, status=status.HTTP_200_OK)



################################################################################################

# Identity Pass

class IdentityPassAPIView(APIView):
    permission_classes = [MetaMapViewWhitelist]

    def post(self, request):
        resp = request.data

        idpass_rawdata = MetamapRawData.objects.create(
            payload = json.dumps(resp),
            provider = "IDPASS"
        )

        log_debug("----------------------------------")
        log_debug("----------------------------------")
        log_info(str(request.headers))
        log_debug("----------------------------------")
        log_debug("----------------------------------")


        returned_response = {
            "status": True,
            "message": "data received"
        }

        return Response(returned_response, status=status.HTTP_200_OK)


class GuarantorDetailAPIView(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, HasKYCLevelTwo]

    def post(self, request):
        serializer = NewGuarantorFormSerializerIn(data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class GuarantorDetailAPIViewOld(APIView):
    permission_classes = [CustomIsAuthenticated, OTPVerified, HasTransactionPin, HasKYC, HasKYCLevelTwo]

    def post(self, request):
        user_instance: User = request.user

        serializer = GuarantorDetailSerializer(data=request.data)
        if serializer.is_valid():
            guarantor_name = serializer.validated_data["guarantor_name"]
            guarantor_phone_number = serializer.validated_data["guarantor_phone_number"]
            guarantor_email = serializer.validated_data["guarantor_email"]
            guarantor_occupation = serializer.validated_data["guarantor_occupation"]
            guarantor_address = serializer.validated_data["guarantor_address"]
            next_of_kin_name = serializer.validated_data["next_of_kin_name"]
            next_of_kin_relationship = serializer.validated_data["next_of_kin_relationship"]
            next_of_kin_phone_number = serializer.validated_data["next_of_kin_phone_number"]
            next_of_kin_address = serializer.validated_data["next_of_kin_address"]

            len_of_guarantor_phone_number = len(guarantor_phone_number)
            formatted_guarantor_phone_number = User.format_number_from_back_add_234(guarantor_phone_number)

            len_of_next_of_kin_phone_number = len(next_of_kin_phone_number)
            formatted_next_of_kin_phone_number = User.format_number_from_back_add_234(next_of_kin_phone_number)

            if user_instance.type_of_user == "LOTTO_AGENT":
                is_lotto_user = True
            else:
                is_lotto_user = False

            if not guarantor_phone_number.isnumeric() or not next_of_kin_phone_number.isnumeric():
                response = {
                    "error_code": "14",
                    "message": "You must supply an integer for guarantor phone_number and next of kin phone number"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Guarantor Checks
            if len_of_guarantor_phone_number == 13 and not guarantor_phone_number.startswith("234"):
                response = {
                    "error": "174",
                    "message": f"entered gurarantor phone number is incorrect. it is {len_of_guarantor_phone_number} numbers."
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if len_of_guarantor_phone_number != 11 and len_of_guarantor_phone_number != 13:
                response = {
                    "error": "177",
                    "message": f"entered guarantor phone number is incorrect. it is {len_of_guarantor_phone_number} numbers."
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            # Next of kin Checks
            if len_of_next_of_kin_phone_number == 13 and not next_of_kin_phone_number.startswith("234"):
                response = {
                    "error": "174",
                    "message": f"entered next of kin number is incorrect. it is {len_of_next_of_kin_phone_number} numbers."
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if len_of_next_of_kin_phone_number != 11 and len_of_next_of_kin_phone_number != 13:
                response = {
                    "error": "177",
                    "message": f"entered next of kin phone number is incorrect. it is {len_of_next_of_kin_phone_number} numbers."
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if user_instance.phone_number == formatted_guarantor_phone_number:
                response = {
                    "error": "178",
                    "message": "Sorry, you cannot enter your phone number as guarantor number"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if custom_validate_email(guarantor_email) == False:
                response = {
                    "error": "179",
                    "message": "Sorry, the email you entered is incorrect"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if user_instance.email == guarantor_email:
                response = {
                    "error": "194",
                    "message": "Sorry, you cannot enter your email as guarantor email"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if user_instance.phone_number == next_of_kin_phone_number:
                response = {
                    "error": "194",
                    "message": "Sorry, you cannot enter your phone number as guarantor next of kin phone number"
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            get_guarantor_rel = GuarantorDetail.objects.filter(kyc=user_instance.check_kyc).last()
            if get_guarantor_rel:
                if get_guarantor_rel.is_verified:
                    response = {
                        "error": "355",
                        "message": "Guarantor Form Already Verified",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                # elif get_guarantor_rel.awaiting_verification is True or get_guarantor_rel.verification_status == "PENDING":
                elif get_guarantor_rel.verf_started:
                    response = {
                        "error": "356",
                        "message": "Guarantor Form is being processed",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    get_guarantor_rel.guarantor_name = guarantor_name
                    get_guarantor_rel.guarantor_phone_number = formatted_guarantor_phone_number
                    get_guarantor_rel.guarantor_email = guarantor_email
                    get_guarantor_rel.guarantor_occupation = guarantor_occupation
                    get_guarantor_rel.guarantor_address = guarantor_address
                    get_guarantor_rel.next_of_kin_name = next_of_kin_name
                    get_guarantor_rel.next_of_kin_relationship = next_of_kin_relationship
                    get_guarantor_rel.next_of_kin_phone_number = formatted_next_of_kin_phone_number
                    get_guarantor_rel.next_of_kin_address = next_of_kin_address
                    get_guarantor_rel.verification_status = "PENDING"
                    # get_guarantor_rel.verification_unique_id = verification_unique_id
                    get_guarantor_rel.awaiting_verification = True
                    get_guarantor_rel.payload = str(request.data)
                    get_guarantor_rel.is_lotto_user = is_lotto_user
                    get_guarantor_rel.date_started = datetime.now()
                    get_guarantor_rel.save()

                    response = {
                        "message": "Request successfully received. Thank you"
                    }

            else:
                get_guarantor_rel = GuarantorDetail.objects.create(
                    kyc = user_instance.check_kyc,
                    guarantor_name = guarantor_name,
                    guarantor_phone_number = formatted_guarantor_phone_number,
                    guarantor_email = guarantor_email,
                    guarantor_occupation = guarantor_occupation,
                    guarantor_address = guarantor_address,
                    next_of_kin_name = next_of_kin_name,
                    next_of_kin_relationship = next_of_kin_relationship,
                    next_of_kin_phone_number = formatted_next_of_kin_phone_number,
                    next_of_kin_address = next_of_kin_address,
                    verification_status = "PENDING",
                    # verification_unique_id = verification_unique_id,
                    awaiting_verification = True,
                    is_lotto_user = is_lotto_user,
                    payload = str(request.data),
                    date_started = datetime.now()
                )

                # Send email to guarantor
                try:
                    send_email_to_guarantor(get_guarantor_rel.id)
                    # send_email_to_guarantor.delay(str(get_guarantor_rel.id))
                except Exception as err:
                    log_info(str(err))

                response = {
                    "message": "Request successfully received. Thank you"
                }

            # # WORK WITH DATA
            # if is_lotto_user == True:

            lotto_verf_data = {
                "verification_unique_id": get_guarantor_rel.verification_unique_id,
                "agent_name": user_instance.bvn_full_name if user_instance.bvn_first_name else user_instance.full_name,
                "agent_phone": user_instance.phone_number,
                "agent_email": user_instance.email,
                "user_type": user_instance.type_of_user,
                "guarantor_name": get_guarantor_rel.guarantor_name,
                "guarantor_email": get_guarantor_rel.guarantor_email,
                "guarantor_phone": get_guarantor_rel.guarantor_phone_number,
                "guarantor_occupation": get_guarantor_rel.guarantor_occupation,
                "guarantor_address": get_guarantor_rel.guarantor_address
            }

            get_guarantor_rel.verf_started = True
            get_guarantor_rel.initial_payload = lotto_verf_data

            get_guarantor_rel.save()

            # send_data_to_lotto_back = send_data_to_lotto_back_task.delay(data=lotto_verf_data, guarantor_id=get_guarantor_rel.id)
            send_data_to_lotto_back = send_data_to_lotto_back_task_without_delay(data=lotto_verf_data, guarantor_id=get_guarantor_rel.id)

            return Response(response, status=status.HTTP_202_ACCEPTED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GuarantorDetailAdminVerificationAPIView(APIView):
    permission_classes = []

    def put(self, request, email):
        guarantor = GuarantorDetail.objects.filter(guarantor_email__iexact=email)
        instance = guarantor.last() if guarantor.exists() else None
        if not instance:
            raise InvalidRequestException({"detail": "Guarantor information with this email is unavailable"})
        serializer = GuarantorDetailAdminVerificationSerializerIn(instance=instance, data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


###########################################
# Guarantor verification
###########################################


class RecieveLottoVerfAPIView(APIView):
    permission_classes = [OtherServiceOtherPermissions]

    def post(self, request):

        raw_data_guarantor = MetamapRawData.objects.create(
            verification_type = "GUARANTOR",
            payload = request.data
        )

        serializer = GuarantorLottoVerfSerializer(data=request.data)
        if serializer.is_valid():

            verification_unique_id = serializer.validated_data["verification_unique_id"]
            verification_status = serializer.validated_data["verification_status"]
            guarantor_verified_first_name = serializer.validated_data["guarantor_verified_first_name"]
            guarantor_verified_last_name = serializer.validated_data["guarantor_verified_last_name"]
            guarantor_verified_address = serializer.validated_data["guarantor_verified_address"]
            guarantor_id_type = serializer.validated_data["guarantor_id_type"]
            guarantor_id_number = serializer.validated_data["guarantor_id_number"]
            guarantor_id_is_verified = serializer.validated_data["guarantor_id_is_verified"]
            guarantor_id_verification_payload = serializer.validated_data["guarantor_id_verification_payload"]

            # (REJECTED, "REJECTED")




            get_guarantor_user = GuarantorDetail.objects.filter(verification_unique_id = verification_unique_id).last()
            if not get_guarantor_user:
                response = {
                    "error": "99",
                    "message": "User for guarantor does not exist",
                }
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            else:
                if get_guarantor_user.guarantor_id_is_verified and get_guarantor_user.is_verified:
                    response = {
                        "error": "101",
                        "message": "Guarantor for this user is already verified",
                    }
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)

                else:
                    get_guarantor_user.verification_status = verification_status
                    get_guarantor_user.guarantor_verified_first_name = guarantor_verified_first_name
                    get_guarantor_user.guarantor_verified_last_name = guarantor_verified_last_name
                    get_guarantor_user.guarantor_verified_address = guarantor_verified_address
                    get_guarantor_user.guarantor_id_type = guarantor_id_type
                    get_guarantor_user.guarantor_id_number = guarantor_id_number
                    get_guarantor_user.guarantor_id_is_verified = guarantor_id_is_verified
                    get_guarantor_user.guarantor_id_verification_payload = guarantor_id_verification_payload


                    if guarantor_id_is_verified == True and verification_status == "SUCCESSFUL":
                        get_guarantor_user.awaiting_verification = False
                        get_guarantor_user.is_verified = True
                        get_guarantor_user.date_verified = datetime.now()
                    else:
                        get_guarantor_user.awaiting_verification = False
                        get_guarantor_user.is_verified = False
                        get_guarantor_user.date_verified = datetime.now()

                    get_guarantor_user.save()


                    response = {
                        "message": "data recieved"
                    }

                    return Response(response, status=status.HTTP_202_ACCEPTED)


        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GuarantorVerificationForm(APIView):
    post_serializer_class = GuarantorWebLinkVerfSerializer

    def get(self, request):
        verification_unique_id = request.query_params.get('verification_unique_id')
        if verification_unique_id is None:
            response = {
                "error": "549",
                "message": "no gurantor_id attached"
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        guarantor_det = GuarantorDetail.objects.filter(verification_unique_id=verification_unique_id).last()

        if guarantor_det:
            if guarantor_det.is_verified:
                response = {
                    "error": "550",
                    "message": "Guarantor Details Already Verified"
                }
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            else:
                response = {
                    'status': 'success',
                    'message': 'success',
                }
                return Response(response, status=status.HTTP_200_OK)

        else:
            response = {
                "error": "550",
                "message": "Guarantor Details Does Not Exist"
            }
            return Response(response, status=status.HTTP_401_UNAUTHORIZED)


#     def post(self, request):
#         serializer = self.post_serializer_class(data=request.data)
#         if serializer.is_valid():
#             verification_unique_id = serializer.validated_data["verification_unique_id"]
#             bvn_number = serializer.validated_data["bvn_number"]

#             bvn_resp = BVNDetail.verify_bvn(bvn_number=bvn_number, user=None, last_name=last_name)
#     user, last_name=None, verified_for=None):


#             # if bvn_resp is None:
#             if bvn_resp.get("status") == True and bvn_resp.get("bvn_phone_number"):
#                 bvn_phone_number = bvn_resp.get("bvn_phone_number")
#                 first_covered = bvn_phone_number[0:4]
#                 second_covered = bvn_phone_number[-3:]
#                 total_covered = first_covered + "****" + second_covered
#                 # send_whisper_otp = send_bvn_otp_task.delay(
#                 #     formatted_bvn_phone_number=User.format_number_from_back_add_234(
#                 #         bvn_phone_number
#                 #     ),
#                 #     app_name="Liberty Agency BVN OTP",
#                 # )

#                 # send_whisper_otp = Users_Otp.send_new_otp(
#                 #     phone_number=User.format_number_from_back_add_234(
#                 #         bvn_phone_number
#                 #     ),
#                 #     app_name="Liberty Agency BVN OTP",
#                 # )

#                 response = {
#                     # "message": f"An OTP code has been sent to your phone number linked to your BVN {total_covered}",
#                     "data": bvn_resp.get("data")
#                 }
#                 return Response(response, status=status.HTTP_200_OK)

#             else:
#                 response = {
#                     "error": "23",
#                     "message": "Could not verify BVN",
#                     "data": bvn_resp,
#                 }
#                 return Response(response, status=status.HTTP_400_BAD_REQUEST)



#         data = request.data
#         user = User.objects.get(phone_number=user_mobile)
#         if not user:    # should never happen
#             resp = {
#                 "error": "404",
#                 "message": "User account not found!"
#             }
#             return Response(resp, status=status.HTTP_404_NOT_FOUND)
#         guarantor = GuarantorDetail.objects.get(kyc__user=user)
#         guarantor_fullname = data.first_name + " " + data.last_name
#         if guarantor_fullname != guarantor.name:
#             resp = {
#                 "error": "404",
#                 "message": "Guarantor details are not correct!"
#             }
#             return Response(resp, status=status.HTTP_400_BAD_REQUEST)
#         guarantor_bvn = data['bvn']
#         response = verify_bvn_with_uverify(guarantor_bvn, data['last_name'])
#         if response.get("success") == True and response.get("statusCode") == 200:
#             bvn_data = response.get("data")
#             bvn_phone_number = bvn_data.get("mobile")
#             if bvn_phone_number:
#                 formatted_bvn_phone_number = User.format_number_from_back_add_234(
#                     bvn_phone_number
#                 )
#                 resp = ThirdPartyApis.send_phone_otp(bvn_phone_number)
#                 guarantor.guarantor_entered_address = data['address']
#                 guarantor.guarantor_bvn = guarantor_bvn
#                 guarantor.guarantor_phone_number = formatted_bvn_phone_number
#                 guarantor.guarantor_bvn_verification_payload = bvn_data
#                 guarantor.save()
#                 serializer = GuarantorDetailSerializer(guarantor)
#                 return Response(serializer.data, status=status.HTTP_200_OK)
#         resp = {
#             "error": "404",
#             "message": "BVN isn't valid!"
#         }
#         return Response(resp, status=status.HTTP_400_BAD_REQUEST)

# class GuarantorBvnVerification(APIView):

#     def post(self, request):
#         data = request.data
#         otp = data['otp']
#         try:
#             guarantor = GuarantorDetail.objects.get(guarantor_email=data['guarantor_email'])
#         except GuarantorDetail.DoesNotExist:
#             raise Http404
#         resp = ThirdPartyApis.verify_otp(guarantor.guarantor_phone_number, otp)
#         if resp['verified']:
#             guarantor.guarantor_bvn_is_verified = True
#             guarantor.verification_status = "SUCCESSFUL"
#             guarantor.save()
#             serializer = GuarantorDetailSerializer(guarantor)
#             return Response(serializer.data, status=status.HTTP_200_OK)
#         resp = {
#             "error": "404",
#             "message": "Could not verify BVN!"
#         }
#         return Response(resp, status=status.HTTP_400_BAD_REQUEST)

# class GuarantorVerificationRejection(APIView):

#     def get(self, request, user_mobile):
#         try:
#             user = User.objects.get(phone_number=user_mobile)
#             guarantor = GuarantorDetail.objects.get(kyc__user=user)
#         except User.DoesNotExist:
#             raise Http404
#         except GuarantorDetail.DoesNotExist:
#             raise Http404
#         guarantor.verification_status = "REJECTED"
#         guarantor.save()
#         resp = {
#             'message': 'Guarantor application rejected successfully!'
#         }
#         return Response(resp, status=status.HTTP_200_OK)


# Dojah
class DojahAPIView(APIView):
    permission_classes = [MetaMapViewWhitelist]

    def post(self, request):
        resp = request.data

        dojah_rawdata = MetamapRawData.objects.create(
            payload=json.dumps(resp),
            source="DOJAH"
            )

        meta_data = resp.get("metadata")

        if meta_data:
            user_email = meta_data.get("user_email")
            user_type = meta_data.get("user_type")
            verification_status = resp.get("verificationStatus")

            if user_email:
                get_user_instance = User.objects.filter(email=user_email).last()

                if get_user_instance:
                    user_instance = get_user_instance
                else:
                    user_instance = None
            else:
                user_instance = None


            if user_type == "SALES_REP":
                sales_rep_phone_number = user_email
                send_out_verification_status = send_out_verification_status(
                    phone_number=sales_rep_phone_number, verification_status=""
                    )

            # KYC 2
            elif user_type == "AGENT_USER":

                # Start Agent Verification
                if user_instance is not None:
                    kyc_instance = KYCTable.objects.filter(user=user_instance).last()
                    doc_face_ver, created = DocumentFaceMatchKYC2Detail.objects.get_or_create(kyc=kyc_instance)
                else:
                    doc_face_ver = None

                if user_instance is not None and doc_face_ver is not None:
                    dojah_rawdata.user = user_instance
                    dojah_rawdata.verification_type = "KYC2_DOCUMENT_VERIFICATION"
                    dojah_rawdata.save()

                    channel = "DOJAH"
                    dumped_payload = json.dumps(resp)
                    resource_url = resp.get("verificationUrl")

                    # Check for successful metrics like liveliness url.
                    # If status is successful does not infer successful.

                    if verification_status == "Completed":
                        doc_face_ver.is_verified = True
                        doc_face_ver.verification_status = "SUCCESSFUL"
                        doc_face_ver.is_verified = True
                        doc_face_ver.date_added = datetime.now()
                        doc_face_ver.payload = dumped_payload
                        doc_face_ver.resource_url = resource_url
                        doc_face_ver.channel = channel
                        doc_face_ver.save()


                    elif verification_status == "Failed":
                            doc_face_ver.verification_status = "REJECTED"
                            doc_face_ver.is_verified = False
                            doc_face_ver.date_added = datetime.now()
                            doc_face_ver.resource_url = resource_url
                            doc_face_ver.payload = dumped_payload
                            doc_face_ver.save()

                    else:
                        doc_face_ver.verification_status = "PENDING"
                        doc_face_ver.is_verified = False
                        doc_face_ver.date_added = datetime.now()
                        doc_face_ver.resource_url = resource_url
                        doc_face_ver.payload = dumped_payload
                        doc_face_ver.save()


            # KYC 1
            elif user_type == "AGENT_BVN_CHECK":

                # Start Agent Verification
                if user_instance is not None:
                    kyc_instance = KYCTable.objects.filter(user=user_instance).last()
                    bvn_rel, created = BVNDetail.objects.get_or_create(kyc=kyc_instance)
                else:
                    bvn_rel = None

                if user_instance is not None and bvn_rel is not None:
                    dojah_rawdata.user = user_instance
                    dojah_rawdata.verification_type = "KYC1_BVN_VERIFICATION"
                    dojah_rawdata.save()

                    if verification_status == "Completed":
                        bvn_other_data = resp.get("governmentData")
                        bvn_number = resp.get("value")

                        bvn_phone_number = bvn_other_data.get("phone_number1")

                        if len(bvn_phone_number) < 10:
                            formatted_bvn_phone_number = user_instance.phone_number
                        else:
                            formatted_bvn_phone_number = User.format_number_from_back_add_234(bvn_other_data["phone_number1"])

                        if BlacklistedBVN.objects.filter(bvn_number=bvn_number).exists():
                            pass
                        else:

                            get_bvn_check_error = None

                            if bvn_rel.is_verified:
                                pass
                            else:

                                if bvn_rel.bvn_number is None:
                                    bvn_rel.bvn_number = bvn_number
                                    bvn_rel.bvn_phone_number = formatted_bvn_phone_number
                                    bvn_rel.bvn_phone_number_2 = bvn_other_data.get("phone_number2")
                                    bvn_rel.bvn_first_name = bvn_other_data.get("first_name")
                                    bvn_rel.bvn_middle_name = bvn_other_data.get("middle_name")
                                    bvn_rel.bvn_last_name = bvn_other_data.get("last_name")
                                    bvn_rel.bvn_gender = bvn_other_data.get("gender")
                                    bvn_rel.bvn_birthdate = bvn_other_data.get("date_of_birth")
                                    bvn_rel.bvn_nationality = bvn_other_data.get("nationality")
                                    bvn_rel.bvn_watchlisted = bvn_other_data.get("watch_listed")
                                    bvn_rel.bvn_marital_status = bvn_other_data.get("marital_status")
                                    bvn_rel.bvn_lga_of_origin = bvn_other_data.get("lga_of_origin")
                                    bvn_rel.bvn_residential_address = bvn_other_data.get("residential_address")
                                    bvn_rel.bvn_state_of_origin = bvn_other_data.get("state_of_origin")
                                    bvn_rel.payload = resp.get("governmentData")
                                    bvn_rel.verified_by = "dojah"
                                    bvn_rel.save()



                                if bvn_number == bvn_rel.bvn_number:

                                    if verification_status == "Completed":
                                        bvn_rel.verification_status = "SUCCESSFUL"
                                        bvn_rel.is_verified = True
                                        bvn_rel.date_added = datetime.now()
                                        # bvn_rel.payload = str(retrieve_resource)
                                        bvn_rel.channel = "DOJAH"
                                        bvn_rel.save()

                                        create_wallets = create_wallets_and_accounts_task(bvn_instance_id=bvn_rel.id)

                                    else:
                                        get_bvn_check_error = "Face Does not match"

                                        bvn_rel.failure_reason = get_bvn_check_error
                                        bvn_rel.save()

                                else:
                                    get_bvn_check_error = "BVN number is different"

                                    bvn_rel.failure_reason = get_bvn_check_error
                                    bvn_rel.save()

                else:
                    pass
            else:
                pass
        else:
            pass

        return Response({"status": "True"}, status=status.HTTP_200_OK)


# class UserImageVerificationView(APIView):
#     permission_classes = [CustomIsAuthenticated]

#     def post(self, request):
#         """Handle image upload and verification"""
#         # Add user to the data
#         data = request.data.copy()
#         data['user'] = request.user.id

#         serializer = UserImageSerializer(data=data)
#         if serializer.is_valid():
#             # Save the images
#             user_image = serializer.save()

#             try:

#                 verification_status = user_image.verify_user_image()

#                 # For now, just return the saved data

#                 return Response({
#                                     'error': None,
#                                     'detail': "Image verification successful",
#                                     'status': verification_status
#                                 }, status=status.HTTP_200_OK)

#             except Exception as e:
#                 user_image.is_match=False
#                 user_image.save()

#                 return Response({
#                     'error': 'Image verification failed',
#                     'detail': str(e)
#                 }, status=status.HTTP_400_BAD_REQUEST)

#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserImageVerificationView(APIView):
    permission_classes = [CustomIsAuthenticated]

    def post(self, request):
        """Handle image upload and verification"""
        # Add user to the data
        data = request.data.copy()
        data['user'] = request.user.id

        serializer = UserImageSerializer(data=data)
        if serializer.is_valid():
            # Save the images
            user_image = serializer.save()

            # # Skip the process for sub accounts
            # sub_account = UserOtherAccount.objects.filter(other_account=request.user)
            # if sub_account.exists():
            #     user_image.is_match = True
            #     user_image.save()
            #     return Response({
            #         'error': None,
            #         'detail': "Image verification successful",
            #         'status': True
            #     }, status=status.HTTP_200_OK)

        try:
            # First, try to get existing BVN payload
            kyc = KYCTable.objects.get(user=request.user)
            bvn_detail = kyc.bvn_rel

            if suspend_watchlisted_bvn_user(request.user):
                return Response({"status": False, "detail": "BVN is watchlisted, and account suspended. Please contact admin."},
                                status=status.HTTP_400_BAD_REQUEST)

            payload = None
            if bvn_detail.payload:
                try:
                    # First try standard JSON parsing
                    payload = json.loads(bvn_detail.payload)
                except json.JSONDecodeError:
                    try:
                        # If that fails, try parsing as a Python literal
                        payload = ast.literal_eval(bvn_detail.payload)
                    except (ValueError, SyntaxError):
                        # If both methods fail, set payload to None
                        payload = None

                # Check if we have either photo or image in the expected location
                if payload and type(payload) is str:
                    payload = ast.literal_eval(payload)

                if payload and not (payload.get('data', {}).get('photo') or payload.get('data', {}).get('image')):
                    payload = None

            # If no valid payload, fetch from YouVerify
            if not payload:
                # Assuming you have a method to get BVN number from user or KYC
                bvn_number = serializer.data['bvn_number']

                # Make YouVerify API request
                youverify_response = verify_bvn_with_uverify(bvn_number=bvn_number)
                log_info(f"YOUVERIFY: {youverify_response}")

                if youverify_response.get("success") == True and youverify_response.get("statusCode") == 200:
                    payload = youverify_response

                # If YouVerify fails, fallback to Dojah
                if (not payload or not payload.get('success', False)) and bvn_number:
                    # Fallback to Dojah
                    dojah_response = verify_bvn_with_dojah(bvn_number=bvn_number)
                    log_info("dojah_response")

                    if dojah_response.get("success") == True and dojah_response.get("statusCode") == 200:
                        payload = dojah_response["data"]["entity"]

            # Perform verification
            if payload:
                log_info(f"PAYLOAD: {payload}")
                verification_status = user_image.verify_user_image(payload)

                return Response({
                    'error': None,
                    'detail': "Image verification successful",
                    'status': True
                }, status=status.HTTP_200_OK)
            else:
                raise ValueError("Unable to retrieve BVN details")

        except Exception as e:
            user_image.is_match = False
            user_image.save()

            return Response({
                'error': 'Image verification failed',
                'detail': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetBVNNumberAPIView(APIView):
    permission_classes = []

    def post(self, request):
        serializer = BVNPhoneNumberSerializerIn(data=request.data, context={'request': request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


class VerificationCallBackAPIView(APIView):
    permission_classes = [WhitelistPermission]

    def post(self, request):
        serializer = VerificationCallBackSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response(response)


