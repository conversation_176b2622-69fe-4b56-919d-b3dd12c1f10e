#!/usr/bin/env python3
import os
import vgs
from django.conf import settings
from main.helper.logging_utils import log_info

# Defining the host is optional and defaults to https://api.sandbox.verygoodvault.com.
# For production use https://api.live.verygoodvault.com
config = vgs.config(
    # username=os.environ["VAULT_API_USERNAME"], 
    # password=os.environ["VAULT_API_PASSWORD"], 
    username = f"{settings.VGS_USERNAME}",
    password = f"{settings.VGS_PASSWORD}",
    host="https://api.sandbox.verygoodvault.com"
)

# in this example we are storing three tokens within your VGS vault using a single API call
# this will return three tokens which can be stored in your system and used individually or together
# through any VGS Vault product including our Zero Data platform.
#
# the aliases namespace allows you to directly store, manage, and
# retrieve tokens from your vgs vault.
#
# in the below example we demonstrate how to store payment card and personally identifiable
# information in a single API call.
#
# see https://www.verygoodsecurity.com/docs/vault/concepts/zero-data
# to learn how to remove any servers handling sensitive information
# 100% from pci scope.

api = vgs.Aliases(config)

# first, let's create a request payload to tokenize our sensitive data.
data = [
    # credit card
    dict(
        # format is used to determine how the stored token is represented
        # see https://www.verygoodsecurity.com/docs/terminology/nomenclature#alias-formats
        # to learn about different formats and representations available
        # to tokenize secured data.
        format="PFPT",
        value="****************",
        # see https://www.verygoodsecurity.com/docs/vault/concepts#classifiers
        # to learn how to classify and tag your data to help secure access and
        # route data compliantly.
        classifiers=["credit-card", "number"],
        storage="PERSISTENT"
    ),
    # card security code
    dict(
        format="UUID",
        value="123",
        classifiers=["credit-card", "csc"],
        # learn how volatile storage allows you to maintain full pci compliance
        # https://www.verygoodsecurity.com/docs/terminology/nomenclature#storage
        storage="VOLATILE"
    ),
    # social security number
    dict(
        format="UUID",
        value="***********",
        classifiers=["pii", "ssn"]
    )
]
log_info("Tokenizing multiple values...")

aliases = api.redact(data=data)

log_info(f"Tokens created{aliases}")

# example of how to find the alias associated to each value
print([
    (record["value"], record["aliases"][0]["alias"])
    for record in aliases
])

# next, let's update the classifiers and re-alias
first_alias = aliases[0]["aliases"][0]["alias"]

api.update(alias=first_alias, data=dict(classifiers=["bank-account", "test-tag"]))

log_info(f"Token {first_alias} updated")

# now let's retrieve it back
revealed = api.reveal(first_alias)

log_info(f"Token {first_alias} retrieved{revealed}")

# finally, let's delete an alias
api.delete(first_alias)

log_info(f"Token {first_alias} deleted")

# now let's fetch it back to ensure it's not usable.
log_info(f"Trying to retrieve deleted token {first_alias}...")
try:
    api.reveal(first_alias)
except vgs.NotFoundException as e:
    log_info("We can no longer fetch the token, it has been deleted: %s" % e)