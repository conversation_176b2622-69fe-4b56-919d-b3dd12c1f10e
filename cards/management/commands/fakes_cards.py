
from django.utils import timezone
from django.core.management.base import BaseCommand
from django.conf import settings
from cards.models import RawSudoCardCallback, SudoCardTransaction, SudoHelper

from accounts.models import Transaction, Escrow, WalletSystem
from main.models import User, DeliveryAddressData, OtherServiceDetail
from main.helper.send_emails import send_email

from cards.tasks import send_default_pin_task

import json
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical


class Command(BaseCommand):
    help = ""

    def handle(self, *args, **kwargs):
                
        failed_trans_list = [3878549]

        for sender_transaction in Transaction.objects.filter(id__in=failed_trans_list):
            escrow_instance = Escrow.objects.get(escrow_id=sender_transaction.escrow_id)
            request_user = sender_transaction.user
            get_card_type = "PHYSICAL"
            ip_addr = escrow_instance.ip_addr
            delivery_address_id = DeliveryAddressData.objects.filter(user=request_user).last().id
            card_brand_choice = "Verve"


            service_user_check = OtherServiceDetail.objects.filter(service_name="CARDS").last()
            cards_user = service_user_check.user
            wallet_instance = WalletSystem.get_wallet(user=request_user, from_wallet_type="COLLECTION")
            receiver_wallet_instance = WalletSystem.get_wallet(user=cards_user, from_wallet_type="COLLECTION")



            create_user_card = SudoHelper.create_old_new_card(
                request_user = request_user,
                card_type = get_card_type,
                ip_addr = ip_addr,
                delivery_address_id = delivery_address_id,
                card_brand = card_brand_choice
            )

            log_info(str(create_user_card))
                
            
            if create_user_card is not None:

                release_to_cards = WalletSystem.release_fund_wallet_pay_buddy_for_receiver(
                    sender_transaction = sender_transaction,
                    sender_user_instance = request_user,
                    buddy_user_instance = cards_user,
                    sender_wallet = wallet_instance,
                    wallet = receiver_wallet_instance,
                    escrow_instance = escrow_instance,
                    customer_reference=None
                )


                card_id = create_user_card.get("data", {}).get('_id')
                if card_id:

                    send_default_pin_task.apply_async(
                        queue="resolvecardpur",
                        kwargs={
                            "card_id": card_id
                        }
                    )


                for email in ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]:
                    email_text = f"User with email: {request_user.email} and names: {request_user.bvn_full_name} has requested for a {get_card_type} card.\n\nPlease confirm"
                    send_email(email=email, passcode=email_text)
                    

            else:

                # Refund Money
                reversal_transaction = Transaction.reverse_bills_airpin_transactions(transaction=sender_transaction, provider_status="FAILED", payload_reversal_reason=create_user_card)


                  





        # reveal_card = SudoHelper.get_card_token(card_id="63f2ab4f29117cdcca0fc4e1")
        # print(reveal_card)


        # for raw_sudo_card in RawSudoCardCallback.objects.filter(auth_header=True):
        #     try:
        #         request_payload = json.loads(raw_sudo_card.payload)
        #     except:
        #         request_payload = eval(raw_sudo_card.payload)
                
        #     request_type = request_payload.get("type")
        #     if request_type == "authorization.request":

        #         unique_reference = request_payload.get("data").get("object").get("_id")
        #         get_transaction = Transaction.objects.filter(unique_reference=unique_reference, transaction_type="CARD_PURCHASE").last()


        #         if get_transaction and unique_reference is not None:
        #             print(get_transaction.id)
        #             to_set_payload = {
        #                 "maskedPan": json.dumps(request_payload.get("data").get("object").get("card").get("maskedPan")),
        #                 "data1": json.dumps(request_payload.get("data").get("object").get("merchant")),
        #                 "data2": json.dumps(request_payload.get("data").get("object").get("terminal")),
        #             }


        #             get_transaction.payload = to_set_payload
        #             print(get_transaction.payload)

        #             get_transaction.save()

        #         else:
        #             pass

        return "DONE"
    



        # for raw_sudo_card in RawSudoCardCallback.objects.all():
        #     try:
        #         request_payload = json.loads(raw_sudo_card.payload)
        #     except:
        #         request_payload = eval(raw_sudo_card.payload)
            
        #     request_type = request_payload.get("type")

        #     raw_sudo_card.request_type = request_type
        #     raw_sudo_card.save()

        #     if request_type == "transaction.refund": 
        #         response = SudoCardTransaction.process_transaction_refund(request_payload=request_payload)
       
        #     else:
        #         pass



        # for raw_sudo_card in RawSudoCardCallback.objects.all():
        #     request_payload = json.loads(raw_sudo_card.payload)
        #     request_type = request_payload.get("type")

        #     if request_type == "authorization.request" and raw_sudo_card.id == 10:

                # receive_trans_created.apply_async(
                #     queue="resolvecardpur",
                #     kwargs={
                #         "request_payload": request_payload,
                #         "raw_data_id": raw_sudo_card.id,
                #         "request_type": request_type,
                #         "use_auth": True
                #     }
                # )
        #     pass

        # return "DONE"