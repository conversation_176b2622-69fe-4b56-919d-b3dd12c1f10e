import requests
import json

from django.db import models
from django.db.models import Q
from django.conf import settings
from django.core.exceptions import ValidationError
from django.contrib.auth.hashers import make_password, check_password

from accounts.models import Transaction, WalletSystem, DebitCreditRecordOnAccount, Escrow, InAppTransactionNotification, OutOfBookTransfer, \
    AccountSystem, SendCommissionScheduler
from accounts.helpers.vfdbank_manager import VFDBank
from liberty_pay.settings import cloud_messaging

from main.models import User
from main.models import DeliverySystem, ConstantTable
from main.helper.utils import get_ip_address
from kyc_app.models import BVNDetail
from datetime import datetime, timedelta
from main.helper.logging_utils import log_info, log_debug, log_warning, log_error, log_critical

# Create your models here.


SUDO_API_KEY = f"{settings.SUDO_API_KEY}"
SUDO_FUNDING_SOURCE_ID = f"{settings.SUDO_FUNDING_SOURCE_ID}"
SUDO_DEBIT_ACCOUNT_ID = f"{settings.SUDO_DEBIT_ACCOUNT_ID}"


class SudoHelper:
    url = settings.SUDO_LIVE_BASE_URL
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": f"Bearer {SUDO_API_KEY}"
    }

    @classmethod
    def device_ip(cls, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR', None)
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[-1].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', None)
        return ip

    @classmethod
    def format_birthdate(cls, birthdate):
        if birthdate:
            date = birthdate.split("-")
            if len(date[0]) == 4:
                return birthdate
            elif len(date[2]) == 4:
                date = datetime.strptime(birthdate, '%d-%m-%Y')
                year = date.year
                month = date.month
                day = date.day
                return f"{year}-{month}-{day}"
            else:
                return ""
        else:
            return ""

    @classmethod
    def create_new_customer_card(cls, user, card_type, ip_address, delivery_address_id, card_brand):
        card_type = card_type.lower()
        bvn_instance = BVNDetail.objects.filter(kyc__user=user, is_verified=True).last()

        if bvn_instance:
            bvn_name = user.bvn_full_name
            bvn_phone = user.phone_number
            bvn_first_name = bvn_instance.bvn_first_name
            bvn_last_name = bvn_instance.bvn_last_name
            bvn_middle_name = bvn_instance.bvn_middle_name if bvn_instance.bvn_middle_name is not None else ""
            bvn_number = bvn_instance.bvn_number
            bvn_birthdate = cls.format_birthdate(bvn_instance.bvn_birthdate)
            bvn_email = user.email
            # address_1 = bvn_instance.bvn_residential_address if bvn_instance.bvn_residential_address else "27 Alara Street" #Address This
            # city = user.nearest_landmark if user.nearest_landmark else "Sabo-Yaba" #Address This
            address_1 = "27 Alara Street, Sabo, Yaba, Lagos"  # Address This
            city = "Yaba"  # Address This
            state = "Lagos"
            country = "NG"
            postal_code = "101212"

            if not bvn_first_name or not bvn_last_name or not bvn_number or not bvn_email or not bvn_phone:
                log_info(f"cannot get {bvn_first_name}, {bvn_last_name}, {bvn_number}, {city}, {bvn_email}, {bvn_phone}", "cards.models.create_new_customer_card")
                return None
            else:
                log_info(
                    f"{bvn_name}, {bvn_first_name}, {bvn_last_name}, {bvn_middle_name}, {bvn_birthdate}, {bvn_number}, {address_1}, {bvn_email}, {bvn_phone}")
                url = f"{cls.url}/customers"
                payload = {
                    "type": "individual",
                    "name": bvn_name,
                    "phoneNumber": bvn_phone,
                    "emailAddress": bvn_email,
                    "individual": {
                        "firstName": bvn_first_name,
                        "lastName": bvn_last_name,
                        "otherNames": bvn_middle_name,
                        "dob": bvn_birthdate,
                        "identity": {
                            "type": "BVN",
                            "number": bvn_number,
                        },
                        "documents": {}
                    },
                    "status": "active",
                    "billingAddress": {
                        "line1": address_1,
                        "line2": "",
                        "city": city,
                        "state": state,
                        "country": country,
                        "postalCode": postal_code
                    }

                }

                try:
                    response = requests.post(url, json=payload, headers=cls.headers)
                    resp = {
                        "status": "success",
                        "data": response.json()
                    }


                except requests.exceptions.RequestException as err:
                    resp = {
                        "status": "success",
                        "data": f"{err}"
                    }

                log_info(str(resp), "cards.models.create_new_customer_card")

                if resp["status"] == "success":
                    raw_cust_status = "success"
                else:
                    raw_cust_status = "failed"

                customer_detail = RawCustomerData.objects.create(
                    user=user,
                    initial_payload=payload,
                    status=raw_cust_status,
                    data=json.dumps(resp)
                )

                if resp["status"] == "success":
                    customer_data = resp["data"]

                    if "statusCode" in customer_data.keys():
                        if customer_data["statusCode"] == 200:
                            customer_business_id = customer_data["data"]["business"]
                            customer_type = customer_data["data"]["type"]
                            customer_phone_number = customer_data["data"]["phoneNumber"]
                            customer_name = customer_data["data"]["name"]
                            customer_email = customer_data["data"]["emailAddress"]
                            customer_status = customer_data["data"]["status"]
                            customer_first_name = customer_data["data"]["individual"]["firstName"]
                            customer_last_name = customer_data["data"]["individual"]["lastName"]
                            customer_other_name = customer_data["data"]["individual"]["otherNames"]
                            customer_date_of_birth = customer_data["data"]["individual"]["dob"]
                            customer_bvn_number = customer_data["data"]["individual"]["identity"]["number"]
                            customer_identity_id = customer_data["data"]["individual"]["identity"]["_id"]
                            customer_individual_id = customer_data["data"]["individual"]["_id"]
                            customer_document_id = customer_data["data"]["individual"]["documents"]["_id"]
                            customer_address_1 = customer_data["data"]["billingAddress"]["line1"]
                            customer_city = customer_data["data"]["billingAddress"]["city"]
                            customer_state = customer_data["data"]["billingAddress"]["state"]
                            customer_country = customer_data["data"]["billingAddress"]["country"]
                            customer_postal_zipcode = customer_data["data"]["billingAddress"]["postalCode"]
                            customer_billing_address_id = customer_data["data"]["billingAddress"]["_id"]
                            customer_created_at = customer_data["data"]["createdAt"]
                            customer_id = customer_data["data"]["_id"]
                            customer_v = customer_data["data"]["__v"]

                            database_customer = SudoCustomer.objects.create(
                                user=user,
                                customer_id=customer_id,
                                customer_business_id=customer_business_id,
                                customer_type=customer_type,
                                customer_phone_number=f"234{customer_phone_number[-10:]}",
                                customer_name=customer_name,
                                customer_email=customer_email,
                                customer_status=customer_status,
                                customer_first_name=customer_first_name,
                                customer_last_name=customer_last_name,
                                customer_other_name=customer_other_name,
                                customer_date_of_birth=customer_date_of_birth,
                                customer_bvn_number=customer_bvn_number,
                                customer_identity_id=customer_identity_id,
                                customer_individual_id=customer_individual_id,
                                customer_document_id=customer_document_id,
                                customer_address_1=customer_address_1,
                                customer_city=customer_city,
                                customer_state=customer_state,
                                customer_country=customer_country,
                                customer_postal_zipcode=customer_postal_zipcode,
                                customer_billing_address_id=customer_billing_address_id,
                                customer_created_at=customer_created_at,
                                customer_v=customer_v,
                                customer_ip_address=ip_address
                            )

                            log_info("customer created and added to database", "cards.models.create_new_customer_card")
                            ####################################################
                            # On production, remove this map customer card and return customer

                            # if settings.ENVIRONMENT == "development":
                            return database_customer
                            # else:
                            #     create_card = cls.map_customer_to_card(
                            #         customer=database_customer,
                            #         funding_source_id=SUDO_FUNDING_SOURCE_ID,
                            #         card_type=card_type,
                            #         ip_address=ip_address,
                            #         delivery_address_id=delivery_address_id,
                            #         card_brand = card_brand
                            #     )

                            #     if create_card is not None:
                            #         print("card created for new customer")
                            #         return create_card
                            #     else:
                            #         return None


                        else:
                            log_info("could not create customer", "cards.models.create_new_customer_card")
                            return None
                    else:
                        log_error("an error occured", "cards.models.create_new_customer_card")
                        return None
                else:
                    log_error("second error occured", "cards.models.create_new_customer_card")
                    return None

        else:
            return None

    @classmethod
    def map_customer_to_card(cls, customer, funding_source_id, card_type, ip_address, delivery_address_id, card_brand, card_pan_number=None,
                             physical_card: 'PhysicalCard' = None):

        if settings.ENVIRONMENT == "development":
            return True

        url = f"{cls.url}/cards"
        customer_id = customer.customer_id

        payload = {
            "customerId": customer_id,
            "type": card_type.lower(),
            "currency": "NGN",
            "issuerCountry": "NGA",
            "status": "active",
            "brand": card_brand,
            "spendingControls": {
                "allowedCategories": [],
                "blockedCategories": [],
                "channels": {
                    "atm": True,
                    "pos": True,
                    "web": True,
                    "mobile": True
                },
                "spendingLimits": [
                    {
                        "interval": "daily",
                        "amount": ConstantTable.get_constant_table_instance().default_card_spending_limit
                    }
                ]
            },
            "debitAccountId": "",
            "sendPINSMS": True,
            "debitAccountId": SUDO_DEBIT_ACCOUNT_ID,
            "fundingSourceId": funding_source_id
        }

        if card_type == "virtual":
            is_physical = False
        else:
            if not card_pan_number and not physical_card:
                return None

            payload["number"] = card_pan_number
            is_physical = True

        try:
            response = requests.post(url, json=payload, headers=cls.headers)
            resp = {
                "status": "success",
                "data": response.json()
            }

            card_detail = RawVirtualPhysicalCardData.objects.create(
                initial_payload=payload,
                customer=customer,
                card_type=card_type,
                data=json.dumps(resp),
            )

        except requests.exceptions.RequestException as err:
            resp = {
                "status": "success",
                "data": f"{err}"
            }

            card_detail = RawVirtualPhysicalCardData.objects.create(
                initial_payload=payload,
                customer=customer,
                card_type=card_type,
                data=json.dumps(resp),
                status="FAILED"
            )

        log_info(resp, "THIS IS CARD RESPONSE")
        if resp["status"] == "success":
            map_card = resp["data"]

            if map_card["statusCode"] == 200:
                if is_physical == True:
                    physical_card.customer = customer
                    physical_card.card_status = "ASSIGNED"
                    physical_card.date_assigned = datetime.now()
                    physical_card.save()

                CustomerCardDetail.create_customer_card(
                    customer, map_card, ip_address, delivery_address_id
                )

                # card_id = map_card["data"]["_id"]
                return map_card
            else:
                return None
        else:
            return None

    @classmethod
    def create_old_new_card(cls, request_user, card_type, ip_addr, card_brand, delivery_address_id=None):
        customer = SudoCustomer.objects.filter(user=request_user).last()
        if not customer:
            customer = cls.create_new_customer_card(
                user=request_user,
                card_type=card_type,
                ip_address=ip_addr,
                delivery_address_id=delivery_address_id,
                card_brand=card_brand
            )

        if customer and card_type == "VIRTUAL":
            map_user_card = cls.map_customer_to_card(customer, SUDO_FUNDING_SOURCE_ID, card_type, ip_addr, delivery_address_id, card_brand)

            if map_user_card:
                card_id = map_user_card["data"]["_id"]

                RequestCardData.objects.create(
                    customer=customer,
                    card_type=card_type,
                    card_brand=card_brand,
                    delivery_address_id=delivery_address_id,
                    date_requested=datetime.now(),
                    assigned=True,
                    card_id=card_id
                )

                from cards.tasks import send_default_pin_task

                send_default_pin_task.apply_async(
                    queue="resolvecardpur",
                    kwargs={
                        "card_id": card_id
                    }
                )

                return customer
            else:
                return None
        else:
            return customer

    @classmethod
    def update_card_settings(cls, card_id, new_limit, freeze_status):

        url = f"{cls.url}/cards/{card_id}"

        payload = {
            "status": freeze_status,
            "spendingControls": {
                "channels": {
                    "atm": True,
                    "pos": True,
                    "web": True,
                    "mobile": True
                },
                "allowedCategories": [],
                "blockedCategories": [],
                "spendingLimits": [
                    {
                        "interval": "daily",
                        "amount": new_limit
                    }
                ]
            }
        }

        log_info(str(payload), "cards.models.update_card_settings")

        try:
            response = requests.put(url, json=payload, headers=cls.headers)
            resp = response.json()

            if response.status_code and resp.get("statusCode") == 200:
                resp = {
                    "status": True,
                    "data": f"{resp}",
                }
            else:
                resp = {
                    "status": False,
                    "data": f"{resp}",
                }


        except requests.exceptions.RequestException as err:
            resp = {
                "status": False,
                "data": f"{err}"
            }

        return resp

    @classmethod
    def send_default_card_pin(cls, card_id):
        url = f"{cls.url}/cards/{card_id}/send-pin"

        try:
            response = requests.put(url, headers=cls.headers)
            resp = response.json()
            log_info(str(resp), "cards.models.send_default_card_pin")

            if response.status_code and resp.get("statusCode") == 200:
                resp = {
                    "status": True,
                    "data": f"{resp}",
                }
            else:
                resp = {
                    "status": False,
                    "data": f"{resp}",
                }


        except requests.exceptions.RequestException as err:
            resp = {
                "status": False,
                "data": f"{err}"
            }

        return resp

    @classmethod
    def change_card_pin(cls, card_id, old_pin, new_pin):

        url = f"{cls.url}/cards/{card_id}/pin"
        payload = {
            "oldPin": old_pin,
            "newPin": new_pin
        }

        try:
            response = requests.put(url, json=payload, headers=cls.headers)
            resp = response.json()
            log_info(str(resp), "cards.models.change_card_pin")
            log_info(str(response.status_code), "cards.models.change_card_pin")

            if response.status_code and resp.get("statusCode") == 200:
                resp = {
                    "status": True,
                    "data": f"{resp}",
                }
            else:
                resp = {
                    "status": False,
                    "data": f"{resp}",
                }

        except requests.exceptions.RequestException as err:
            resp = {
                "status": False,
                "data": f"{err}"
            }

        return resp

    @classmethod
    def get_single_authorization(cls, auth_id):

        url = f"{cls.url}/cards/authorizations/{auth_id}"

        log_info(str(url), "cards.models.get_single_authorization")

        try:
            response = requests.get(url, headers=cls.headers)
            resp = response.json()

            if response.status_code and resp.get("statusCode") == 200:
                resp = {
                    "status": True,
                    "data": resp
                }
            else:
                resp = {
                    "status": False,
                    "data": resp
                }

        except requests.exceptions.RequestException as err:
            resp = {
                "status": False,
                "data": f"{err}"
            }

        return resp

    ####################################################################################################################################################
    ####################################################################################################################################################

    @classmethod
    def get_all_customers(cls, page, limit):
        url = f"{cls.url}/customers?page={page}&limit={limit}"
        response = requests.get(url, headers=cls.headers)
        return response.json()

    @classmethod
    def get_single_customer(cls, customer_id):
        url = f"{cls.url}/customers/{customer_id}"
        response = requests.get(url, headers=cls.headers)
        return response.json()

    @classmethod
    def get_all_accounts(cls, page, limit, currency, type):
        url = f"{cls.url}/accounts?page={page}&limit={limit}&currency={currency}&type={type}"
        response = requests.get(url, headers=cls.headers)
        return response.json()

    @classmethod
    def get_all_cards(cls, page, limit):
        url = f"{cls.url}/cards?page={page}&limit={limit}"
        response = requests.get(url, headers=cls.headers)
        return response.json()

    @classmethod
    def get_single_user_cards(cls, customer_id, page, limit):
        url = f"{cls.url}/cards/customer/{customer_id}?page={page}&limit={limit}"
        response = requests.get(url, headers=cls.headers)
        return response.json()

    @classmethod
    def get_single_card(cls, card_id):
        url = f"{cls.url}/cards/{card_id}"
        response = requests.get(url, headers=cls.headers)
        return response.json()

    @classmethod
    def reveal_or_hide_card_details(cls, card_id):
        url = f"{cls.url}/cards/{card_id}?reveal=true"
        response = requests.get(url, headers=cls.headers)
        return response.json()

    @classmethod
    def get_card_token(cls, card_id):
        url = f"{cls.url}/cards/{card_id}/token"
        response = requests.get(url, headers=cls.headers)
        return response.json()

    @classmethod
    def encrypt_pan_num(cls, pan_number):
        new_hash = make_password(pan_number)
        return new_hash


##################################################################################################################################################
##################################################################################################################################################


class RawCustomerData(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, related_name="raw_card_customer_data", null=True, blank=True)
    initial_payload = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=200, null=True, blank=True)
    data = models.TextField()
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)


class SudoCustomer(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="sudo_customer", null=True, blank=True)
    customer_id = models.CharField(max_length=400, blank=True, null=True)
    customer_business_id = models.CharField(max_length=400, blank=True, null=True)
    customer_type = models.CharField(max_length=400, blank=True, null=True)
    customer_phone_number = models.CharField(max_length=400, blank=True, null=True)
    customer_name = models.CharField(max_length=400, blank=True, null=True)
    customer_email = models.EmailField(unique=True)
    customer_status = models.CharField(max_length=400, blank=True, null=True)
    customer_first_name = models.CharField(max_length=400, blank=True, null=True)
    customer_last_name = models.CharField(max_length=400, blank=True, null=True)
    customer_other_name = models.CharField(max_length=400, blank=True, null=True)
    customer_date_of_birth = models.CharField(max_length=400, blank=True, null=True)
    customer_bvn_number = models.CharField(max_length=400, blank=True, null=True)
    customer_identity_id = models.CharField(max_length=400, blank=True, null=True)
    customer_individual_id = models.CharField(max_length=400, blank=True, null=True)
    customer_document_id = models.CharField(max_length=400, blank=True, null=True)
    customer_address_1 = models.CharField(max_length=400, blank=True, null=True)
    customer_city = models.CharField(max_length=400, blank=True, null=True)
    customer_state = models.CharField(max_length=400, blank=True, null=True)
    customer_country = models.CharField(max_length=400, default="Nigeria")
    customer_postal_zipcode = models.CharField(max_length=400, blank=True, null=True)
    customer_billing_address_id = models.CharField(max_length=400, blank=True, null=True)
    customer_v = models.CharField(max_length=400, blank=True, null=True)
    customer_created_at = models.DateTimeField(null=True, blank=True)
    customer_ip_address = models.CharField(max_length=400, blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.user.email if self.user else self.customer_email

    @property
    def get_customer_full_name(self):
        return f"{self.customer_first_name} {self.customer_last_name}"


class PhysicalCard(models.Model):
    CARD_STATUS = [
        ("ASSIGNED", "ASSIGNED"),
        ("PENDING", "PENDING"),
        ("NOT_ASSIGNED", "NOT_ASSIGNED"),
    ]

    CARD_BRAND = [
        ("Verve", "Verve"),
        ("Visa", "Visa"),
        ("MasterCard", "MasterCard"),
    ]

    customer = models.ForeignKey(SudoCustomer, on_delete=models.CASCADE, null=True, blank=True)
    pan_number = models.CharField(max_length=100)
    card_first_six = models.CharField(max_length=100, blank=True, null=True)
    card_last_four = models.CharField(max_length=100, blank=True, null=True)
    card_expiry_month = models.CharField(max_length=100, blank=True, null=True)
    card_expiry_year = models.CharField(max_length=100, blank=True, null=True)
    card_status = models.CharField(max_length=200, choices=CARD_STATUS, default="NOT_ASSIGNED")
    # card_brand = models.CharField(max_length=200, choices=CARD_BRAND, default="Verve")
    date_assigned = models.DateTimeField(null=True, blank=True)
    # is_dispatched = models.BooleanField(default=False)
    # is_user_mapped = models.BooleanField(default=False)
    is_delivered = models.BooleanField(default=False)
    date_delivered = models.DateTimeField(null=True, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    # class Meta:
    #     unique_together = ('pan_number', 'card_expiry_month', 'card_expiry_year')

    def clean(self):
        if self.is_delivered and not self.date_delivered:
            raise ValidationError('Must Enter Date Delivered if is_delivered is True')

        if self.date_delivered and not self.is_delivered:
            raise ValidationError('Must Enter is_delivered if Date Delivered is True')

    def save(self, *args, **kwargs):
        # 5061360204389618751
        # 506136
        # 8751
        # 11/25

        if settings.ENVIRONMENT == "development":
            self.pan_number = SudoHelper.encrypt_pan_num(self.pan_number)

        super(PhysicalCard, self).save(*args, **kwargs)

    @classmethod
    def map_physical_card_start(cls, request_user, card_pan, card_first_six, card_last_four, card_expiry_month, card_expiry_year, ip_addr,
                                unique_reference):
        # get_entered_pan_hash = SudoHelper.encrypt_pan_num(pan_number=card_pan)
        # & Q(pan_number=get_entered_pan_hash)
        # print(card_expiry_month)
        # print(card_expiry_year)

        filter_query = Q(card_first_six=card_first_six) & Q(card_last_four=card_last_four) & Q(card_expiry_month=card_expiry_month) & Q(
            card_expiry_year=card_expiry_year) & Q(pan_number=card_pan)

        get_request_object = RequestCardData.objects.filter(customer__user=request_user, assigned=False, card_type="PHYSICAL",
                                                            unique_reference=unique_reference).last()

        physical_card = PhysicalCard.objects.exclude(card_status="ASSIGNED").filter(card_status="NOT_ASSIGNED").filter(filter_query).last()

        if not physical_card:
            response = {
                "error": "56",
                "message": "No Physical Card Found"
            }
            return response

        if not get_request_object:
            response = {
                "error": "56",
                "message": "No Request Data Found"
            }
            return response

        if settings.ENVIRONMENT == "development":
            import random
            import string

            card_id = ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(24))

            get_request_object.assigned = True
            get_request_object.assigned_card = physical_card
            get_request_object.card_id = card_id
            get_request_object.save()

            physical_card.customer = get_request_object.customer
            physical_card.card_status = "ASSIGNED"
            physical_card.date_assigned = datetime.now()
            physical_card.save()

            CustomerCardDetail.objects.create(
                customer=get_request_object.customer,
                card_creation_message="Card mapped successfully.",
                account="63f2ab4529117cdcca0fc4cb",
                funding_source="6331dc9d55b58f87888f51fb",
                card_type=get_request_object.card_type.lower(),
                card_brand=get_request_object.card_brand,
                card_masked_pan=f"{card_first_six}****{card_last_four}",
                card_expiry_month=card_expiry_month,
                card_expiry_year=card_expiry_year,
                card_status="active",
                card_id=card_id,
                ip_address=ip_addr,
                delivery_address_id=get_request_object.delivery_address_id
            )

            response = {
                "status": "00",
                "message": "Card Successfully Registered",
                "card_id": card_id
            }

            return response

        # GET IP ADDRESS

        map_user_card = SudoHelper.map_customer_to_card(
            customer=get_request_object.customer,
            funding_source_id=SUDO_FUNDING_SOURCE_ID,
            card_type="PHYSICAL",
            ip_address=ip_addr,
            delivery_address_id=get_request_object.delivery_address_id,
            card_brand=get_request_object.card_brand,
            card_pan_number=card_pan,
            physical_card=physical_card,
        )

        log_info(str(map_user_card), "cards.models.map_physical_card_start")

        if map_user_card is not None:
            # card_id = map_user_card["_id"]
            card_id = map_user_card["data"]["_id"]

            get_request_object.assigned = True
            get_request_object.assigned_card = physical_card
            get_request_object.card_id = card_id
            get_request_object.save()

            # physical_card.customer = get_request_object.customer
            # physical_card.card_status = "ASSIGNED"
            # physical_card.date_assigned = datetime.now()
            # physical_card.save()

            if card_id:
                from cards.tasks import send_default_pin_task

                send_default_pin_task.apply_async(
                    queue="resolvecardpur",
                    kwargs={
                        "card_id": card_id
                    }
                )

            response = {
                "status": "00",
                "message": "Card Successfully Registered",
                "card_id": card_id
            }


        else:
            response = {
                "error": "565",
                "message": "Could not assign card at the moment, please again later"
            }

        return response


class RequestCardData(models.Model):
    customer = models.ForeignKey(SudoCustomer, on_delete=models.CASCADE, null=True, blank=True)
    assigned_card = models.ForeignKey(PhysicalCard, on_delete=models.SET_NULL, blank=True, null=True)
    assigned = models.BooleanField(default=False)
    card_type = models.CharField(max_length=200)
    card_brand = models.CharField(max_length=200)
    card_id = models.CharField(max_length=200, null=True, blank=True)
    delivery_address_id = models.CharField(max_length=200)
    unique_reference = models.CharField(max_length=100, null=True, blank=True)
    card_given_out = models.BooleanField(default=False)
    date_requested = models.DateTimeField()
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class CustomerCardDetail(models.Model):
    customer = models.ForeignKey(SudoCustomer, on_delete=models.CASCADE)
    card_type = models.CharField(max_length=400, blank=True, null=True)
    card_creation_message = models.CharField(max_length=400, blank=True, null=True)
    account = models.CharField(max_length=400, blank=True, null=True)
    funding_source = models.CharField(max_length=400, blank=True, null=True)
    daily_limit = models.FloatField(default=30000)
    card_brand = models.CharField(max_length=400, blank=True, null=True)
    card_masked_pan = models.CharField(max_length=400, blank=True, null=True)
    card_expiry_month = models.CharField(max_length=400, blank=True, null=True)
    card_expiry_year = models.CharField(max_length=400, blank=True, null=True)
    card_status = models.CharField(max_length=400, blank=True, null=True)
    card_id = models.CharField(max_length=400, blank=True, null=True)
    delivery_address_id = models.CharField(max_length=100, blank=True, null=True)
    is_activated = models.BooleanField(default=True)
    is_frozen = models.BooleanField(default=False)
    is_canceled = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    pin_changed = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)
    ip_address = models.CharField(max_length=400, blank=True, null=True)

    def __str__(self) -> str:
        return self.customer.customer_email

    @staticmethod
    def create_customer_card(customer, card_detail, ip_address, delivery_address_id=None):
        if customer:
            card_creation_message = card_detail.get('message')
            account = card_detail.get("data").get('account')
            funding_source = card_detail.get("data").get('fundingSource')
            card_type = card_detail.get("data").get('type')
            card_brand = card_detail.get("data").get('brand')
            card_masked_pan = card_detail.get("data").get('maskedPan')
            card_expiry_month = card_detail.get("data").get('expiryMonth')
            card_expiry_year = card_detail.get("data").get('expiryYear')
            card_status = card_detail.get("data").get('status')
            card_id = card_detail.get("data").get('_id')

            CustomerCardDetail.objects.create(
                customer=customer,
                card_creation_message=card_creation_message,
                account=account,
                funding_source=funding_source,
                card_type=card_type,
                card_brand=card_brand,
                card_masked_pan=card_masked_pan,
                card_expiry_month=card_expiry_month,
                card_expiry_year=card_expiry_year,
                card_status=card_status,
                card_id=card_id,
                ip_address=ip_address,
                delivery_address_id=delivery_address_id
            )
        return True


class RawVirtualPhysicalCardData(models.Model):
    CARD_TYPE_CHOICE = [
        ("PHYSICAL", "PHYSICAL"),
        ("VIRTUAL", "VIRTUAL"),
    ]

    customer = models.ForeignKey(SudoCustomer, on_delete=models.CASCADE, null=True, blank=True)
    card_type = models.CharField(max_length=100, default="VIRTUAL")
    status = models.CharField(max_length=100, default="SUCCESSFUL")
    initial_payload = models.TextField(null=True, blank=True)
    data = models.TextField()
    date_created = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.customer if self.customer else self.card_type


class RawSudoCardCallback(models.Model):
    payload = models.TextField()
    ip_addr = models.CharField(max_length=100, null=True, blank=True)
    auth_header = models.BooleanField(default=False)
    returned_response = models.TextField(null=True, blank=True)
    new_returned_response = models.TextField(null=True, blank=True)
    request_type = models.CharField(max_length=150, null=True, blank=True)
    other_service_response = models.TextField(null=True, blank=True)
    date_added = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)


class SudoCardAuthorizationBegin(models.Model):
    environment = models.CharField(max_length=400, blank=False, null=False)
    business = models.CharField(max_length=400, blank=False, null=False)
    data = models.TextField()
    pending_request_amount = models.CharField(max_length=400, blank=False, null=False)
    authorization_type = models.CharField(max_length=400, blank=False, null=False)
    created_at = models.DateTimeField()
    customer_id = models.CharField(max_length=400, blank=False, null=False)
    account_id = models.CharField(max_length=400, blank=False, null=False)
    card_id = models.CharField(max_length=400, blank=False, null=False)
    funding_source_id = models.CharField(max_length=400, blank=False, null=False)
    authorization_id = models.CharField(max_length=400, blank=False, null=False)
    card_brand = models.CharField(max_length=400, blank=False, null=False)
    card_type = models.CharField(max_length=400, blank=False, null=False)
    amount = models.CharField(max_length=400, blank=False, null=False)
    fee = models.CharField(max_length=400, blank=False, null=False)
    vat = models.CharField(max_length=400, blank=False, null=False)
    merchant_amount = models.CharField(max_length=400, blank=False, null=False)
    merchant = models.TextField()
    terminal = models.TextField()
    verification = models.TextField()
    transaction_metadata = models.TextField()
    pending_request = models.TextField()
    data_id = models.CharField(max_length=400, blank=False, null=False)
    fee_details = models.TextField()
    reference = models.CharField(max_length=400, blank=True, null=True)
    date = models.DateTimeField(auto_now_add=True)
    date_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.authorization_id

    @staticmethod
    def authorize_card(data):
        objects = data.get('data').get('object')
        environment = data.get('environment')
        business = data.get('business')
        data_list = data.get('data')
        authorization_type = data.get('type')
        created_at = objects.get('createdAt')
        customer_id = objects.get('customer').get('_id')
        account_id = objects.get('account').get('_id')
        card_id = objects.get('card').get('_id')
        funding_source_id = objects.get('card').get('fundingSource').get('_id')
        card_type = objects.get('card').get('type')
        card_brand = objects.get('card').get('brand')
        authorization_id = data.get('_id')
        amount = objects.get('amount')
        fee = objects.get('fee')
        vat = objects.get('vat')
        merchant_amount = objects.get('merchantAmount')
        merchant = objects.get('merchant')
        terminal = objects.get('terminal')
        transaction_metadata = objects.get('transactionMetadata')
        pending_request = objects.get('pendingRequest')
        verification = objects.get('verification')
        pending_request_amount = objects.get('pendingRequest').get('amount')
        data_id = data_list.get('_id')
        fee_details = objects.get('feeDetails')
        reference = objects.get('transactionMetadata').get('reference')

        authorize_transaction = SudoCardAuthorizationBegin.objects.create(
            environment=environment,
            business=business,
            data=data_list,
            pending_request_amount=pending_request_amount,
            authorization_type=authorization_type,
            created_at=created_at,
            customer_id=customer_id,
            account_id=account_id,
            card_id=card_id,
            funding_source_id=funding_source_id,
            authorization_id=authorization_id,
            card_brand=card_brand,
            card_type=card_type,
            amount=amount,
            fee=fee,
            vat=vat,
            merchant_amount=merchant_amount,
            merchant=merchant,
            terminal=terminal,
            verification=verification,
            transaction_metadata=transaction_metadata,
            pending_request=pending_request,
            data_id=data_id,
            fee_details=fee_details,
            reference=reference
        )
        return True


class SudoCardTransaction(models.Model):
    environment = models.CharField(max_length=150, blank=False, null=False)
    transaction_type = models.CharField(max_length=150, blank=False, null=False)
    created_at = models.CharField(max_length=150, blank=False, null=False)
    transaction_created_id = models.CharField(max_length=150, blank=False, null=False)
    customer_id = models.CharField(max_length=150, blank=False, null=False)
    account_id = models.CharField(max_length=150, blank=False, null=False)
    card_id = models.CharField(max_length=150, blank=False, null=False)
    authorization_id = models.CharField(max_length=150, blank=False, null=False)
    amount = models.CharField(max_length=150, blank=False, null=False)
    fee = models.CharField(max_length=150, blank=False, null=False)
    vat = models.CharField(max_length=150, blank=False, null=False)
    merchant_id = models.CharField(max_length=150, blank=False, null=False)
    merchant = models.TextField(blank=False, null=False)
    terminal = models.TextField(blank=False, null=False)
    transaction_metadata = models.TextField(blank=False, null=False)
    data_id = models.CharField(max_length=150, blank=False, null=False)
    fee_details = models.TextField(blank=False, null=False)
    reference = models.CharField(max_length=150, blank=True, null=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.authorization_id

    @classmethod
    def initialize_card_payment(
            cls, request_user, cards_user, total_amount_charged, ip_addr,
            deduct_balance, wallet_instance, receiver_wallet_instance,
            payload_saved, transfer_type, unique_reference
    ):

        narration = transfer_type
        liberty_commission = 0.00
        is_beneficiary = False
        is_recurring = False

        # Send Money
        user_balance_before = deduct_balance["balance_before"]

        user_balance_after = WalletSystem.get_balance_after(
            user=request_user,
            balance_before=user_balance_before,
            total_amount=total_amount_charged,
            is_credit=False
        )

        escrow_instance = WalletSystem.move_to_escrow_send_pay_buddy(
            user=request_user,
            balance_before=user_balance_before,
            balance_after=user_balance_after,
            from_wallet_id=wallet_instance.wallet_id,
            to_wallet_id=receiver_wallet_instance.wallet_id,
            from_wallet_type="COLLECTION",
            to_wallet_type="COLLECTION",
            transfer_type=transfer_type,
            amount=total_amount_charged,
            to_nuban=cards_user.phone_number,
            to_account_name=cards_user.bvn_full_name if cards_user.bvn_first_name else cards_user.full_name,
            liberty_commission=liberty_commission,
            total_amount_charged=total_amount_charged,
            narration=narration,
            is_beneficiary=is_beneficiary,
            is_recurring=is_recurring,
            debit_credit_record_id=deduct_balance.get("debit_credit_record_id"),
            ip_addr=ip_addr,
            customer_reference=unique_reference
        )

        payload_saved.escrow_created = True
        payload_saved.save()

        send_money_to_buddy = WalletSystem.fund_wallet_pay_buddy_and_hold_for_receiver(
            sender_user_instance=request_user,
            buddy_user_instance=cards_user,
            sender_wallet=wallet_instance,
            receiver_wallet_id=receiver_wallet_instance.wallet_id,
            receiver_wallet_type=receiver_wallet_instance.wallet_type,
            amount=total_amount_charged,
            escrow_instance=escrow_instance
        )

        return {
            "escrow_instance": escrow_instance,
            "sender_transaction": send_money_to_buddy
        }

    @classmethod
    def finish_transaction(cls, request_payload):
        # if raw_sudo_card_inst iNone:
        # if 
        user_email = request_payload.get("data").get("object").get("metadata").get("user_email")
        transaction_id = request_payload.get("data").get("object").get("metadata").get("transaction_id")
        liberty_reference = request_payload.get("data").get("object").get("metadata").get("liberty_reference")
        debit_credit_record_id = request_payload.get("data").get("object").get("metadata").get("record_id")
        unique_reference = request_payload.get("data").get("object").get("authorization")
        bypass = request_payload.get("data").get("bypass")

        # else:
        #     raw_sudo_card_inst_response = json.loads(raw_sudo_card_inst.payload)

        #     user_email = raw_sudo_card_inst_response.get("data").get("metadata").get("user_email")
        #     transaction_id = raw_sudo_card_inst_response.get("data").get("metadata").get("transaction_id")
        #     liberty_reference = raw_sudo_card_inst_response.get("data").get("metadata").get("liberty_reference")
        #     debit_credit_record_id = raw_sudo_card_inst_response.get("data").get("metadata").get("record_id")
        #     unique_reference = request_payload.get("data").get("object").get("_id")
        #     bypass = request_payload.get("data").get("bypass")

        # former_unique_ref = request_payload.get("data").get("object").get("transactionMetadata").get("reference")

        # get_trans = Transaction.objects.filter(unique_reference=former_unique_ref).last()

        # if get_trans:
        #     get_trans.unique_reference = unique_reference
        #     get_trans.save()

        user = User.objects.filter(email=user_email).last()
        cards_user = User.objects.filter(email=settings.CARDS_USER_EMAIL).last()

        if user and cards_user:

            cards_user_wallet = WalletSystem.objects.filter(user=cards_user, wallet_type="COLLECTION").first()

            get_transaction = Transaction.objects.filter(unique_reference=unique_reference)
            get_debit_record = DebitCreditRecordOnAccount.objects.filter(id=int(debit_credit_record_id), entry="DEBIT", user=user).first()
            log_info(f"get_transaction: {get_transaction}", "cards.models.finish_transaction")
            log_info(f"get_debit_record: {get_debit_record}", "cards.models.finish_transaction")

            if get_transaction and get_debit_record and cards_user_wallet:

                get_user_trans = get_transaction.filter(user=user).first()
                get_cards_user_trans = get_transaction.filter(user=cards_user).first()
                user_wallet = WalletSystem.objects.filter(user=user, wallet_type="COLLECTION").first()

                log_info(user_wallet, "user_wallet")
                log_info(get_user_trans, "get_user_trans")
                log_info(liberty_reference, "liberty_reference")
                log_info(get_user_trans.liberty_reference, "get_user_trans_liberty_reference")
                log_info(transaction_id, "transaction_id")
                log_info(get_user_trans.transaction_id, "get_user_trans_get_user_trans.transaction_id")

                if get_user_trans and user_wallet and liberty_reference == str(get_user_trans.liberty_reference) and transaction_id == str(
                        get_user_trans.transaction_id):

                    ################################################################################
                    # get authorization
                    auth_response = SudoHelper.get_single_authorization(auth_id=unique_reference)
                    if auth_response["status"] == True:

                        approved_resp = auth_response.get("data").get("data").get("approved")
                        approved_status = auth_response.get("data").get("data").get("status")
                        approved_amount = auth_response.get("data").get("data").get("amount")

                        amount_without_liberty_own_comm = (get_user_trans.amount - get_user_trans.liberty_commission) + get_user_trans.provider_fee

                        log_info(str(amount_without_liberty_own_comm), "cards.models.finish_transaction")

                        log_info(str(approved_resp))
                        log_info(str(approved_status))
                        log_info(str(approved_amount))

                        if (
                                approved_resp == True and approved_status == "approved" and approved_amount == amount_without_liberty_own_comm) or bypass == True:
                            pass
                        else:
                            return {
                                "statusCode": 400,
                                "responseCode": "99",
                                "data": {
                                    "message": "Could not fetch auth data"
                                }
                            }

                    ################################################################################

                    receiver_trans_type = "SETTLE_CARDS_PURCHASE"
                    settling_amount = (get_debit_record.amount - get_user_trans.liberty_commission) + get_user_trans.provider_fee
                    bank_commission = get_user_trans.liberty_commission - get_user_trans.provider_fee

                    get_debit_record.transaction_instance_id = get_user_trans.transaction_id
                    get_debit_record.save()

                    escrow_instance = Escrow.objects.filter(escrow_id=get_user_trans.escrow_id).first()

                    if get_cards_user_trans:
                        receiver_transaction = get_cards_user_trans
                        receiver_liberty_reference = get_cards_user_trans.liberty_reference

                    else:

                        receiver_liberty_reference = Transaction.create_liberty_reference(suffix="SETTLECARDPUR")

                        card_user_balance_before = cards_user_wallet.available_balance
                        card_user_balance_after = WalletSystem.get_balance_after(
                            user=cards_user,
                            balance_before=card_user_balance_before,
                            total_amount=settling_amount,
                            is_credit=True
                        )

                        # Create transaction for Receiver
                        receiver_transaction = Transaction.objects.create(
                            user=cards_user,
                            wallet_id=cards_user_wallet.wallet_id,
                            wallet_type=cards_user_wallet.wallet_type,
                            transaction_type=receiver_trans_type,
                            transaction_sub_type=get_user_trans.transaction_sub_type,
                            amount=settling_amount,
                            liberty_reference=receiver_liberty_reference,
                            total_amount_received=settling_amount,
                            escrow_id=escrow_instance.escrow_id,
                            source_account_name=user.bvn_full_name,
                            source_wallet_id=get_user_trans.wallet_id,
                            source_wallet_type=get_user_trans.wallet_type,
                            narration=escrow_instance.narration,
                            status="PENDING",
                            transaction_leg="EXTERNAL",
                            balance_before=card_user_balance_before,
                            balance_after=card_user_balance_after,
                            lotto_agent_user_id=escrow_instance.lotto_agent_user_id,
                            lotto_agent_user_phone=escrow_instance.lotto_agent_user_phone,
                            unique_reference=unique_reference,
                        )

                    get_card_user_debit_record = DebitCreditRecordOnAccount.objects.filter(
                        transaction_instance_id=receiver_transaction.transaction_id, entry="CREDIT", user=cards_user).first()
                    log_info(get_card_user_debit_record, "get_card_user_debit_record")
                    log_info(receiver_transaction, "receiver_transaction_status")

                    if receiver_transaction.status != "SUCCESSFUL" and not get_card_user_debit_record:

                        # fund wallet
                        fund_buddy_balance = WalletSystem.fund_balance(
                            user=cards_user,
                            wallet=cards_user_wallet,
                            amount=settling_amount,
                            trans_type=receiver_trans_type,
                            transaction_instance_id=receiver_transaction.transaction_id,
                        )

                        cards_debit_credit_record = fund_buddy_balance.get("record")

                        get_user_trans.status = "SUCCESSFUL"
                        get_user_trans.provider_status = "SUCCESSFUL"
                        get_user_trans.save()

                        receiver_transaction.status = "SUCCESSFUL"
                        receiver_transaction.provider_status = "SUCCESSFUL"
                        receiver_transaction.debit_credit_record_id = cards_debit_credit_record.id if cards_debit_credit_record else None

                        receiver_transaction.save()

                        ##########################################################################################
                        # SEND OUT APP NOTIFICATION
                        not_token = user.firebase_key
                        not_title = "Transaction Successful"
                        not_body = f"You have successfully performed a CARDS PURCHASE of N{get_user_trans.amount}. COMM - {get_user_trans.liberty_commission}"
                        not_data = {"amount_sent": f"{get_user_trans.amount}", "available_balance": f"{user_wallet.available_balance}"}

                        send_out_notification = cloud_messaging.send_broadcast(
                            token=not_token,
                            title=not_title,
                            body=not_body,
                            data=not_data
                        )

                        InAppTransactionNotification.create_in_app_transaction_notification(
                            user=user,
                            title=not_title,
                            message_body=not_body
                        )

                        ############################################################################################################################
                        # DEBIT SENDER SMS

                        WalletSystem.transaction_alert_notfication_manager(
                            user=user,
                            amount=float(get_user_trans.amount),
                            cr_dr="DR",
                            narration=f"{escrow_instance.narration}----CARDS PURCHASE",
                            from_wallet_type=user_wallet.wallet_type,
                            transaction_instance_id=get_user_trans.transaction_id
                        )

                        if bank_commission > 0:
                            # Send Commission Scheduler
                            SendCommissionScheduler.objects.create(
                                user=user, wallet_id=user_wallet.wallet_id, wallet_type=user_wallet.wallet_type, amount=float(bank_commission), provider="VFD",
                                transaction_commission_id=str(get_user_trans.transaction_id), transfer_leg="CARD_PUR_COMM",
                                escrow_id=escrow_instance.escrow_id, transaction_sub_type="CARD_PUR_COMM"
                            )
                            # send_commission = WalletSystem.pay_commission_to_liberty(
                            #     user_id=user.id,
                            #     wallet_id=user_wallet.wallet_id,
                            #     wallet_type=user_wallet.wallet_type,
                            #     liberty_commission=bank_commission,
                            #     from_provider_type="VFD",
                            #     transaction_commission_id=get_user_trans.transaction_id,
                            #     transfer_leg="CARD_PUR_COMM",
                            #     get_escrow_id=escrow_instance.escrow_id,
                            #     transaction_sub_type="CARD_PUR_COMM"
                            # )

                        response = {
                            "statusCode": 200,
                            "responseCode": "00",
                            "data": {
                                "metadata": "succcess"
                            }
                        }


                    else:
                        if not Transaction.objects.filter(escrow_id=escrow_instance.escrow_id,
                                                          transaction_type="SEND_LIBERTY_COMMISSION").exists() and bank_commission > 0:
                            SendCommissionScheduler.objects.create(
                                user=user, wallet_id=user_wallet.wallet_id, wallet_type=user_wallet.wallet_type, amount=float(bank_commission), provider="VFD",
                                transaction_commission_id=str(get_user_trans.transaction_id), transfer_leg="CARD_PUR_COMM",
                                escrow_id=escrow_instance.escrow_id, transaction_sub_type="CARD_PUR_COMM"
                            )

                            # send_commission = WalletSystem.pay_commission_to_liberty(
                            #     user_id=user.id,
                            #     wallet_id=user_wallet.wallet_id,
                            #     wallet_type=user_wallet.wallet_type,
                            #     liberty_commission=bank_commission,
                            #     from_provider_type="VFD",
                            #     transaction_commission_id=get_user_trans.transaction_id,
                            #     transfer_leg="CARD_PUR_COMM",
                            #     get_escrow_id=escrow_instance.escrow_id,
                            #     transaction_sub_type="CARD_PUR_COMM"
                            # )

                        response = {
                            "statusCode": 400,
                            "responseCode": "99",
                            "data": {
                                "message": "cards_user_debit_credit exists ot receiver_transaction is already successfull"
                            }
                        }

                else:
                    response = {
                        "statusCode": 400,
                        "responseCode": "99",
                        "data": {
                            "message": "No user_wallet Or liberty_reference not equal to Transaction.liberty_reference or transaction_id not equal to Transaction.transaction_id Credit or no get_user_trans"
                        }
                    }

            else:
                response = {
                    "statusCode": 400,
                    "responseCode": "99",
                    "data": {
                        "message": "No Transaction Or User Debit Credit Record Or User Cards Wallet"
                    }
                }
        else:
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {
                    "message": "No user or no cards user"
                }
            }

        return response

    @staticmethod
    def process_transaction_refund(request_payload, escape_limit=False):
        user_email = request_payload.get("data").get("object").get("metadata").get("user_email")
        trans_id = request_payload.get("data").get("object").get("metadata").get("transaction_id")
        card_id = request_payload.get("data", {"object": {"card": "null"}}).get("object").get("card")

        payload_amount = abs(request_payload.get("data").get("object").get("amount")) if request_payload.get("data").get("object").get(
            "amount") else 0.0

        transaction_in_q = Transaction.objects.filter(transaction_id=trans_id).first()
        if transaction_in_q and transaction_in_q.user.email == user_email:
            user = transaction_in_q.user
            user_wallet_instance = user.wallets.filter(wallet_type="COLLECTION").first()

            sender_transaction_type = "SEND_REFUND_CARD_PURCHASE"
            transaction_type = "REFUND_CARD_PURCHASE"

            # Check if There is a refund for transaction already
            # get_refund_credit_record = DebitCreditRecordOnAccount.objects.filter(user=user, entry="CREDIT", type_of_trans=transaction_type).first()
            check_refund_exists = Transaction.objects.filter(escrow_id=transaction_in_q.escrow_id, transaction_type="REFUND_CARD_PURCHASE").first()

            # print(get_refund_credit_record)
            log_info(str(check_refund_exists))

            if not check_refund_exists:

                amount_to_refund = transaction_in_q.amount
                amount_as_in_payload = amount_to_refund - transaction_in_q.liberty_commission + transaction_in_q.provider_fee

                # Check if payload amount and transaction amount match

                if payload_amount == amount_as_in_payload:

                    if escape_limit == False:

                        hours_to_count = 6
                        set_amount_limit = 50000
                        current_time = datetime.now()
                        last_five_hours = current_time - timedelta(hours=hours_to_count)

                        # Get Refund in last set hours
                        refunds_in_last_set_hours = Transaction.objects.filter(user=user, date_created__gte=last_five_hours,
                                                                               transaction_type="REFUND_CARD_PURCHASE", status="SUCCESSFUL")

                        log_info(str(refunds_in_last_set_hours))

                        amount_refunded_today = sum(abs(item.amount) for item in refunds_in_last_set_hours)

                        log_info(str(amount_refunded_today))

                        if amount_refunded_today + amount_to_refund > set_amount_limit:
                            response = {
                                "statusCode": 400,
                                "responseCode": "99",
                                "data": {
                                    "message": f"{hours_to_count} hours interval Refund has crossed {set_amount_limit}"
                                }
                            }

                            return response
                        else:
                            pass

                    # Now Process Refund

                    cards_user = User.objects.filter(email=settings.CARDS_USER_EMAIL).last()

                    if cards_user:

                        cards_user_wallet = WalletSystem.objects.filter(user=cards_user, wallet_type="COLLECTION").first()
                        if cards_user_wallet:
                            # Debit Cards User with amount due
                            deduct_cards_user_balance = WalletSystem.deduct_balance(
                                user=cards_user,
                                wallet=cards_user_wallet,
                                amount=amount_as_in_payload,
                                trans_type=sender_transaction_type
                            )

                            cards_user_balance_before = deduct_cards_user_balance.get("balance_before")

                            cards_user_balance_after = WalletSystem.get_balance_after(
                                user=cards_user,
                                balance_before=cards_user_balance_before,
                                total_amount=amount_as_in_payload,
                                is_credit=False,
                                transaction_type=sender_transaction_type
                            )

                            debit_credit_record_id = deduct_cards_user_balance.get("debit_credit_record_id")

                            sender_liberty_reference = Transaction.create_liberty_reference(suffix="REFCARDPUR")
                            narration = "CARD PURCAHSE REFUND"

                            create_ref_cards_transaction = Transaction.objects.create(
                                user=cards_user,
                                wallet_id=cards_user_wallet.wallet_id,
                                wallet_type=cards_user_wallet.wallet_type,
                                transaction_type=sender_transaction_type,
                                transaction_sub_type="refund_card_pur",
                                amount=amount_as_in_payload,
                                liberty_commission=0.00,
                                balance_before=cards_user_balance_before,
                                balance_after=cards_user_balance_after,
                                liberty_reference=sender_liberty_reference,
                                escrow_id=transaction_in_q.escrow_id,
                                unique_reference=transaction_in_q.unique_reference,
                                total_amount_charged=amount_as_in_payload,
                                narration=narration,
                                status="SUCCESSFUL",
                                transaction_leg="EXTERNAL",
                                terminal_id=user.terminal_id,
                                debit_credit_record_id=debit_credit_record_id
                            )

                            get_cards_user_debit_record = DebitCreditRecordOnAccount.objects.filter(id=int(debit_credit_record_id)).first()
                            get_cards_user_debit_record.transaction_instance_id = create_ref_cards_transaction.transaction_id
                            get_cards_user_debit_record.save()

                            # Credit User With Refund
                            user_balance_before = user_wallet_instance.available_balance
                            user_balance_after = WalletSystem.get_balance_after(
                                user=cards_user,
                                balance_before=user_balance_before,
                                total_amount=amount_to_refund,
                                is_credit=True
                            )

                            # Create transaction for Receiver
                            receiver_liberty_reference = Transaction.create_liberty_reference(suffix="REFCARDPUR")

                            receiver_transaction = Transaction.objects.create(
                                user=user,
                                wallet_id=user_wallet_instance.wallet_id,
                                wallet_type=user_wallet_instance.wallet_type,
                                transaction_type=transaction_type,
                                transaction_sub_type="redund_cardpur",
                                amount=amount_to_refund,
                                liberty_reference=receiver_liberty_reference,
                                total_amount_received=amount_to_refund,
                                escrow_id=transaction_in_q.escrow_id,
                                source_account_name=user.bvn_full_name,
                                source_wallet_id=cards_user_wallet.wallet_id,
                                source_wallet_type=cards_user_wallet.wallet_type,
                                narration=narration,
                                status="SUCCESSFUL",
                                transaction_leg="EXTERNAL",
                                balance_before=user_balance_before,
                                balance_after=user_balance_after,
                                unique_reference=transaction_in_q.unique_reference,
                            )

                            # fund wallet
                            fund_receiver_balance = WalletSystem.fund_balance(
                                user=user,
                                wallet=user_wallet_instance,
                                amount=amount_to_refund,
                                trans_type=transaction_type,
                                transaction_instance_id=receiver_transaction.transaction_id,
                                reversal_trans_id=transaction_in_q.transaction_id,
                                unique_reference=transaction_in_q.unique_reference

                            )

                            user_debit_credit_record = deduct_cards_user_balance.get("record")

                            receiver_transaction.status = "SUCCESSFUL"
                            receiver_transaction.provider_status = "SUCCESSFUL"
                            receiver_transaction.debit_credit_record_id = user_debit_credit_record.id if user_debit_credit_record else None
                            receiver_transaction.save()

                            transaction_in_q.status = "REVERSED"
                            transaction_in_q.is_reversed = True
                            transaction_in_q.save()

                            ##########################################################################################
                            # SEND OUT APP NOTIFICATION
                            not_token = user.firebase_key
                            not_title = "Transaction Successful"
                            not_body = f"A Refund of N{amount_to_refund} has occured on your account. COMM - 0.00"
                            not_data = {"amount_sent": f"{amount_to_refund}", "available_balance": f"{user_wallet_instance.available_balance}"}

                            send_out_notification = cloud_messaging.send_broadcast(
                                token=not_token,
                                title=not_title,
                                body=not_body,
                                data=not_data
                            )

                            InAppTransactionNotification.create_in_app_transaction_notification(
                                user=user,
                                title=not_title,
                                message_body=not_body
                            )

                            ############################################################################################################################
                            # DEBIT SENDER SMS

                            WalletSystem.transaction_alert_notfication_manager(
                                user=user,
                                amount=float(amount_to_refund),
                                cr_dr="DR",
                                narration=f"{narration}",
                                from_wallet_type=user_wallet_instance.wallet_type,
                                transaction_instance_id=transaction_in_q.transaction_id
                            )

                            # Send Notification to other Users
                            send_notification_to_user = OtherServiceUsersGateway.send_refund_notification_to_others(request_payload=request_payload,
                                                                                                                    user=user, card_id=card_id)

                            card_initial_trans = Transaction.objects.filter(escrow_id=transaction_in_q.escrow_id,
                                                                            transaction_type="SETTLE_CARDS_PURCHASE").first()
                            if card_initial_trans:
                                card_initial_trans.status = "REVERSED"
                                card_initial_trans.is_reversed = True
                                card_initial_trans.save()

                            # Process Commission Reversal

                            # Get Comm Liberty Ref

                            comm_trans = Transaction.objects.filter(escrow_id=transaction_in_q.escrow_id,
                                                                    transaction_type="SEND_LIBERTY_COMMISSION").first()
                            if comm_trans:
                                check_liberty_refernce = VFDBank.vfd_transaction_verification_handler(reference=comm_trans.liberty_reference)
                                if check_liberty_refernce["status"] != "00":
                                    response = {
                                        "statusCode": 400,
                                        "responseCode": "99",
                                        "data": {
                                            "message": f"Could not verify commissions liberty refernce"
                                        },
                                        "others_data": send_notification_to_user

                                    }

                                    return response

                                get_real_transfer_status = check_liberty_refernce["data"].get("transactionStatus")

                                if get_real_transfer_status != "00":
                                    response = {
                                        "statusCode": 400,
                                        "responseCode": "99",
                                        "data": {
                                            "message": f"VFD Comm Transaction status is not 00"
                                        },
                                        "others_data": send_notification_to_user

                                    }

                                    return response

                                else:
                                    # Process Reversal

                                    if not OutOfBookTransfer.objects.filter(escrow_id=transaction_in_q.escrow_id,
                                                                            trans_id=comm_trans.transaction_id).exists():

                                        liberty_commission_bank_account = AccountSystem.get_float_account(
                                            from_wallet_type="COMMISSIONS", from_provider_type="VFD"
                                        )

                                        liberty_float_bank_account = AccountSystem.get_float_account(
                                            from_wallet_type="FLOAT", from_provider_type="VFD"
                                        )

                                        liberty_beneficiary_account_name = liberty_float_bank_account.account_name
                                        liberty_beneficiary_nuban = liberty_float_bank_account.account_number

                                        beneficiary_bank_code = "999999"

                                        sub_comm_liberty_ref = Transaction.create_liberty_reference(suffix="LGLP-CARDSCOMMRVRSL-VFD")

                                        record_sub_com = OutOfBookTransfer.objects.create(
                                            type_of_transfer="CARDPURREF",
                                            provider="VFD",
                                            amount=comm_trans.amount,
                                            from_account=liberty_commission_bank_account.account_number,
                                            to_account=liberty_beneficiary_nuban,
                                            created_liberty_reference=sub_comm_liberty_ref,
                                            trans_id=comm_trans.transaction_id,
                                            escrow_id=transaction_in_q.escrow_id,
                                        )

                                        reverse_sub_comm = VFDBank.initiate_payout(
                                            beneficiary_account_name=liberty_beneficiary_account_name,
                                            beneficiary_nuban=liberty_beneficiary_nuban,
                                            beneficiary_bank_code=beneficiary_bank_code,
                                            source_account=liberty_commission_bank_account.account_number,
                                            narration=f"COMMISSION REVERSAL FOR {transaction_in_q.liberty_reference}",
                                            amount=comm_trans.amount,
                                            transfer_type="intra",
                                            user_bvn=user.check_kyc.bvn_rel.bvn_number,
                                            reference=sub_comm_liberty_ref,
                                        )

                                        record_sub_com.send_payload = reverse_sub_comm
                                        record_sub_com.save()

                                        comm_trans.status = "REVERSED"
                                        comm_trans.save()

                                        check_reversed_comm = VFDBank.vfd_transaction_verification_handler(reference=sub_comm_liberty_ref)
                                        if check_reversed_comm["status"] == "00":
                                            get_real_freversed_comm = check_reversed_comm["data"].get("transactionStatus")

                                            if get_real_freversed_comm == "00":
                                                record_sub_com.verification_payload = json.dumps(check_reversed_comm)
                                                record_sub_com.is_verified = True
                                                record_sub_com.is_done = True
                                                record_sub_com.save()

                                        response = {
                                            "statusCode": 200,
                                            "responseCode": "00",
                                            "data": {
                                                "metadata": "amount refunded and commissions reversed successfully"
                                            },
                                            "others_data": send_notification_to_user
                                        }

                                        return response


                                    else:
                                        response = {
                                            "statusCode": 400,
                                            "responseCode": "99",
                                            "data": {
                                                "message": f"Out Of Books Transaction Already Exists"
                                            },
                                            "others_data": send_notification_to_user

                                        }

                                        return response

                            else:
                                response = {
                                    "statusCode": 200,
                                    "responseCode": "00",
                                    "data": {
                                        "metadata": "amount refunded and commissions transaction does not exist"
                                    },
                                    "others_data": send_notification_to_user

                                }

                                return response

                        else:
                            response = {
                                "statusCode": 400,
                                "responseCode": "99",
                                "data": {
                                    "message": f"Cards User Wallet does not exist"
                                }
                            }

                            return response
                    else:
                        response = {
                            "statusCode": 400,
                            "responseCode": "99",
                            "data": {
                                "message": f"Cards User does not exist"
                            }
                        }

                        return response

                else:
                    response = {
                        "statusCode": 400,
                        "responseCode": "99",
                        "data": {
                            "message": f"Payload Amount does not mactch transaction Amount"
                        }
                    }

                    return response

            else:
                response = {
                    "statusCode": 400,
                    "responseCode": "99",
                    "data": {
                        "message": f"Refund Transaction exists or debit credit for refund exists"
                    }
                }

                return response

        else:
            response = {
                "statusCode": 400,
                "responseCode": "99",
                "data": {
                    "message": f"Transaction Does not exist or email does not match"
                }
            }

            return response

    @staticmethod
    def create_transaction(request_payload):
        objects = request_payload.get('data').get('object')
        environment = request_payload.get('environment')
        business = request_payload.get('business')
        data_list = request_payload.get('data')
        transaction_type = request_payload.get('type')
        transaction_created_id = request_payload.get('_id')
        created_at = objects.get('createdAt')
        customer_id = objects.get('customer')
        account_id = objects.get('account')
        card_id = objects.get('card')
        authorization_id = objects.get('authorization')
        amount = objects.get('amount')
        fee = objects.get('fee')
        vat = objects.get('vat')
        merchant_amount = objects.get('merchant_amount')
        merchant = objects.get('merchant')
        merchant_id = objects.get('merchant').get('_id')
        terminal = objects.get('terminal')
        transaction_metadata = objects.get('transactionMetadata')
        data_id = data_list.get('_id')
        fee_details = objects.get('feeDetails')
        reference = objects.get('transactionMetadata').get('reference')

        try:

            transaction = SudoCardTransaction.objects.create(
                environment=environment,
                business=business,
                created_at=created_at,
                transaction_created_id=transaction_created_id,
                customer_id=customer_id,
                account_id=account_id,
                card_id=card_id,
                authorization_id=authorization_id,
                data=data_list,
                amount=amount,
                fee=fee,
                vat=vat,
                merchant_id=merchant_id,
                transaction_type=transaction_type,
                merchant=json.dumps(merchant),
                terminal=json.dumps(terminal),
                transaction_metadata=json.dumps(transaction_metadata),
                data_id=data_id,
                fee_details=json.dumps(fee_details),
                reference=reference
            )

        except:
            pass

        return True


class SudoCardAuthorization(models.Model):
    environment = models.CharField(max_length=400, blank=False, null=False)
    business = models.CharField(max_length=400, blank=False, null=False)
    pending_request_amount = models.CharField(max_length=400, blank=False, null=False)
    authorization_type = models.CharField(max_length=400, blank=False, null=False)
    created_at = models.DateTimeField()
    customer_id = models.CharField(max_length=400, blank=False, null=False)
    account_id = models.CharField(max_length=400, blank=False, null=False)
    card_id = models.CharField(max_length=400, blank=False, null=False)
    funding_source_id = models.CharField(max_length=400, blank=False, null=False)
    authorization_id = models.CharField(max_length=400, blank=False, null=False)
    card_brand = models.CharField(max_length=400, blank=False, null=False)
    card_type = models.CharField(max_length=400, blank=False, null=False)
    amount = models.CharField(max_length=400, blank=False, null=False)
    fee = models.CharField(max_length=400, blank=False, null=False)
    vat = models.CharField(max_length=400, blank=False, null=False)
    merchant_amount = models.CharField(max_length=400, blank=False, null=False)
    data_id = models.CharField(max_length=400, blank=False, null=False)
    fee_details = models.TextField()
    merchant = models.TextField()
    terminal = models.TextField()
    verification = models.TextField()
    transaction_metadata = models.TextField()
    pending_request = models.TextField()
    changes = models.TextField()
    data = models.TextField()
    date = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return self.authorization_id

    @classmethod
    def get_user_balance(cls, user):
        WalletSystem

    @staticmethod
    def authorize_changes(data):
        objects = data.get('data').get('object')
        environment = data.get('environment')
        business = data.get('business')
        data_list = data.get('data')
        authorization_type = data.get('type')
        created_at = objects.get('createdAt')
        customer_id = objects.get('customer').get('_id')
        account_id = objects.get('account').get('_id')
        card_id = objects.get('card').get('_id')
        funding_source_id = objects.get('card').get('fundingSource').get('_id')
        card_type = objects.get('card').get('type')
        card_brand = objects.get('card').get('brand')
        authorization_id = data.get('_id')
        amount = objects.get('amount')
        fee = objects.get('fee')
        vat = objects.get('vat')
        merchant_amount = objects.get('merchantAmount')
        merchant = objects.get('merchant')
        terminal = objects.get('terminal')
        transaction_metadata = objects.get('transactionMetadata')
        pending_request = objects.get('pendingRequest')
        verification = objects.get('verification')
        pending_request_amount = objects.get('pendingRequest').get('amount')
        data_id = data_list.get('_id')
        fee_details = objects.get('feeDetails')
        changes = data_list.get('changes')

        authorize_changes = SudoCardAuthorization.objects.create(
            environment=environment,
            business=business,
            data=data_list,
            pending_request_amount=pending_request_amount,
            authorization_type=authorization_type,
            created_at=created_at,
            customer_id=customer_id,
            account_id=account_id,
            card_id=card_id,
            funding_source_id=funding_source_id,
            authorization_id=authorization_id,
            card_brand=card_brand,
            card_type=card_type,
            amount=amount,
            fee=fee,
            vat=vat,
            merchant_amount=merchant_amount,
            merchant=merchant,
            terminal=terminal,
            verification=verification,
            transaction_metadata=transaction_metadata,
            pending_request=pending_request,
            data_id=data_id,
            fee_details=fee_details,
            changes=changes
        )
        return True


class OtherServiceUsersGateway(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    headers = models.TextField(default={
        "Content-Type": "application/json", "Authorization": "Token "
    })
    gateway = models.CharField(max_length=500)
    is_active = models.BooleanField(default=True)
    date_created = models.DateTimeField(auto_now_add=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.user.email

    def save(self, *args, **kwargs):
        if self.headers:
            if self.headers.startswith('{'):
                from horizon_pay.helpers.helper_function import encrypt_trans_pin
                json_form = json.dumps(self.headers)
                get_encrypted_header = encrypt_trans_pin(json_form)
                self.headers = get_encrypted_header
            else:
                pass
        else:
            pass

        super(OtherServiceUsersGateway, self).save(*args, **kwargs)

    @classmethod
    def send_gateway_data(cls, payload, service_user: 'OtherServiceUsersGateway', card_id):
        from horizon_pay.helpers.helper_function import decrypt_trans_pin

        if not service_user:
            return None

        url = f"{service_user.gateway}?card_id={card_id}"
        get_decrypted_header = decrypt_trans_pin(service_user.headers)
        headers = eval(json.loads(get_decrypted_header))
        payload = payload

        log_info(str(headers))
        log_info(str(type(headers)))

        log_info(str(url))
        log_info(str(type(url)))

        log_info(str(payload))
        log_info(str(type(payload)))

        try:
            response = requests.post(url=url, json=payload, headers=headers, timeout=5)
            json_resp = response.json()

            if json_resp.get("status") != "Failed":
                resp = {
                    'status': 'sent',
                    'response': json_resp
                }

            else:
                resp = {
                    'status': 'unsent',
                    'response': json_resp
                }


        except requests.exceptions.RequestException as e:
            resp = {
                'status': 'unsent',
                'response': {},
                'message': f"{e}"
            }

        log_info(str(resp))
        return resp

    @classmethod
    def send_refund_notification_to_others(cls, request_payload, user, card_id):
        try:
            get_external_gateway = OtherServiceUsersGateway.objects.get(user=user, is_active=True)
        except OtherServiceUsersGateway.DoesNotExist:
            return None

        send_data = cls.send_gateway_data(request_payload, get_external_gateway, card_id)

        return send_data
