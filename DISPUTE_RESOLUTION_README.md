# Dispute Resolution System

An automated system for resolving customer disputes by matching Excel transaction data with unresolved disputes, processing refunds, and sending email notifications.

## Overview

This system automates the dispute resolution workflow by:

1. **Downloading Excel files** from Google Drive containing transaction data
2. **Processing transaction data** to extract RRN (Retrieval Reference Number) and amounts
3. **Matching transactions** with unresolved disputes in the database
4. **Processing refunds** automatically for resolved disputes
5. **Sending email notifications** to customers about their resolved disputes
6. **Caching processed data** to avoid reprocessing the same files

## Components

### Core Modules

- **`resolve_disputes_from_excel.py`** - Main Django management command
- **`dispute_cache.py`** - SQLite-based caching system for processed data
- **`google_drive_service.py`** - Google Drive API integration for file downloads
- **`excel_processor.py`** - Excel file parsing and transaction extraction
- **`dispute_resolver.py`** - Core logic for matching disputes with transactions
- **`refund_processor.py`** - Refund processing using existing send money infrastructure
- **`email_notifier.py`** - Email notification system for customers
- **`logging_config.py`** - Comprehensive logging and error handling
- **`config.py`** - Configuration management and environment variables

### Templates

- **`dispute_resolution_notification.html`** - Email template for customer notifications

### Configuration

- **`.env.dispute_resolution.example`** - Environment variable template
- **`config.py`** - Configuration defaults and validation

## Installation and Setup

### 1. Prerequisites

Ensure you have the required packages installed:
```bash
pip install google-api-python-client openpyxl pandas
```

### 2. Environment Configuration

1. Ensure environment variable data is updated

2. Update the configuration values in `.env.dispute_resolution`:
```bash
# Required for Google Drive integration
GOOGLE_DRIVE_CREDENTIALS_PATH=/path/to/service-account-credentials.json
GOOGLE_DRIVE_FOLDER_ID=your_google_drive_folder_id

# Required for refund processing
DISPUTE_REFUND_SENDER_EMAIL=<EMAIL>
REFUND_TRANSACTION_PIN=your_secure_pin
```

### 3. Google Drive Setup

1. Create a Google Cloud Project and enable the Drive API
2. Create a service account and download the credentials JSON file
3. Share your Google Drive folder with the service account email
4. Update `GOOGLE_DRIVE_CREDENTIALS_PATH` and `GOOGLE_DRIVE_FOLDER_ID`

### 4. Database Setup

The system uses SQLite for caching. No additional database setup is required.

## Usage

### Basic Usage

Run the dispute resolution process:
```bash
python manage.py resolve_disputes_from_excel
```

### Command Options

```bash
# Run in dry-run mode (no actual changes)
python manage.py resolve_disputes_from_excel --dry-run

# Process files for a specific date
python manage.py resolve_disputes_from_excel --date 2025-09-09

# Look back specific number of days
python manage.py resolve_disputes_from_excel --days-back 14

# Use a different sender account for refunds
python manage.py resolve_disputes_from_excel --sender-email <EMAIL>

# Skip specific steps
python manage.py resolve_disputes_from_excel --skip-refunds
python manage.py resolve_disputes_from_excel --skip-emails

# Process local files instead of downloading
python manage.py resolve_disputes_from_excel --local-files file1.xlsx file2.xlsx

# Clear cache before processing
python manage.py resolve_disputes_from_excel --clear-cache

# Enable verbose logging
python manage.py resolve_disputes_from_excel --verbose
```

### Testing

Test the system components:
```bash
# Test all components
python manage.py test_dispute_resolution

# Test specific component
python manage.py test_dispute_resolution --component excel

# Create test data and run tests
python manage.py test_dispute_resolution --create-test-data --cleanup
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `GOOGLE_DRIVE_CREDENTIALS_PATH` | Path to service account JSON | Required |
| `GOOGLE_DRIVE_FOLDER_ID` | Google Drive folder ID | Required |
| `DISPUTE_REFUND_SENDER_EMAIL` | Email for refund sender account | <EMAIL> |
| `REFUND_TRANSACTION_PIN` | PIN for refund transactions | 1234 (change in production) |
| `AMOUNT_VALIDATION_TOLERANCE_PERCENT` | Amount matching tolerance | 1.0 |
| `DEFAULT_DAYS_BACK` | Default days to look back | 7 |
| `DRY_RUN_BY_DEFAULT` | Enable dry-run by default | false |

See `.env.dispute_resolution.example` for all available options.

### File Patterns

The system expects Excel files with these naming patterns:
- `LIBERTYPAY_Terminal_Owner_CR_YYYY_MM_DD_HHMMSS.xlsx`
- `LIBERTYPAY_Terminal_Owner_DR_YYYY_MM_DD_HHMMSS.xlsx`
- `LIBERTYPAY_Pos_Acquired_Detail_Report_CR_YYYY_MM_DD_HHMMSS.xlsx`
- `LIBERTYPAY_Pos_Acquired_Detail_Report_DR_YYYY_MM_DD_HHMMSS.xlsx`

## Workflow

### 1. File Download
- Connects to Google Drive using service account credentials
- Lists files in the specified folder matching expected patterns
- Downloads files for the specified date range
- Skips files that have already been processed (cached)

### 2. Excel Processing
- Parses Excel files to extract transaction data
- Automatically detects RRN and amount columns
- Validates and cleans data
- Handles multiple sheets per file

### 3. Dispute Matching
- Queries unresolved disputes from the database
- Matches Excel RRNs with dispute RRNs
- Validates amount matching within tolerance
- Marks disputes as resolved

### 4. Refund Processing
- Validates dispute eligibility for refunds
- Prepares refund data using existing send money infrastructure
- Processes refunds in batches
- Tracks refund references

### 5. Email Notifications
- Sends personalized emails to customers
- Uses professional HTML templates
- Includes dispute details and refund information
- Handles email failures gracefully

### 6. Caching and Logging
- Caches processed files and transactions
- Comprehensive audit logging
- Performance monitoring
- Error tracking and recovery

## Monitoring and Logs

### Log Files

Logs are stored in `logs/dispute_resolution/`:
- `dispute_resolution.log` - Main application logs
- `audit.log` - Audit trail of all actions
- `errors.log` - Error-specific logs

### Cache Database

The SQLite cache database is stored at `data/dispute_cache.db` and tracks:
- Processed files with metadata
- Individual processed transactions
- Processing timestamps and statistics

### Performance Monitoring

The system tracks:
- Operation timing
- Memory usage
- Error frequencies
- Processing statistics

## Security Considerations

1. **Credentials**: Store Google Drive credentials securely
2. **Transaction PIN**: Change default PIN in production
3. **Email Content**: Sensitive information is included in emails
4. **Database Access**: Ensure proper database permissions
5. **File Access**: Validate file sources and content

## Error Handling

The system includes comprehensive error handling:
- **Graceful Failures**: Operations continue despite individual failures
- **Error Recovery**: Automatic retry mechanisms where appropriate
- **Error Reporting**: Detailed error logging and tracking
- **Validation**: Input validation at every step
- **Safety Limits**: Maximum amounts, batch sizes, and error thresholds

## Troubleshooting

### Common Issues

1. **Google Drive Access Denied**
   - Verify service account credentials
   - Check folder sharing permissions
   - Validate folder ID

2. **No Disputes Found**
   - Check date range parameters
   - Verify dispute status in database
   - Ensure RRN fields are populated

3. **Amount Mismatches**
   - Adjust tolerance percentage
   - Check data quality in Excel files
   - Verify dispute amount fields

4. **Refund Failures**
   - Verify sender account configuration
   - Check account balances
   - Validate bank details

5. **Email Failures**
   - Check email configuration
   - Verify SMTP settings
   - Validate recipient addresses

### Debug Mode

Enable verbose logging for debugging:
```bash
python manage.py resolve_disputes_from_excel --verbose --dry-run
```

## Maintenance

### Regular Tasks

1. **Cache Cleanup**: Clear old cache entries periodically
2. **Log Rotation**: Archive old log files
3. **Database Backup**: Backup the cache database
4. **Configuration Review**: Review and update configuration
5. **Performance Monitoring**: Monitor processing times and error rates

### Scheduled Execution

Consider setting up scheduled execution:
```bash
# Daily execution at 2 AM
0 2 * * * cd /path/to/project && python manage.py resolve_disputes_from_excel
```

## Support

For issues or questions:
1. Check the logs for error details
2. Run the test suite to validate components
3. Use dry-run mode to test changes safely
4. Review configuration and environment variables
