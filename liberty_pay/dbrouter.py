from random import choice
from django.db.utils import OperationalError
from django.db import connections
from main.helper.logging_utils import log_info


class ReadWriteRouter:
    """
    A router that route query to different database based on.
    """

    def db_for_read(self, model, **hints):
        """
        Route read to either the default or replica.
        """

        return "default"
        table_names_for_default = ["Transaction", "WalletSystem", "DebitCreditRecordOnAccount"]

        if model.__name__ in table_names_for_default:
            return "default"

        db_list = ["slaveone", "default"]

        db_choice = db_list[0]
        try:
            # Check if the chosen database is online
            db_conn = connections[db_choice]
            db_conn.cursor()
            
        # except OperationalError:
        except Exception as err:
            log_info(str(err))
            log_info(f"{db_choice} is down !")
            
            #Retrieve the other database 
            #if the chosen database is down
            
            db_list.remove(db_choice)
            if len(db_list) > 0:
                db_choice = db_list[0]
            else:
                db_choice = "default"
            
            log_info(f"Switching to {db_choice} for READ Query")
            
        log_info(f"performing READ query on {db_choice}")
        return db_choice

    def db_for_write(self, model, **hints):
       
        log_info(f"WRITE OPERATION using default")

        return "default"

    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if a model in the auth or contenttypes apps is
        involved.
        """
  
        return True
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        return False
    

        # def allow_migrate(self, db, **hints):
        #     if db == 'default':
        #         return True
        #     elif db == 'readonly_db':
        #         return False
        #     return None
        

        # model = hints.get('model')
        # if not model:
        #     return None
        
        # if db != 'default':
        #     return False
        
        # return db == 'default'
        # if db != 'default':
        #     # modelname = model_name or model._meta.model_name
        #     # usedb = getattr(model._meta, 'database', None)
        #     # if not usedb:
        #     #     return False

        #     # if modelname and usedb:
        #     #     return usedb == db
        #     # return False
        # else:
        #     usedb = getattr(model._meta, 'database', 'default')
        #     return usedb == db
        # return None
    

    #     route_app_labels = ['categories','products']
    # def allow_migrate(self, db, app_label, model_name=None, **hints):
    #     if app_label in self.route_app_labels:
    #         return db == 'secondary'
    #     return None