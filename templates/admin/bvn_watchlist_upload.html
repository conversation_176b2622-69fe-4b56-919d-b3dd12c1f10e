{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url 'admin:main_bvnwatchlist_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; {{ title }}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="module aligned">
        <h2>Upload BVN Watchlist</h2>
        
        <div class="form-row">
            <div class="help">
                <p><strong>Instructions:</strong></p>
                <ul>
                    <li>Upload a CSV or XLSX file containing BVN watchlist data</li>
                    <li><strong>For Excel files:</strong> Data should be in the first sheet or any sheet with valid data</li>
                    <li>The file must contain the following columns (case-insensitive, accepts variations):
                        <ul>
                            <li><strong>BVN</strong> (or BVN NUMBER) - 11-digit BVN number</li>
                            <li><strong>REQUESTING BANK</strong> (or BANK, BANK NAME) - Bank that requested the watchlist</li>
                            <li><strong>FIRST NAME</strong> (or FIRSTNAME) - First name of the person</li>
                            <li><strong>MIDDLE NAME</strong> (or OTHER NAME, optional) - Middle name</li>
                            <li><strong>SURNAME</strong> (or LAST NAME) - Last name of the person</li>
                            <li><strong>CATEGORY</strong> (or TYPE) - Category of watchlist (FRAUD, SUSPICIOUS, MONEY_LAUNDERING, TERRORISM_FINANCING, IDENTITY_THEFT, OTHER)</li>
                            <li><strong>WATCHLIST DATE</strong> (or DATE WATCHLISTED) - Date when BVN was watchlisted</li>
                        </ul>
                    </li>
                    <li>Existing BVN entries will be updated with new information</li>
                    <li>Maximum file size: 10MB</li>
                    <li><strong>Note:</strong> Column names are flexible - the system will try to match common variations</li>
                </ul>
            </div>
        </div>

        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <fieldset class="module aligned">
                <div class="form-row">
                    <div>
                        <label for="{{ form.file.id_for_label }}" class="required">{{ form.file.label }}:</label>
                        {{ form.file }}
                        {% if form.file.help_text %}
                            <div class="help">{{ form.file.help_text }}</div>
                        {% endif %}
                        {% if form.file.errors %}
                            <ul class="errorlist">
                                {% for error in form.file.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>
                </div>
            </fieldset>

            <div class="submit-row">
                <input type="submit" value="Upload and Process" class="default" />
                <a href="{% url 'admin:main_bvnwatchlist_changelist' %}" class="button cancel-link">Cancel</a>
            </div>
        </form>

        <div class="form-row">
            <div class="help">
                <h3>Sample File Format:</h3>
                <table border="1" style="border-collapse: collapse; margin-top: 10px;">
                    <thead>
                        <tr style="background-color: #f0f0f0;">
                            <th style="padding: 8px;">BVN</th>
                            <th style="padding: 8px;">REQUESTING BANK</th>
                            <th style="padding: 8px;">FIRST NAME</th>
                            <th style="padding: 8px;">MIDDLE NAME</th>
                            <th style="padding: 8px;">SURNAME</th>
                            <th style="padding: 8px;">CATEGORY</th>
                            <th style="padding: 8px;">WATCHLIST DATE</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 8px;">***********</td>
                            <td style="padding: 8px;">First Bank</td>
                            <td style="padding: 8px;">John</td>
                            <td style="padding: 8px;">Michael</td>
                            <td style="padding: 8px;">Doe</td>
                            <td style="padding: 8px;">FRAUD</td>
                            <td style="padding: 8px;">2024-01-15</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px;">***********</td>
                            <td style="padding: 8px;">Access Bank</td>
                            <td style="padding: 8px;">Jane</td>
                            <td style="padding: 8px;"></td>
                            <td style="padding: 8px;">Smith</td>
                            <td style="padding: 8px;">SUSPICIOUS</td>
                            <td style="padding: 8px;">2024-02-20</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
