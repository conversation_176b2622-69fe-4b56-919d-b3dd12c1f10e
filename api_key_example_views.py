"""
Example views demonstrating API key authentication usage.
These examples show how to integrate the API key system with your existing views.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model

from main.permissions import RequireAP<PERSON><PERSON>eyScope, CheckDynamicAuthentication, CustomIsAuthenticated

User = get_user_model()


class APIKeyProtectedView(APIView):
    """
    Example view that requires API key authentication with specific scope.
    """
    permission_classes = [RequireAPIKeyScope]
    required_scope = 'transactions:read'
    
    def get(self, request):
        """
        Get transaction data - requires 'transactions:read' scope
        """
        api_key = getattr(request, 'api_key', None)
        
        response_data = {
            'message': 'Access granted to transaction data',
            'user': request.user.email,
            'authentication_method': 'API Key' if api_key else 'Other',
        }
        
        if api_key:
            response_data.update({
                'api_key_name': api_key.name,
                'api_key_id': api_key.key_id,
                'api_key_scopes': api_key.scopes,
                'usage_count': api_key.usage_count,
            })
        
        return Response(response_data)


class FlexibleAuthView(APIView):
    """
    Example view that accepts multiple authentication methods including API keys.
    """
    permission_classes = [CheckDynamicAuthentication]
    
    def get(self, request):
        """
        Endpoint that works with JWT, API keys, Basic auth, or Token auth
        """
        api_key = getattr(request, 'api_key', None)
        
        # Different behavior based on authentication method
        if api_key:
            # API key authentication
            if not api_key.has_scope('data:read'):
                return Response({
                    'error': 'Insufficient scope',
                    'required_scope': 'data:read',
                    'available_scopes': api_key.scopes
                }, status=status.HTTP_403_FORBIDDEN)
            
            auth_info = {
                'method': 'API Key',
                'key_name': api_key.name,
                'key_id': api_key.key_id,
                'scopes': api_key.scopes,
            }
        else:
            # Regular user authentication (JWT, Token, etc.)
            auth_info = {
                'method': 'User Authentication',
                'user_permissions': list(request.user.user_permissions.values_list('codename', flat=True)),
            }
        
        return Response({
            'message': 'Successfully authenticated',
            'user': request.user.email,
            'auth_info': auth_info,
            'data': {
                'example': 'This is protected data',
                'timestamp': '2024-01-01T12:00:00Z'
            }
        })


class ScopedTransactionView(APIView):
    """
    Example view with different endpoints requiring different scopes.
    """
    permission_classes = [CheckDynamicAuthentication]
    
    def get(self, request):
        """
        Read transactions - requires 'transactions:read' scope for API keys
        """
        api_key = getattr(request, 'api_key', None)
        
        if api_key and not api_key.has_scope('transactions:read'):
            return Response({
                'error': 'Insufficient scope for reading transactions',
                'required_scope': 'transactions:read'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Mock transaction data
        transactions = [
            {'id': 1, 'amount': 100.00, 'type': 'credit'},
            {'id': 2, 'amount': 50.00, 'type': 'debit'},
        ]
        
        return Response({
            'transactions': transactions,
            'count': len(transactions)
        })
    
    def post(self, request):
        """
        Create transaction - requires 'transactions:write' scope for API keys
        """
        api_key = getattr(request, 'api_key', None)
        
        if api_key and not api_key.has_scope('transactions:write'):
            return Response({
                'error': 'Insufficient scope for creating transactions',
                'required_scope': 'transactions:write'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Mock transaction creation
        transaction_data = request.data
        
        return Response({
            'message': 'Transaction created successfully',
            'transaction': {
                'id': 123,
                'amount': transaction_data.get('amount'),
                'type': transaction_data.get('type', 'debit'),
                'created_by': request.user.email
            }
        }, status=status.HTTP_201_CREATED)


class AdminOnlyView(APIView):
    """
    Example view that requires admin scope for API keys or admin user for regular auth.
    """
    permission_classes = [CheckDynamicAuthentication]
    
    def get(self, request):
        """
        Admin-only endpoint
        """
        api_key = getattr(request, 'api_key', None)
        
        if api_key:
            # API key authentication - check for admin scope
            if not api_key.has_scope('admin:read'):
                return Response({
                    'error': 'Admin scope required',
                    'required_scope': 'admin:read'
                }, status=status.HTTP_403_FORBIDDEN)
        else:
            # Regular user authentication - check if user is admin
            if not request.user.is_staff:
                return Response({
                    'error': 'Admin access required'
                }, status=status.HTTP_403_FORBIDDEN)
        
        return Response({
            'message': 'Admin data access granted',
            'admin_data': {
                'total_users': User.objects.count(),
                'system_status': 'operational'
            }
        })


class RateLimitedView(APIView):
    """
    Example view that demonstrates rate limiting behavior.
    """
    permission_classes = [CheckDynamicAuthentication]
    
    def get(self, request):
        """
        Endpoint that shows rate limit information for API keys
        """
        api_key = getattr(request, 'api_key', None)
        
        if api_key:
            # Get current rate limit status
            is_allowed, current_count, reset_time = api_key.check_rate_limit()
            
            rate_limit_info = {
                'requests_made': current_count,
                'requests_limit': api_key.rate_limit_requests,
                'window_seconds': api_key.rate_limit_window,
                'reset_time': reset_time.isoformat() if reset_time else None,
                'requests_remaining': max(0, api_key.rate_limit_requests - current_count)
            }
        else:
            rate_limit_info = {
                'message': 'Rate limiting only applies to API key authentication'
            }
        
        return Response({
            'message': 'Rate limit information',
            'rate_limit': rate_limit_info,
            'usage_stats': {
                'total_usage_count': api_key.usage_count if api_key else 'N/A',
                'last_used': api_key.last_used.isoformat() if api_key and api_key.last_used else 'N/A'
            }
        })


# URL patterns example (add to your urls.py):
"""
from django.urls import path
from . import api_key_example_views

urlpatterns = [
    path('api/protected/', api_key_example_views.APIKeyProtectedView.as_view(), name='api-protected'),
    path('api/flexible/', api_key_example_views.FlexibleAuthView.as_view(), name='api-flexible'),
    path('api/transactions/', api_key_example_views.ScopedTransactionView.as_view(), name='api-transactions'),
    path('api/admin/', api_key_example_views.AdminOnlyView.as_view(), name='api-admin'),
    path('api/rate-limit/', api_key_example_views.RateLimitedView.as_view(), name='api-rate-limit'),
]
"""
