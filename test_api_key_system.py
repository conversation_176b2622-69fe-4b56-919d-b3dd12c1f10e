"""
Test cases for the API Key authentication system.
Run these tests to verify the implementation works correctly.
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APITestCase
from rest_framework import status

from main.models import APIKey

User = get_user_model()


class APIKeyModelTest(TestCase):
    """Test cases for the APIKey model."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_generate_api_key(self):
        """Test API key generation."""
        api_key, full_key = APIKey.generate_key(
            user=self.user,
            name='Test Key',
            scopes=['transactions:read', 'users:read']
        )
        
        # Check the API key object
        self.assertEqual(api_key.user, self.user)
        self.assertEqual(api_key.name, 'Test Key')
        self.assertEqual(api_key.scopes, ['transactions:read', 'users:read'])
        self.assertTrue(api_key.is_active)
        self.assertTrue(api_key.key_id.startswith('ak_'))
        
        # Check the full key format
        self.assertIn(':', full_key)
        key_id, secret = full_key.split(':', 1)
        self.assertEqual(key_id, api_key.key_id)
        self.assertEqual(len(secret), 64)  # 32 bytes = 64 hex chars
    
    def test_verify_key(self):
        """Test API key verification."""
        api_key, full_key = APIKey.generate_key(
            user=self.user,
            name='Test Key'
        )
        
        key_id, secret = full_key.split(':', 1)
        
        # Valid secret should verify
        self.assertTrue(api_key.verify_key(secret))
        
        # Invalid secret should not verify
        self.assertFalse(api_key.verify_key('invalid_secret'))
        
        # Inactive key should not verify
        api_key.is_active = False
        api_key.save()
        self.assertFalse(api_key.verify_key(secret))
    
    def test_expiration(self):
        """Test API key expiration."""
        # Create expired key
        expired_key, _ = APIKey.generate_key(
            user=self.user,
            name='Expired Key',
            expires_at=timezone.now() - timedelta(days=1)
        )
        
        self.assertTrue(expired_key.is_expired())
        
        # Create non-expired key
        valid_key, _ = APIKey.generate_key(
            user=self.user,
            name='Valid Key',
            expires_at=timezone.now() + timedelta(days=1)
        )
        
        self.assertFalse(valid_key.is_expired())
        
        # Key with no expiration
        no_expiry_key, _ = APIKey.generate_key(
            user=self.user,
            name='No Expiry Key'
        )
        
        self.assertFalse(no_expiry_key.is_expired())
    
    def test_scopes(self):
        """Test scope checking."""
        api_key, _ = APIKey.generate_key(
            user=self.user,
            name='Scoped Key',
            scopes=['transactions:read', 'users:read']
        )
        
        # Should have required scopes
        self.assertTrue(api_key.has_scope('transactions:read'))
        self.assertTrue(api_key.has_scope('users:read'))
        
        # Should not have other scopes
        self.assertFalse(api_key.has_scope('transactions:write'))
        self.assertFalse(api_key.has_scope('admin:read'))
        
        # Empty scopes should allow everything
        no_scope_key, _ = APIKey.generate_key(
            user=self.user,
            name='No Scope Key',
            scopes=[]
        )
        
        self.assertTrue(no_scope_key.has_scope('any:scope'))
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        api_key, _ = APIKey.generate_key(
            user=self.user,
            name='Rate Limited Key',
            rate_limit_requests=5,
            rate_limit_window=60
        )
        
        # Clear any existing cache
        cache.clear()
        
        # Should be allowed initially
        is_allowed, count, reset_time = api_key.check_rate_limit()
        self.assertTrue(is_allowed)
        self.assertEqual(count, 0)
        
        # Increment rate limit 5 times
        for i in range(5):
            api_key.increment_rate_limit()
        
        # Should still be allowed (5 requests, limit is 5)
        is_allowed, count, reset_time = api_key.check_rate_limit()
        self.assertTrue(is_allowed)
        self.assertEqual(count, 5)
        
        # One more increment should exceed limit
        api_key.increment_rate_limit()
        is_allowed, count, reset_time = api_key.check_rate_limit()
        self.assertFalse(is_allowed)
        self.assertEqual(count, 6)
        self.assertIsNotNone(reset_time)
    
    def test_usage_tracking(self):
        """Test usage statistics tracking."""
        api_key, _ = APIKey.generate_key(
            user=self.user,
            name='Usage Tracked Key'
        )
        
        initial_count = api_key.usage_count
        initial_last_used = api_key.last_used
        
        # Update usage
        api_key.update_usage()
        api_key.refresh_from_db()
        
        self.assertEqual(api_key.usage_count, initial_count + 1)
        self.assertIsNotNone(api_key.last_used)
        self.assertNotEqual(api_key.last_used, initial_last_used)


class APIKeyAuthenticationTest(APITestCase):
    """Test cases for API key authentication."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.api_key, self.full_key = APIKey.generate_key(
            user=self.user,
            name='Test API Key',
            scopes=['test:read']
        )
    
    def test_valid_api_key_authentication(self):
        """Test authentication with valid API key."""
        from accounts.authentication import APIKeyAuthentication
        from django.http import HttpRequest
        
        auth = APIKeyAuthentication()
        request = HttpRequest()
        request.META['HTTP_AUTHORIZATION'] = f'ApiKey {self.full_key}'
        
        result = auth.authenticate(request)
        self.assertIsNotNone(result)
        
        user, api_key = result
        self.assertEqual(user, self.user)
        self.assertEqual(api_key, self.api_key)
    
    def test_invalid_api_key_format(self):
        """Test authentication with invalid API key format."""
        from accounts.authentication import APIKeyAuthentication
        from django.http import HttpRequest
        from rest_framework.exceptions import AuthenticationFailed
        
        auth = APIKeyAuthentication()
        request = HttpRequest()
        
        # Test various invalid formats
        invalid_keys = [
            'Bearer invalid_key',  # Wrong keyword
            'ApiKey invalid_format',  # Missing colon
            'ApiKey invalid:key:format',  # Too many colons
            'ApiKey wrong_prefix:secret',  # Wrong prefix
        ]
        
        for invalid_key in invalid_keys:
            request.META['HTTP_AUTHORIZATION'] = invalid_key
            with self.assertRaises(AuthenticationFailed):
                auth.authenticate(request)
    
    def test_nonexistent_api_key(self):
        """Test authentication with non-existent API key."""
        from accounts.authentication import APIKeyAuthentication
        from django.http import HttpRequest
        from rest_framework.exceptions import AuthenticationFailed
        
        auth = APIKeyAuthentication()
        request = HttpRequest()
        request.META['HTTP_AUTHORIZATION'] = 'ApiKey ak_nonexistent:fake_secret'
        
        with self.assertRaises(AuthenticationFailed):
            auth.authenticate(request)
    
    def test_inactive_user(self):
        """Test authentication with inactive user."""
        from accounts.authentication import APIKeyAuthentication
        from django.http import HttpRequest
        from rest_framework.exceptions import AuthenticationFailed
        
        # Deactivate user
        self.user.is_active = False
        self.user.save()
        
        auth = APIKeyAuthentication()
        request = HttpRequest()
        request.META['HTTP_AUTHORIZATION'] = f'ApiKey {self.full_key}'
        
        with self.assertRaises(AuthenticationFailed):
            auth.authenticate(request)


class APIKeyPermissionTest(APITestCase):
    """Test cases for API key permissions."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.scoped_key, self.scoped_full_key = APIKey.generate_key(
            user=self.user,
            name='Scoped Key',
            scopes=['transactions:read']
        )
        
        self.admin_key, self.admin_full_key = APIKey.generate_key(
            user=self.user,
            name='Admin Key',
            scopes=['admin:read', 'admin:write']
        )
    
    def test_scope_permission_success(self):
        """Test successful scope permission check."""
        from main.permissions import RequireAPIKeyScope
        from django.http import HttpRequest
        from unittest.mock import Mock
        
        permission = RequireAPIKeyScope()
        permission.required_scope = 'transactions:read'
        
        request = HttpRequest()
        request.user = self.user
        request.api_key = self.scoped_key
        
        view = Mock()
        view.required_scope = 'transactions:read'
        
        self.assertTrue(permission.has_permission(request, view))
    
    def test_scope_permission_failure(self):
        """Test failed scope permission check."""
        from main.permissions import RequireAPIKeyScope, APIKeyScopeException
        from django.http import HttpRequest
        from unittest.mock import Mock
        
        permission = RequireAPIKeyScope()
        
        request = HttpRequest()
        request.user = self.user
        request.api_key = self.scoped_key
        
        view = Mock()
        view.required_scope = 'admin:write'  # Key doesn't have this scope
        
        with self.assertRaises(APIKeyScopeException):
            permission.has_permission(request, view)


if __name__ == '__main__':
    # Run tests
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    if not settings.configured:
        settings.configure(
            DEBUG=True,
            DATABASES={
                'default': {
                    'ENGINE': 'django.db.backends.sqlite3',
                    'NAME': ':memory:',
                }
            },
            INSTALLED_APPS=[
                'django.contrib.auth',
                'django.contrib.contenttypes',
                'rest_framework',
                'main',
                'accounts',
            ],
            SECRET_KEY='test-secret-key',
            USE_TZ=True,
        )
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(['__main__'])
